{% comment %}
  This section is used in the search template to render search results for
  products, articles, and pages.

  https://shopify.dev/docs/storefronts/themes/architecture/templates/search
{% endcomment %}

<h1>{{ 'search.title' | t }}</h1>

<form action="{{ routes.search_url }}" method="get" role="search">
  <input
    type="search"
    size="50"
    name="q"
    value="{{ search.terms | escape }}"
    placeholder="{{ 'search.placeholder' | t }}"
  >
  <button type="submit">{{ 'search.submit' | t }}</button>
</form>

{% if search.performed %}
  {% if search.results_count == 0 %}
    <p>{{ 'search.no_results_html' | t: terms: search.terms }}</p>
  {% else %}
    <p>{{ 'search.results_for_html' | t: terms: search.terms, count: search.results_count }}</p>

    <div class="search-results">
      {% paginate search.results by 20 %}
        {% # Search result items may be an article, a page, or a product. %}
        {% for result in search.results %}
          <div class="search-result">
            {% assign featured_image = result.featured_image | default: result.image %}
            {% if featured_image %}
              {% render 'image', class: 'search-result__image', image: featured_image, url: result.url, width: 400 %}
            {% endif %}
            <div class="search-result__content">
              <p>
                {{ result.title | link_to: result.url }}
                {% if result.price %}
                  {{ result.price | money_with_currency }}
                {% endif %}
              </p>
            </div>
          </div>
        {% endfor %}

        {{ paginate | default_pagination }}
      {% endpaginate %}
    </div>
  {% endif %}
{% endif %}

{% stylesheet %}
  .search-results {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
  .search-results .prev,
  .search-results .page,
  .search-results .next {
    grid-column: 1 / -1;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:general.search",
  "settings": []
}
{% endschema %}
