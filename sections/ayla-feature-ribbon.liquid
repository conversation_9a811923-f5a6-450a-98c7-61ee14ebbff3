{% comment %}
  AYLA Feature Ribbon Section - Individual Feature Pills
  Horizontal scrollable feature pills with icons and text
{% endcomment %}

<ayla-feature-ribbon class="ayla-feature-ribbon">
  {% comment %} Features ribbon - using individual icons for responsive scrolling {% endcomment %}
  <div class="features-container">
    <!-- Monobiałkowe -->
    <div class="feature-pill">
      <div class="feature-icon-circle">
        <!-- No image for Monobiałkowe yet, leaving empty for now -->
        <div style="width: 24px; height: 24px;"></div>
      </div>
      <span class="feature-text">Monobiałkowe</span>
    </div>

    <!-- Bez antybiotyków -->
    <div class="feature-pill">
      <div class="feature-icon-circle">
        <img src="{{ 'Icon-Circle-ribbon.png' | asset_url }}" alt="Bez antybiotyków" class="feature-icon" width="40" height="40">
      </div>
      <span class="feature-text">Bez antybiotyków</span>
    </div>

    <!-- Bez konserwantów -->
    <div class="feature-pill">
      <div class="feature-icon-circle">
        <img src="{{ 'konserwanty-ribbon.png' | asset_url }}" alt="Bez konserwantów" class="feature-icon" width="40" height="40">
      </div>
      <span class="feature-text">Bez konserwantów</span>
    </div>

    <!-- Bez barwników -->
    <div class="feature-pill">
      <div class="feature-icon-circle">
        <img src="{{ 'barwniki-ribbon.png' | asset_url }}" alt="Bez barwników" class="feature-icon" width="40" height="40">
      </div>
      <span class="feature-text">Bez barwników</span>
    </div>

    <!-- Jakość Human-Grade -->
    <div class="feature-pill">
      <div class="feature-icon-circle">
        <img src="{{ 'humangrade-ribbon.png' | asset_url }}" alt="Jakość Human-Grade" class="feature-icon" width="40" height="40">
      </div>
      <span class="feature-text">Jakość Human-Grade</span>
    </div>

    <!-- Hipoalergiczne -->
    <div class="feature-pill">
      <div class="feature-icon-circle">
        <img src="{{ 'hipoalergia-ribbon.png' | asset_url }}" alt="Hipoalergiczne" class="feature-icon" width="40" height="40">
      </div>
      <span class="feature-text">Hipoalergiczne</span>
    </div>

    <!-- Dla wrażliwych -->
    <div class="feature-pill">
      <div class="feature-icon-circle">
        <img src="{{ 'wrazliwy-ribbon.png' | asset_url }}" alt="Dla wrażliwych" class="feature-icon" width="40" height="40">
      </div>
      <span class="feature-text">Dla wrażliwych</span>
    </div>
  </div>
</ayla-feature-ribbon>

{% stylesheet %}
  .ayla-feature-ribbon {
    --primary-p800: #273f34;
    --bezowe-b50: #f9f9f4;
    
    background: transparent;
    padding: 24px 0 80px 0;
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    overflow: hidden;
  }

  .features-container {
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: center;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    padding: 0 24px;
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* Hide scrollbar for all browsers */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    width: 100%;
    max-width: none;
    flex-wrap: nowrap;
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .features-container::-webkit-scrollbar {
    display: none;
  }
  
  .features-container.grabbing {
    cursor: grabbing;
  }

  .feature-pill {
    display: inline-flex;
    padding: 8px 16px 8px 8px;
    align-items: center;
    gap: 12px;
    border-radius: 88px;
    background: var(--bezowe-b50);
    white-space: nowrap;
    flex: 0 0 auto;
    border: 1px solid rgba(39, 63, 52, 0.1);
    width: auto !important;
    min-width: 0;
    max-width: none;
  }
  
  /* Add padding to first and last pills to ensure proper edge spacing */
  .feature-pill:first-child {
    margin-left: 0;
  }
  
  .feature-pill:last-child {
    margin-right: 0;
  }
  

  .feature-icon-circle {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 56px;
    border: 1px solid var(--primary-p800);
    width: 48px;
    height: 48px;
    box-sizing: border-box;
    position: relative;
    background: transparent;
    flex-shrink: 0;
  }

  .feature-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }

  .ayla-feature-ribbon .feature-pill .feature-text {
    color: var(--primary-p800);
    font-family: Jost, -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 18px;
    font-weight: 400;
    line-height: 110%;
    letter-spacing: -0.54px;
    white-space: nowrap;
    width: auto !important;
    min-width: 0 !important;
    max-width: none !important;
    display: inline !important;
    flex: none !important;
    flex-direction: unset !important;
    align-items: unset !important;
    justify-content: unset !important;
    gap: unset !important;
    margin-right: 0 !important;
  }


  /* Responsive Design */
  @media (max-width: 1199px) {
    .features-container {
      justify-content: flex-start;
      padding: 0 24px;
    }
    
    .feature-pill {
      width: auto !important;
      flex: 0 0 auto;
    }
  }
  
  @media (max-width: 768px) {
    .features-container {
      justify-content: flex-start;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      padding: 0 16px;
    }

    .feature-pill {
      scroll-snap-align: start;
      flex: 0 0 auto !important;
      width: auto !important;
      min-width: 0 !important;
      max-width: none !important;
    }
    
    .ayla-feature-ribbon .feature-pill .feature-text {
      font-size: 16px;
      width: auto !important;
      display: inline !important;
    }
  }

  @media (max-width: 480px) {
    .features-container {
      padding: 0 12px;
    }

    .ayla-feature-ribbon .feature-pill .feature-text {
      font-size: 14px;
      width: auto !important;
      display: inline !important;
    }
    
    .feature-icon {
      width: 20px;
      height: 20px;
    }
    
    .feature-icon-circle {
      width: 40px;
      height: 40px;
    }
  }
{% endstylesheet %}

{% javascript %}
  document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('.features-container');
    if (!container) return;
    
    let isDown = false;
    let startX;
    let scrollLeft;
    
    container.addEventListener('mousedown', (e) => {
      isDown = true;
      container.classList.add('grabbing');
      startX = e.pageX - container.offsetLeft;
      scrollLeft = container.scrollLeft;
      e.preventDefault();
    });
    
    container.addEventListener('mouseleave', () => {
      isDown = false;
      container.classList.remove('grabbing');
    });
    
    container.addEventListener('mouseup', () => {
      isDown = false;
      container.classList.remove('grabbing');
    });
    
    container.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - container.offsetLeft;
      const walk = (x - startX) * 2; // Scroll speed multiplier
      container.scrollLeft = scrollLeft - walk;
    });
    
    // Touch support for mobile devices
    let touchStartX = 0;
    let touchScrollLeft = 0;
    
    container.addEventListener('touchstart', (e) => {
      touchStartX = e.touches[0].pageX - container.offsetLeft;
      touchScrollLeft = container.scrollLeft;
    }, { passive: true });
    
    container.addEventListener('touchmove', (e) => {
      const x = e.touches[0].pageX - container.offsetLeft;
      const walk = (x - touchStartX) * 2;
      container.scrollLeft = touchScrollLeft - walk;
    }, { passive: true });
  });
{% endjavascript %}

{% schema %}
{
  "name": "AYLA Feature Ribbon",
  "tag": "section",
  "class": "shopify-section-ayla-feature-ribbon",
  "settings": [
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f9f9f4"
    }
  ],
  "presets": [
    {
      "name": "AYLA Feature Ribbon",
      "category": "Product"
    }
  ]
}
{% endschema %}