{% comment %}
  Enhanced Cart Checkout Section
  Renders a modern cart overview with product details, recommendations, and checkout summary
{% endcomment %}

<style>
  .enhanced-cart {
    --primary-color-700: #2D4F40;
    --beige-100: #F0F0E4;
    --beige-50: #F9F9F4;
    --primary-color-500: #4C7D67;
    --primary-color-0: #FFF;
    font-family: 'Jost', -apple-system, 'Roboto', 'Helvetica', sans-serif;
  }

  .enhanced-cart-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    align-items: flex-start;
    gap: 64px;
    padding: 20px;
  }

  @media (max-width: 768px) {
    .enhanced-cart-container {
      flex-direction: column;
      gap: 32px;
      padding: 16px;
    }
  }

  .cart-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 40px;
    flex: 1 0 0;
  }

  .cart-header {
    color: var(--primary-color-700);
    font-size: 32px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.96px;
    margin-bottom: 32px;
  }

  .cart-items-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 12px;
    border: 1px solid var(--beige-100);
    overflow: hidden;
    min-height: 100px; /* Ensure container has minimum height */
  }

  .cart-item {
    display: flex !important; /* Force display */
    padding: 20px;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
    border-bottom: 1px solid var(--beige-100);
    width: 100%;
  }

  .cart-item:last-child {
    border-bottom: none;
  }

  .product-image-wrapper {
    display: flex;
    width: 140px;
    height: 140px;
    justify-content: center;
    align-items: center;
    border-radius: 7px;
    background: var(--beige-50);
    overflow: hidden;
  }

  .product-image {
    width: 140px;
    height: 140px;
    object-fit: cover;
    border-radius: 7px;
  }

  .product-details {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
  }

  .product-info-header {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
  }

  .product-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 11px;
    flex: 1 0 0;
  }

  .product-name {
    color: var(--primary-color-700);
    font-size: 20px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.6px;
  }

  .product-description {
    color: var(--primary-color-700);
    font-size: 12px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.24px;
  }

  .remove-button {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    cursor: pointer;
  }

  .remove-text {
    color: var(--primary-color-500);
    font-size: 12px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.24px;
  }

  .remove-icon {
    width: 20px;
    height: 20px;
  }

  .price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
  }

  .quantity-selector {
    display: flex;
    height: 36px;
    justify-content: center;
    align-items: center;
    border-radius: 44px;
    border: 1px solid var(--beige-100);
    background: var(--primary-color-0);
  }

  .quantity-button {
    display: flex;
    width: 40px;
    height: 36px;
    justify-content: center;
    align-items: center;
    border: none;
    background: transparent;
    cursor: pointer;
  }

  .quantity-divider {
    width: 1px;
    align-self: stretch;
    background: var(--beige-100);
  }

  .quantity-display {
    display: flex;
    width: 40px;
    padding: 10px 0;
    justify-content: center;
    align-items: center;
    color: var(--primary-color-700);
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 1.4px;
    text-transform: uppercase;
  }

  .price-amount {
    color: var(--primary-color-700);
    font-size: 18px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.54px;
  }

  .cart-divider {
    height: 1px;
    align-self: stretch;
    background: var(--beige-100);
    margin: 32px 0;
  }

  .recommendations-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
  }

  .recommendations-title {
    color: var(--primary-color-700);
    font-size: 24px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.72px;
  }

  .recommendations-grid {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    gap: 20px;
  }

  @media (max-width: 768px) {
    .recommendations-grid {
      flex-direction: column;
      gap: 16px;
    }
  }

  .product-tile {
    width: 236px;
    height: auto !important;
  }

  @media (max-width: 768px) {
    .product-tile {
      width: 100%;
      height: auto !important;
    }
    
    .product-tile-image {
      width: 100%;
      height: 236px;
    }
  }

  .product-tile-image {
    display: flex;
    width: 236px;
    height: 236px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: var(--beige-50);
    overflow: hidden;
    margin-bottom: 24px;
  }

  .product-tile-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .product-tile-details {
    display: block;
    width: 100%;
    margin-bottom: 24px;
  }

  .product-tile-info {
    display: block;
    width: 100%;
    margin-bottom: 11px;
  }

  .product-tile-name {
    color: var(--primary-color-700);
    font-size: 16px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.48px;
  }

  .add-to-cart-button {
    display: flex;
    width: 100%;
    height: 44px;
    padding: 12px 32px;
    justify-content: center;
    align-items: center;
    gap: 12px;
    border-radius: 44px;
    border: 1px solid var(--beige-100);
    background: var(--primary-color-0);
    text-decoration: none;
    cursor: pointer;
    box-sizing: border-box;
    transition: all 0.3s ease;
  }

  .add-to-cart-button:hover {
    background: var(--primary-color-700);
    border-color: var(--primary-color-700);
  }

  .add-to-cart-button:hover .add-to-cart-text,
  .add-to-cart-button:hover .product-tile-price {
    color: var(--primary-color-0);
  }

  .add-to-cart-button:hover svg path {
    stroke: var(--primary-color-0);
  }

  .add-to-cart-content {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .add-to-cart-icon {
    width: 28px;
    height: 28px;
  }

  .add-to-cart-text {
    color: var(--primary-color-700);
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: -0.28px;
  }

  .product-tile-price {
    color: var(--primary-color-700);
    font-size: 16px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.48px;
  }

  /* Cart Summary Styles */
  .cart-summary {
    display: flex;
    width: 378px;
    flex-direction: column;
    align-items: flex-start;
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    .cart-summary {
      width: 100%;
    }
  }

  .summary-row {
    display: flex;
    padding: 8px 0;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
  }

  .summary-label {
    color: var(--primary-color-700);
    font-size: 16px;
    font-weight: 400;
    line-height: 140%;
    opacity: 0.8;
  }

  .summary-value {
    color: var(--primary-color-700);
    font-size: 16px;
    font-weight: 500;
    line-height: 140%;
  }

  .summary-divider {
    height: 1px;
    align-self: stretch;
    background: var(--beige-100);
    margin: 8px 0;
  }

  .total-section {
    display: flex;
    padding: 12px 0;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
  }

  .total-label-group {
    display: flex;
    width: 210px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .total-label {
    color: var(--primary-color-700);
    font-size: 16px;
    font-weight: 700;
    line-height: 140%;
  }

  .vat-label {
    color: var(--primary-color-700);
    font-size: 16px;
    font-weight: 400;
    line-height: 140%;
    opacity: 0.8;
  }

  .checkout-section {
    display: flex;
    padding: 12px 0;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
    border-top: 1px solid var(--beige-100);
  }

  .checkout-button {
    display: flex;
    height: 44px;
    padding: 16px 24px;
    justify-content: center;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    border-radius: 44px;
    background: var(--primary-color-700);
    text-decoration: none;
    border: none;
    cursor: pointer;
  }

  .checkout-button-text {
    color: var(--primary-color-0);
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.64px;
    text-transform: uppercase;
  }

  .delivery-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;
  }

  .delivery-notice {
    display: flex;
    padding: 12px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 12px;
    background: var(--beige-50);
  }

  .delivery-icon {
    display: flex;
    width: 24px;
    height: 24px;
    justify-content: center;
    align-items: center;
  }

  .delivery-text-group {
    display: flex;
    padding-right: 4px;
    align-items: center;
    gap: 12px;
    border-radius: 24px;
  }

  .delivery-text {
    color: var(--primary-color-700);
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
    opacity: 0.8;
  }

  .delivery-amount {
    color: var(--primary-color-700);
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
  }

  .payment-info-image {
    display: block;
    width: 100%;
    height: auto;
    margin-top: 12px;
    border-radius: 12px;
    background: var(--primary-color-0);
    object-fit: contain;
    box-sizing: border-box;
  }

  .discount-notice {
    display: flex;
    padding: 16px 0;
    align-items: center;
    gap: 4px;
    align-self: stretch;
  }

  .discount-text-container {
    display: flex;
    padding: 16px 0;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex: 1 0 0;
    border-top: 1px solid var(--beige-100);
  }

  .discount-text {
    color: var(--primary-color-700);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
  }
</style>

<div class="enhanced-cart" data-cart-count="{{ cart.item_count }}" data-cart-total="{{ cart.total_price }}">
  {% if cart.item_count > 0 %}
    <div class="enhanced-cart-container">
      <!-- Cart Content -->
      <div class="cart-content">
        <h1 class="cart-header">{{ section.settings.cart_title | default: 'Zawartość Twojego koszyka' }}</h1>

        <!-- Cart Items -->
        <div class="cart-items-container">
          {% for item in cart.items %}
            <div class="cart-item" data-item-id="{{ item.id }}">
              <!-- Product Image -->
              <div class="product-image-wrapper">
                {% if item.image %}
                  <img class="product-image" src="{{ item.image | image_url: width: 280 }}" alt="{{ item.image.alt | escape }}">
                {% else %}
                  <div class="product-image" style="background: var(--beige-50);"></div>
                {% endif %}
              </div>

              <!-- Product Details -->
              <div class="product-details">
                <div class="product-info-header">
                  <div class="product-info">
                    <div class="product-name">{{ item.product.title }}</div>
                    {% if item.variant.title != 'Default Title' %}
                      <div class="product-description">{{ item.variant.title }}</div>
                    {% endif %}
                  </div>
                  <a href="{{ item.url_to_remove }}" class="remove-button" aria-label="Remove {{ item.product.title }}">
                    <span class="remove-text">Usuń</span>
                    <svg class="remove-icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4.16699 5.83329H15.8337" stroke="#4C7D67" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M15 5.83337V15C15 15.9209 14.2542 16.6667 13.3333 16.6667H6.66667C5.74583 16.6667 5 15.9209 5 15V5.83337" stroke="#4C7D67" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M12.5 3.12504H7.5" stroke="#4C7D67" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M8.33366 9.16663V13.3333" stroke="#4C7D67" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M11.6667 9.16663V13.3333" stroke="#4C7D67" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </a>
                </div>

                <!-- Price Info -->
                <div class="price-info">
                  <div class="quantity-selector">
                    <button class="quantity-button quantity-minus" data-item-id="{{ item.id }}" data-action="decrease">
                      <svg width="40" height="36" viewBox="0 0 40 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 18H26" stroke="#2D4F40" stroke-linecap="round"/>
                      </svg>
                    </button>
                    <div class="quantity-divider"></div>
                    <div class="quantity-display">{{ item.quantity }}</div>
                    <div class="quantity-divider"></div>
                    <button class="quantity-button quantity-plus" data-item-id="{{ item.id }}" data-action="increase">
                      <svg width="40" height="36" viewBox="0 0 40 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 18H26" stroke="#2D4F40" stroke-linecap="round"/>
                        <path d="M20 12L20 24" stroke="#2D4F40" stroke-linecap="round"/>
                      </svg>
                    </button>
                  </div>
                  <div class="price-amount">{{ item.final_line_price | money }}</div>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>

        <div class="cart-divider"></div>

        <!-- Recommendations Section -->
        {% if section.settings.show_recommendations %}
          <div class="recommendations-section">
            <h2 class="recommendations-title">{{ section.settings.recommendations_title | default: 'Dodaj do zamówienia' }}</h2>
            <div class="recommendations-grid">
              {% for product in collections.all.products limit: 3 %}
                <div class="product-tile">
                  <div class="product-tile-image">
                    {% if product.featured_image %}
                      <img class="product-tile-img" src="{{ product.featured_image | image_url: width: 286 }}" alt="{{ product.featured_image.alt | escape }}">
                    {% endif %}
                  </div>
                  <div class="product-tile-details">
                    <div class="product-tile-info">
                      <div class="product-tile-name">{{ product.title | truncate: 60 }}</div>
                    </div>
                  </div>
                  <button type="button" class="add-to-cart-button" data-product-id="{{ product.selected_or_first_available_variant.id | default: product.variants.first.id }}">
                    <div class="add-to-cart-content">
                      <svg class="add-to-cart-icon" width="29" height="28" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M21.2269 24.0784H7.78688C6.54928 24.0784 5.54688 23.076 5.54688 21.8384V10.0784C5.54688 9.15108 6.29952 8.39844 7.22688 8.39844H21.7869C22.7142 8.39844 23.4669 9.15108 23.4669 10.0784V21.8384C23.4669 23.076 22.4645 24.0784 21.2269 24.0784Z" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M18.9808 12.0847C18.8262 12.0847 18.7008 12.2101 18.7019 12.3647C18.7019 12.5192 18.8273 12.6447 18.9819 12.6447C19.1364 12.6447 19.2619 12.5192 19.2619 12.3647C19.2619 12.2101 19.1364 12.0847 18.9808 12.0847" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M10.0276 12.0847C9.87307 12.0847 9.74763 12.2101 9.74875 12.3647C9.74875 12.5192 9.87419 12.6447 10.0288 12.6447C10.1833 12.6447 10.3088 12.5192 10.3088 12.3647C10.3088 12.2101 10.1833 12.0847 10.0276 12.0847" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M10.0312 8.39406V8.11406V8.11406C10.0312 5.79454 11.9117 3.91406 14.2313 3.91406H14.7913C17.1108 3.91406 18.9913 5.79454 18.9913 8.11406V8.11406V8.39406" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                      <span class="add-to-cart-text">Dodaj</span>
                    </div>
                    <span class="product-tile-price">{{ product.price | money }}</span>
                  </button>
                </div>
              {% endfor %}
            </div>
          </div>
        {% endif %}
      </div>

      <!-- Cart Summary -->
      <div class="cart-summary">
        <!-- Order Summary -->
        <div class="summary-row">
          <span class="summary-label">Suma zamówienia</span>
          <span class="summary-value" data-cart-total="{{ cart.total_price }}">{{ cart.total_price | money }}</span>
        </div>
        
        <div class="summary-row">
          <span class="summary-label">Koszt dostawy</span>
          <span class="summary-value" data-shipping-cost="{{ section.settings.shipping_cost_value | default: 1500 }}">{{ section.settings.shipping_cost | default: '15,00zł' }}</span>
        </div>

        <div class="summary-divider"></div>

        <div class="total-section">
          <div class="total-label-group">
            <span class="total-label">Cena razem</span>
            <span class="vat-label">z VAT</span>
          </div>
          <span class="summary-value">
            {% assign shipping = section.settings.shipping_cost_value | default: 1500 %}
            {{ cart.total_price | plus: shipping | money }}
          </span>
        </div>

        <!-- Checkout Section -->
        <div class="checkout-section">
          <a href="/checkout" class="checkout-button">
            <span class="checkout-button-text">Przejdź do podsumowania</span>
          </a>
        </div>

        <!-- Delivery Info -->
        <div class="delivery-info">
          <div class="delivery-notice">
            <div class="delivery-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M21.0036 8.08734V19.0029C21.0036 20.1079 20.1078 21.0037 19.0028 21.0037H4.99693C3.8919 21.0037 2.9961 20.1079 2.9961 19.0029V8.08734C2.99572 7.81603 3.05052 7.54748 3.15716 7.29801L4.47771 4.20772C4.79346 3.47226 5.51711 2.99573 6.31748 2.99622H17.6822C18.4833 2.99622 19.2072 3.47409 19.522 4.21072L20.8425 7.29801C20.949 7.54753 21.0038 7.81605 21.0036 8.08734Z" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2.99609 7.99838H20.9936" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14.0011 2.99622L15.0015 7.9983V11.2246C14.9768 11.6757 14.5924 12.0221 14.1412 12H9.85938C9.40818 12.0221 9.02379 11.6757 8.99902 11.2246V7.9983L9.99944 2.99622" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M5.99805 18.0025H7.99888" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="delivery-text-group">
              <span class="delivery-text">Do darmowej dostawy brakuje</span>
              <span class="delivery-amount">{{ section.settings.free_shipping_threshold | default: '52,20zł' }}</span>
            </div>
          </div>

          <!-- Payment Info -->
          <img src="{{ 'Payment info-chechout.png' | asset_url }}" alt="Payment methods" class="payment-info-image">
        </div>

        <!-- Discount Code Notice -->
        <div class="discount-notice">
          <div class="discount-text-container">
            <span class="discount-text">Kod rabatowy dodasz w kolejnym kroku</span>
          </div>
        </div>
      </div>
    </div>
  {% else %}
    <div class="enhanced-cart-container">
      <div class="cart-content">
        <h1 class="cart-header">{{ section.settings.empty_cart_title | default: 'Twój koszyk jest pusty' }}</h1>
        <p>{{ section.settings.empty_cart_message | default: 'Dodaj produkty do koszyka, aby kontynuować zakupy.' }}</p>
        <a href="{{ routes.all_products_collection_url }}" class="checkout-button">
          <span class="checkout-button-text">Kontynuuj zakupy</span>
        </a>
      </div>
    </div>
  {% endif %}
</div>

<!-- Cart functionality removed - static display only -->
<script>
  // Static cart display only - no JavaScript functionality
  console.log('Cart page loaded - static display only');
</script>

{% schema %}
{
  "name": "Enhanced Cart Checkout",
  "settings": [
    {
      "type": "text",
      "id": "cart_title",
      "label": "Cart Title",
      "default": "Zawartość Twojego koszyka"
    },
    {
      "type": "text",
      "id": "empty_cart_title",
      "label": "Empty Cart Title",
      "default": "Twój koszyk jest pusty"
    },
    {
      "type": "textarea",
      "id": "empty_cart_message",
      "label": "Empty Cart Message",
      "default": "Dodaj produkty do koszyka, aby kontynuować zakupy."
    },
    {
      "type": "checkbox",
      "id": "show_recommendations",
      "label": "Show Product Recommendations",
      "default": true
    },
    {
      "type": "text",
      "id": "recommendations_title",
      "label": "Recommendations Title",
      "default": "Dodaj do zamówienia"
    },
    {
      "type": "text",
      "id": "shipping_cost",
      "label": "Shipping Cost Display",
      "default": "15,00zł"
    },
    {
      "type": "number",
      "id": "shipping_cost_value",
      "label": "Shipping Cost Value (in cents)",
      "default": 1500
    },
    {
      "type": "text",
      "id": "free_shipping_threshold",
      "label": "Free Shipping Threshold Display",
      "default": "52,20zł"
    }
  ],
  "presets": [
    {
      "name": "Enhanced Cart Checkout"
    }
  ]
}
{% endschema %}
