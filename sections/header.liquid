{% comment %}
  Header section matching the Ayla design
{% endcomment %}

<link rel="preconnect" href="https://fonts.googleapis.com">
<link
  rel="preconnect"
  href="https://fonts.gstatic.com"
  crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Jost:wght@400;500&display=swap" rel="stylesheet">

<!-- Info Navbar -->
<div class="navbar-info-wrapper">
  <div class="navbar-info-content">
    <div class="navbar-info-left">
      <p class="navbar-info-text">DARMOWA DOSTAWA POWYŻEJ 149 ZŁ DO PACZKOMATU</p>
    </div>
    <div class="navbar-info-right">
      <a href="{{ pages['about'].url | default: '#' }}" class="navbar-info-link">O NAS</a>
      <a href="{{ pages['contact'].url | default: '#' }}" class="navbar-info-link">KONTAKT</a>
      <a
        href="https://ayla.care"
        class="navbar-info-link"
        target="_blank"
        rel="noopener">WIĘCEJ O AYLA.CARE</a>
    </div>
  </div>
</div>

<header class="navbar-core">
  <div class="navbar-left">
    <nav class="navbar-menu">
      {% if section.settings.menu.links != blank %}
        {% for link in section.settings.menu.links %}
          <div class="navbar-element">
            <div class="navbar-link-wrapper">
              <a href="{{ link.url }}" class="navbar-link">
                {{ link.title }}
              </a>
              {% if link.links != blank %}
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 16 16"
                  fill="none"
                  style="margin-left: 4px;">
                  <path
                    d="M4 6L8 10L12 6"
                    stroke="currentColor"
                    stroke-width="1.5" />
                </svg>
              {% endif %}
            </div>
          </div>
        {% endfor %}
      {% else %}
        {% comment %} Fallback navigation items {% endcomment %}
        <div class="navbar-element">
          <div class="navbar-link-wrapper">
            <a href="{{ routes.all_products_collection_url }}" class="navbar-link">wszystkie</a>
          </div>
        </div>
        <div class="navbar-element">
          <div class="navbar-link-wrapper">
            <a href="/collections/for-cat" class="navbar-link">dla kota</a>
            <svg
              width="12"
              height="12"
              viewBox="0 0 16 16"
              fill="none"
              style="margin-left: 4px;">
              <path
                d="M4 6L8 10L12 6"
                stroke="currentColor"
                stroke-width="1.5" />
            </svg>
          </div>
        </div>
        <div class="navbar-element">
          <div class="navbar-link-wrapper">
            <a href="/collections/for-dog" class="navbar-link">Dla psa</a>
            <svg
              width="12"
              height="12"
              viewBox="0 0 16 16"
              fill="none"
              style="margin-left: 4px;">
              <path
                d="M4 6L8 10L12 6"
                stroke="currentColor"
                stroke-width="1.5" />
            </svg>
          </div>
        </div>
        <div class="navbar-element">
          <div class="navbar-link-wrapper">
            <a href="/collections/ayla-delight" class="navbar-link">delight</a>
          </div>
        </div>
        <div class="navbar-element">
          <div class="navbar-link-wrapper">
            <a href="/collections/ayla-help" class="navbar-link">help</a>
          </div>
        </div>
        <div class="navbar-element">
          <div class="navbar-link-wrapper">
            <a href="/collections/ayla-rescue" class="navbar-link">rescue</a>
          </div>
        </div>
      {% endif %}
    </nav>
  </div>

  <div class="navbar-center">
    <a href="{{ routes.root_url }}" class="navbar-logo">
      <img
        src="{{ 'ayla-logo.svg' | asset_url }}"
        alt="{{ shop.name }}"
        width="67"
        height="40">
    </a>
  </div>

  <div class="navbar-right">
    <div class="navbar-icons">
      <div class="navbar-icon-item navbar-search">
        <button class="navbar-icon-link search-toggle" data-search-toggle>
          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none">
            <circle
              cx="13"
              cy="13"
              r="8"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round"
              fill="none" />
            <path
              d="M23.5 23.5L18.5 18.5"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          <span class="navbar-icon-text">Szukaj</span>
        </button>
      </div>

      {% if shop.customer_accounts_enabled %}
        <div class="navbar-icon-item navbar-account">
          <a href="{{ routes.account_url }}" class="navbar-icon-link">
            <svg
              width="28"
              height="28"
              viewBox="0 0 28 28"
              fill="none">
              <circle
                cx="14"
                cy="14"
                r="10.5"
                stroke="currentColor"
                stroke-width="1"
                fill="none" />
              <circle
                cx="14"
                cy="11.5"
                r="3"
                stroke="currentColor"
                stroke-width="1"
                fill="none" />
              <path
                d="M7.5 21.5C7.5 18.5 10 16 14 16C18 16 20.5 18.5 20.5 21.5"
                stroke="currentColor"
                stroke-width="1"
                stroke-linecap="round" />
            </svg>
            <span class="navbar-icon-text">Konto</span>
          </a>
        </div>
      {% endif %}

      <div class="navbar-icon-item navbar-cart">
        <a href="{{ routes.cart_url }}" class="navbar-icon-link">
          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none">
            <path
              d="M6 8H22C22.55 8 23 8.45 23 9V20C23 21.1 22.1 22 21 22H7C5.9 22 5 21.1 5 20V9C5 8.45 5.45 8 6 8Z"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round"
              fill="none" />
            <path
              d="M9.5 8V7.5C9.5 5 11.5 3 14 3C16.5 3 18.5 5 18.5 7.5V8"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round" />
            <circle
              cx="9.5"
              cy="12"
              r="0.5"
              fill="currentColor" />
            <circle
              cx="18.5"
              cy="12"
              r="0.5"
              fill="currentColor" />
          </svg>
          <span class="navbar-icon-text">Koszyk</span>
          {% if cart.item_count > 0 %}
            <span class="cart-count" data-cart-count>{{ cart.item_count }}</span>
          {% endif %}
        </a>
      </div>
    </div>

    <div class="navbar-divider"></div>

    {% if section.settings.show_language_selector %}
      <div class="navbar-element navbar-language">
        <div class="navbar-link-wrapper">
          <span class="navbar-link navbar-language-text">
            {{ request.locale.iso_code | upcase }}
          </span>
          {{ 'icon-chevron-down.svg' | inline_asset_content }}
        </div>
      </div>
    {% endif %}
  </div>

  <div class="navbar-mobile-menu">
    <button
      class="mobile-menu-toggle"
      aria-label="Toggle mobile menu"
      data-mobile-menu-toggle>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>
  </div>
</header>

<!-- Mobile Menu Overlay -->
<div class="mobile-menu-overlay" data-mobile-menu-overlay>
  <div class="mobile-menu-content">
    <!-- Header with close button and logo -->
    <div class="mobile-menu-header">
      <button
        class="mobile-menu-close"
        data-mobile-menu-close
        aria-label="Close mobile menu">
        <div class="close-icon">
          <div class="close-line close-line-1"></div>
          <div class="close-line close-line-2"></div>
        </div>
      </button>
      <div class="mobile-menu-logo">
        <img
          src="{{ 'ayla-logo.svg' | asset_url }}"
          alt="{{ shop.name }}"
          width="67"
          height="40">
      </div>
      <div class="mobile-menu-cart">
        <a href="{{ routes.cart_url }}" class="mobile-cart-link">
          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none">
            <path
              d="M6 8H22C22.55 8 23 8.45 23 9V20C23 21.1 22.1 22 21 22H7C5.9 22 5 21.1 5 20V9C5 8.45 5.45 8 6 8Z"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round"
              fill="none" />
            <path
              d="M9.5 8V7.5C9.5 5 11.5 3 14 3C16.5 3 18.5 5 18.5 7.5V8"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round"
              stroke-linejoin="round" />
            <circle
              cx="9.5"
              cy="12"
              r="0.5"
              fill="currentColor" />
            <circle
              cx="18.5"
              cy="12"
              r="0.5"
              fill="currentColor" />
          </svg>
          <span class="mobile-cart-text">Koszyk</span>
        </a>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="mobile-menu-search">
      <div class="search-input-wrapper">
        <svg
          width="28"
          height="28"
          viewBox="0 0 28 28"
          fill="none"
          class="search-icon">
          <circle
            cx="13"
            cy="13"
            r="8"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round"
            fill="none" />
          <path
            d="M23.5 23.5L18.5 18.5"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round" />
        </svg>
        <span class="search-placeholder">Szukaj</span>
      </div>
    </div>

    <!-- Navigation Sections -->
    <div class="mobile-menu-navigation">
      <!-- O nas Section -->
      <div class="mobile-menu-section">
        <div class="section-header">O nas</div>
        <div class="section-items">
          <div class="accordion-item">
            <button class="section-item accordion-trigger" data-accordion-trigger>
              <span class="item-text">Dla Kota</span>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                class="item-arrow">
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </button>
            <div class="accordion-content" data-accordion-content>
              <a href="/collections/for-cat-duck" class="sub-item">
                <span class="sub-item-text">Z kaczki</span>
              </a>
              <a href="/collections/for-cat-chicken" class="sub-item">
                <span class="sub-item-text">Z kurczaka</span>
              </a>
              <a href="/collections/for-cat-turkey" class="sub-item">
                <span class="sub-item-text">Z indyka</span>
              </a>
              <a href="/collections/for-cat-meat" class="sub-item">
                <span class="sub-item-text">Z mięsa</span>
              </a>
              <a href="/collections/for-cat-offal" class="sub-item">
                <span class="sub-item-text">Z podrobów</span>
              </a>
            </div>
          </div>
          <div class="accordion-item">
            <button class="section-item accordion-trigger" data-accordion-trigger>
              <span class="item-text">Dla Psa</span>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                class="item-arrow">
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </button>
            <div class="accordion-content" data-accordion-content>
              <a href="/collections/for-dog-duck" class="sub-item">
                <span class="sub-item-text">Z kaczki</span>
              </a>
              <a href="/collections/for-dog-chicken" class="sub-item">
                <span class="sub-item-text">Z kurczaka</span>
              </a>
              <a href="/collections/for-dog-turkey" class="sub-item">
                <span class="sub-item-text">Z indyka</span>
              </a>
              <a href="/collections/for-dog-meat" class="sub-item">
                <span class="sub-item-text">Z mięsa</span>
              </a>
              <a href="/collections/for-dog-offal" class="sub-item">
                <span class="sub-item-text">Z podrobów</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Divider -->
      <div class="mobile-menu-divider"></div>

      <!-- Linie Section -->
      <div class="mobile-menu-section">
        <div class="section-header">Linie</div>
        <div class="section-items">
          <div class="accordion-item">
            <button class="section-item accordion-trigger" data-accordion-trigger>
              <span class="item-text">delight</span>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                class="item-arrow">
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </button>
            <div class="accordion-content" data-accordion-content>
              <a href="/collections/ayla-delight-duck" class="sub-item">
                <span class="sub-item-text">Z kaczki</span>
              </a>
              <a href="/collections/ayla-delight-chicken" class="sub-item">
                <span class="sub-item-text">Z kurczaka</span>
              </a>
              <a href="/collections/ayla-delight-turkey" class="sub-item">
                <span class="sub-item-text">Z indyka</span>
              </a>
              <a href="/collections/ayla-delight-meat" class="sub-item">
                <span class="sub-item-text">Z mięsa</span>
              </a>
              <a href="/collections/ayla-delight-offal" class="sub-item">
                <span class="sub-item-text">Z podrobów</span>
              </a>
            </div>
          </div>
          <div class="accordion-item">
            <button class="section-item accordion-trigger" data-accordion-trigger>
              <span class="item-text">Help</span>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                class="item-arrow">
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </button>
            <div class="accordion-content" data-accordion-content>
              <a href="/collections/ayla-help-senior" class="sub-item">
                <span class="sub-item-text">Dla seniorów</span>
              </a>
              <a href="/collections/ayla-help-special" class="sub-item">
                <span class="sub-item-text">Specjalne potrzeby</span>
              </a>
              <a href="/collections/ayla-help-recovery" class="sub-item">
                <span class="sub-item-text">W trakcie rekonwalescencji</span>
              </a>
              <a href="/collections/ayla-help-sensitive" class="sub-item">
                <span class="sub-item-text">Wrażliwy układ pokarmowy</span>
              </a>
            </div>
          </div>
          <div class="accordion-item">
            <button class="section-item accordion-trigger" data-accordion-trigger>
              <span class="item-text">Rescue</span>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                class="item-arrow">
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </button>
            <div class="accordion-content" data-accordion-content>
              <a href="/collections/ayla-rescue-emergency" class="sub-item">
                <span class="sub-item-text">Sytuacje awaryjne</span>
              </a>
              <a href="/collections/ayla-rescue-recovery" class="sub-item">
                <span class="sub-item-text">Po operacji</span>
              </a>
              <a href="/collections/ayla-rescue-illness" class="sub-item">
                <span class="sub-item-text">Podczas choroby</span>
              </a>
              <a href="/collections/ayla-rescue-stress" class="sub-item">
                <span class="sub-item-text">Stres i niepokój</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Divider -->
      <div class="mobile-menu-divider"></div>

      <!-- Account Section -->
      <div class="mobile-menu-account">
        <a href="{{ routes.account_url }}" class="account-link">
          <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            class="account-icon">
            <circle
              cx="14"
              cy="14"
              r="10.5"
              stroke="currentColor"
              stroke-width="1"
              fill="none" />
            <circle
              cx="14"
              cy="11.5"
              r="3"
              stroke="currentColor"
              stroke-width="1"
              fill="none" />
            <path
              d="M7.5 21.5C7.5 18.5 10 16 14 16C18 16 20.5 18.5 20.5 21.5"
              stroke="currentColor"
              stroke-width="1"
              stroke-linecap="round" />
          </svg>
          <span class="account-text">Konto</span>
        </a>
        <div class="language-selector">
          <span class="language-text">PL</span>
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            class="language-arrow">
            <path
              d="M4 6L8 10L12 6"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </div>
      </div>

      <!-- Divider -->
      <div class="mobile-menu-divider"></div>

      <!-- Bottom Navigation Links -->
      <div class="mobile-menu-bottom">
        <a href="{{ pages['about'].url | default: '#' }}" class="bottom-link">O nas</a>
        <a href="{{ pages['contact'].url | default: '#' }}" class="bottom-link">Kontakt</a>
        <a
          href="https://ayla.care"
          class="bottom-link"
          target="_blank"
          rel="noopener">więcej o Ayla.care</a>
      </div>
    </div>
  </div>
</div>

<!-- Search Bar Overlay -->
<div class="search-overlay" data-search-overlay>
  <div class="search-overlay-background" data-search-background></div>
  <div class="search-bar-container">
    <div class="search-bar-content">
      <div class="search-icon-container">
        <svg
          width="28"
          height="28"
          viewBox="0 0 28 28"
          fill="none"
          class="search-bar-icon">
          <circle
            cx="13"
            cy="13"
            r="8"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round"
            fill="none" />
          <path
            d="M23.5 23.5L18.5 18.5"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round" />
        </svg>
      </div>
      <div class="search-placeholder-text">
        <p>Szukaj produktu, linii lub typu mięsa...</p>
      </div>
    </div>
  </div>
</div>

{% stylesheet %}
  :root  {
    --Primary-Color-P700---Main: #2D4F40;
    --Primary-Color-P100: #E0EBE2;
    --Primary-Color-P0: white;
    --Bezowe-B100: #F0F0E4;
  }

  /* Info Navbar Styles */
  .navbar-info-wrapper {
    position: relative;
    background-color: var(--Primary-Color-P700---Main, #2D4F40);
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
  }

  /* Create full-width background using pseudo-element */
  .navbar-info-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100vw;
    height: 100%;
    background-color: var(--Primary-Color-P700---Main, #2D4F40);
    z-index: -1;
  }

  .navbar-info-content {
    display: flex;
    width: 100%;
    padding: 12px 40px;
    /* Same as navbar-core */
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    position: relative;
  }

  .navbar-info-left {
    display: flex;
    align-items: center;
  }

  .navbar-info-text {
    color: var(--Primary-Color-P0, white);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    line-height: 1.4;
    margin: 0;
    white-space: nowrap;
  }

  .navbar-info-right {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .navbar-info-link {
    color: var(--Primary-Color-P0, white);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    line-height: 1.4;
    text-decoration: none;
    white-space: nowrap;
    transition: opacity 0.2s ease;
  }

  .navbar-info-link:hover {
    opacity: 0.8;
  }

  /* Responsive styles for info navbar - matching navbar-core */

  /* Medium Desktop (1280px and above) */@media (min-width: 1280px) {
    .navbar-info-content {
      padding: 12px 60px;
      /* Same horizontal padding as navbar-core */
    }
  }

  /* Large Desktop (1440px and above) */
  @media (min-width: 1440px) {
    .navbar-info-content {
      padding: 12px 120px;
      /* Same horizontal padding as navbar-core */
    }
  }

  @media (max-width: 1090px) and (min-width: 768px) {
    .navbar-info-content {
      padding: 12px 32px;
    }

    .navbar-info-text {
      font-size: 11px;
    }

    .navbar-info-link {
      font-size: 11px;
    }

    .navbar-info-right {
      gap: 20px;
    }
  }

  @media (max-width: 767px) {
    .navbar-info-content {
      padding: 10px 16px;
      flex-direction: column;
      gap: 8px;
    }

    .navbar-info-left {
      width: 100%;
      max-width: 100%;
      justify-content: center;
    }

    .navbar-info-right {
      width: 100%;
      max-width: 100%;
      justify-content: center;
    }

    .navbar-info-text {
      font-size: 10px;
      letter-spacing: 1px;
      text-align: center;
    }

    .navbar-info-link {
      font-size: 10px;
      letter-spacing: 1px;
    }
  }

  .navbar-core {
    display: flex;
    width: 100%;
    height: 72px;
    padding: 0 40px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--Bezowe-B100, #F0F0E4);
    position: relative;
    background-color: var(--Primary-Color-P0, white);
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
  }

  .navbar-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 40px;
    position: relative;
    flex: 1 1 auto;
    max-width: 45%;
  }

  .navbar-menu {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    position: relative;
  }

  .navbar-element {
    display: inline-flex;
    height: 72px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 28px 0;
    position: relative;
  }

  .navbar-link-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: start;
    position: relative;
  }

  .navbar-link {
    color: var(--Primary-Color-P700---Main, #2D4F40);
    font-size: 13px;
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 18.2px;
    letter-spacing: 1.3px;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    /* Prevent text wrapping */
  }

  /* Desktop View (1091px and above) */
  @media (min-width: 1091px) {
    .navbar-core {
      padding: 0 40px;
    }

    /* Navigation items should not wrap */
    .navbar-link {
      white-space: nowrap;
    }

    /* Ensure logo doesn't overlap navigation */
    .navbar-center {
      z-index: 0;
      pointer-events: none;
    }

    .navbar-center a {
      pointer-events: auto;
    }

    /* Add minimum gap between left navigation and center logo */
    .navbar-left {
      padding-right: 20px;
    }

    .navbar-right {
      padding-left: 20px;
    }
  }

  /* Medium Desktop (1280px and above) */
  @media (min-width: 1280px) {
    .navbar-core {
      padding: 0 60px;
    }

    .navbar-left {
      padding-right: 40px;
    }

    .navbar-right {
      padding-left: 40px;
    }
  }

  /* Large Desktop (1440px and above) */
  @media (min-width: 1440px) {
    .navbar-core {
      padding: 0 120px;
    }

    .navbar-left {
      padding-right: 60px;
    }

    .navbar-right {
      padding-left: 60px;
    }
  }

  .navbar-link:hover {
    opacity: 0.8;
  }

  .navbar-link-wrapper svg {
    width: 16px;
    height: 16px;
    position: relative;
    stroke: #2D4F40;
  }

  .navbar-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .navbar-logo {
    display: block;
    text-decoration: none;
  }

  .navbar-logo img {
    width: 67px;
    height: 40px;
    display: block;
  }

  .navbar-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
    position: relative;
    flex: 1 1 auto;
    max-width: 45%;
  }

  .navbar-icons {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
    position: relative;
  }

  .navbar-icon-item {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
  }

  .navbar-icon-link {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    text-decoration: none;
    color: #2D4F40;
  }

  .navbar-icon-link:hover {
    opacity: 0.8;
  }

  .navbar-icon-link svg {
    width: 28px;
    height: 28px;
    position: relative;
    stroke: #2D4F40;
  }

  .navbar-icon-text {
    color: var(--Primary-Color-P700---Main, #2D4F40);
    font-size: 13px;
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-weight: 400;
    line-height: 18.2px;
    text-align: center;
    position: relative;
  }

  .cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #2D4F40;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 500;
  }

  .navbar-divider {
    width: 1px;
    height: 24px;
    position: relative;
    background-color: var(--Primary-Color-P100, #E0EBE2);
  }

  .navbar-language {
    display: flex;
    height: 72px;
    padding: 28px 0;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    position: relative;
  }

  .navbar-language-text {
    color: var(--Primary-Color-P700---Main, #2D4F40);
    font-size: 14px;
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 19.6px;
    letter-spacing: 1.4px;
    text-align: center;
    position: relative;
  }

  .navbar-mobile-menu {
    display: none;
  }

  .mobile-menu-toggle {
    width: 44px;
    height: 44px;
    padding: 2px;
    border-radius: 44px;
    border: 1px solid var(--Primary-Color-P100, #E0EBE2);
    background: transparent;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 6px;
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .mobile-menu-toggle:hover {
    background: var(--Primary-Color-P100, #E0EBE2);
  }

  .mobile-menu-toggle .hamburger-line {
    width: 18px;
    height: 1px;
    background: var(--Primary-Color-P700---Main, #2D4F40);
    border-radius: 1px;
    transition: all 0.3s ease;
  }

  /* Tablet View (768px - 1090px) */
  @media (max-width: 1090px) and (min-width: 768px) {
    .navbar-core {
      padding: 0 32px;
      height: 72px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    /* Hide desktop navigation */
    .navbar-left {
      display: none;
    }

    /* Show hamburger menu on the left */
    .navbar-mobile-menu {
      display: flex;
      align-items: center;
      order: 1;
      flex: 0 0 auto;
    }

    /* Center logo absolutely */
    .navbar-center {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
    }

    /* Right side - all three icons visible */
    .navbar-right {
      padding-left: 0;
      max-width: none;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 16px;
      order: 3;
      flex: 0 0 auto;
    }

    .navbar-icons {
      display: flex;
      align-items: center;
      gap: 24px;
      /* Proper spacing between icons */
    }

    /* Show all three icons on tablet */
    .navbar-search,
    .navbar-account,
    .navbar-cart {
      display: flex !important;
      align-items: center;
    }

    /* Icon links without text on tablet */
    .navbar-icon-link {
      display: flex;
      align-items: center;
      gap: 0;
      position: relative;
    }

    .navbar-icon-link svg {
      display: block;
      width: 24px;
      height: 24px;
      stroke: var(--Primary-Color-P700---Main, #2D4F40);
    }

    /* Hide icon text on tablet */
    .navbar-icon-text {
      display: none !important;
    }

    /* Ensure cart count stays visible */
    .cart-count {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #2D4F40;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 500;
    }

    /* Hide divider and language selector */
    .navbar-divider,
    .navbar-language {
      display: none;
    }
  }

  /* Mobile View (max-width: 767px) */
  @media (max-width: 767px) {
    .navbar-core {
      padding: 0 16px;
      height: 64px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    /* Hide desktop navigation */
    .navbar-left {
      display: none;
    }

    /* Show hamburger menu on the left */
    .navbar-mobile-menu {
      display: flex;
      align-items: center;
      order: 1;
      flex: 0 0 auto;
    }

    /* Logo positioned after hamburger */
    .navbar-center {
      position: static;
      transform: none;
      order: 2;
      flex: 1 1 auto;
      display: flex;
      justify-content: center;
      padding: 0 16px;
    }

    /* Right side - only cart icon */
    .navbar-right {
      padding-left: 0;
      max-width: none;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      order: 3;
      flex: 0 0 auto;
    }

    .navbar-icons {
      display: flex;
      align-items: center;
    }

    /* Hide search and account icons on mobile */
    .navbar-search,
    .navbar-account {
      display: none !important;
    }

    /* Show only cart icon */
    .navbar-cart {
      display: flex !important;
      align-items: center;
    }

    /* Cart icon without text */
    .navbar-icon-link {
      display: flex;
      align-items: center;
      gap: 0;
      position: relative;
    }

    .navbar-icon-link svg {
      display: block;
      width: 24px;
      height: 24px;
      stroke: var(--Primary-Color-P700---Main, #2D4F40);
    }

    /* Hide icon text on mobile */
    .navbar-icon-text {
      display: none !important;
    }

    /* Cart count stays visible */
    .cart-count {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #2D4F40;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 500;
    }

    /* Hide divider and language selector */
    .navbar-divider,
    .navbar-language {
      display: none;
    }

    /* Adjust logo size for mobile */
    .navbar-logo img {
      width: 60px;
      height: 36px;
    }

    /* Adjust hamburger button for mobile */
    .mobile-menu-toggle {
      width: 40px;
      height: 40px;
    }
  }

  /* Mobile Menu Overlay Styles */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    z-index: 9999;
    display: none;
    overflow-y: auto;
  }

  .mobile-menu-overlay.active {
    display: block;
  }

  .mobile-menu-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 100vh;
    position: relative;
  }

  /* Mobile Menu Header */
  .mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 72px;
    border-bottom: 1px solid var(--Bezowe-B100, #F0F0E4);
    background-color: white;
    position: relative;
  }

  .mobile-menu-close {
    width: 44px;
    height: 44px;
    border-radius: 44px;
    border: 1px solid var(--Primary-Color-P100, #E0EBE2);
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .mobile-menu-close:hover {
    background: var(--Primary-Color-P100, #E0EBE2);
  }

  .close-icon {
    position: relative;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-line {
    position: absolute;
    width: 18px;
    height: 1px;
    background: var(--Primary-Color-P700---Main, #2D4F40);
    border-radius: 1px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .close-line-1 {
    transform: translate(-50%, -50%) rotate(45deg);
  }

  .close-line-2 {
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  .mobile-menu-logo {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }

  .mobile-menu-logo img {
    width: 67px;
    height: 40px;
  }

  .mobile-menu-cart {
    display: flex;
    align-items: center;
  }

  .mobile-cart-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  .mobile-cart-link svg {
    width: 28px;
    height: 28px;
    stroke: var(--Primary-Color-P700---Main, #2D4F40);
  }

  .mobile-cart-text {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 18.2px;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  /* Search Bar */
  .mobile-menu-search {
    padding: 24px 16px 16px;
  }

  .search-input-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid var(--Bezowe-B100, #F0F0E4);
    border-radius: 244px;
    background: white;
  }

  .search-icon {
    width: 28px;
    height: 28px;
    stroke: var(--Primary-Color-P700---Main, #2D4F40);
    flex-shrink: 0;
  }

  .search-placeholder {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 18.2px;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  /* Navigation Sections */
  .mobile-menu-navigation {
    flex: 1;
    padding: 0 16px;
    text-align: left;
  }

  .mobile-menu-section {
    margin-bottom: 20px;
    padding-left: 0;
    margin-left: 0;
  }

  .section-header {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 19.6px;
    letter-spacing: 1.4px;
    color: #6a9981;
    margin-bottom: 8px;
    padding: 0;
    text-align: left;
    margin-left: 0;
    text-indent: 0;
    display: block;
    width: 100%;
  }

  .section-items {
    display: flex;
    flex-direction: column;
    gap: 0;
    align-items: flex-start;
  }

  .section-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.2s ease;
    margin-bottom: 4px;
    margin-left: 0;
    border-bottom: none;
  }

  .section-item:hover {
    background-color: rgba(224, 235, 226, 0.1);
  }

  /* Accordion Styles */
  .accordion-item {
    margin-bottom: 4px;
    border-bottom: none !important;
  }

  .mobile-menu-navigation .accordion-item {
    border-bottom: none !important;
  }

  .section-items .accordion-item {
    border-bottom: none !important;
  }

  .accordion-trigger {
    width: 100%;
    background: none;
    border: none;
    cursor: pointer;
    text-align: left;
  }

  .accordion-trigger:hover {
    background-color: rgba(224, 235, 226, 0.1);
  }

  .accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding-left: 16px;
  }

  .accordion-content.active {
    max-height: 500px;
  }

  .sub-item {
    display: block;
    padding: 8px 0;
    text-decoration: none;
    color: var(--Primary-Color-P700---Main, #2D4F40);
    transition: opacity 0.2s ease;
  }

  .sub-item:hover {
    opacity: 0.8;
  }

  .sub-item-text {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 22.4px;
    letter-spacing: -0.32px;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  .item-arrow {
    transition: transform 0.3s ease;
    transform: rotate(90deg);
  }

  .accordion-trigger.active .item-arrow {
    transform: rotate(270deg);
  }

  .item-text {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 22.4px;
    letter-spacing: 1.6px;
    text-transform: uppercase;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  .item-arrow {
    width: 24px;
    height: 24px;
    stroke: var(--Primary-Color-P700---Main, #2D4F40);
    flex-shrink: 0;
  }

  /* Divider */
  .mobile-menu-divider {
    height: 1px;
    background-color: var(--Primary-Color-P100, #E0EBE2);
    margin: 16px 0;
  }

  /* Account Section */
  .mobile-menu-account {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
  }

  .account-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
  }

  .account-icon {
    width: 28px;
    height: 28px;
    stroke: var(--Primary-Color-P700---Main, #2D4F40);
    fill: none;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  .account-icon circle {
    stroke: var(--Primary-Color-P700---Main, #2D4F40) !important;
    fill: none !important;
  }

  .account-icon path {
    stroke: var(--Primary-Color-P700---Main, #2D4F40) !important;
    fill: none !important;
  }

  .account-text {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 18.2px;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  .language-selector {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .language-text {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 19.6px;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    color: var(--Primary-Color-P700---Main, #2D4F40);
  }

  .language-arrow {
    width: 16px;
    height: 16px;
    stroke: var(--Primary-Color-P700---Main, #2D4F40);
  }

  /* Bottom Navigation Links */
  .mobile-menu-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    padding: 16px 0;
    background-color: white;
    flex-wrap: wrap;
  }

  .bottom-link {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 18.2px;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    color: var(--Primary-Color-P700---Main, #2D4F40);
    text-decoration: none;
    white-space: nowrap;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
  }

  .bottom-link:hover {
    opacity: 0.8;
  }

  /* Search Overlay Styles */
  .search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .search-overlay.active {
    display: block;
    opacity: 1;
  }

  .search-bar-container {
    position: fixed;
    top: 96px;
    /* Height of info navbar + main navbar */
    left: 0;
    right: 0;
    background-color: white;
    border-bottom: 1px solid var(--Bezowe-B100, #F0F0E4);
    padding: 0 40px;
    box-sizing: border-box;
    z-index: 1001;
  }

  .search-overlay-background {
    position: fixed;
    top: 96px;
    /* Start below the search bar */
    left: 0;
    width: 100%;
    height: calc(100vh - 96px);
    /* Height from search bar to bottom */
    background-color: rgba(0, 0, 0, 0.05);
    z-index: 999;
  }

  .search-bar-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
  }

  .search-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }

  .search-bar-icon {
    width: 28px;
    height: 28px;
    stroke: var(--Primary-Color-P700---Main, #2D4F40);
    opacity: 0.7;
  }

  .search-placeholder-text {
    flex: 1;
  }

  .search-placeholder-text p {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.4;
    color: var(--Primary-Color-P700---Main, #2D4F40);
    opacity: 0.7;
    margin: 0;
    letter-spacing: 1.6px;
    text-transform: uppercase;
  }

  /* Search toggle button styles */
  .search-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    margin: 0;
  }

  .search-toggle:hover {
    opacity: 0.8;
  }

  /* Responsive adjustments for search overlay */
  @media (min-width: 1280px) {
    .search-bar-container {
      padding: 0 60px;
    }
  }

  @media (min-width: 1440px) {
    .search-bar-container {
      padding: 0 120px;
    }
  }

  @media (max-width: 1090px) and (min-width: 768px) {
    .search-bar-container {
      top: 88px;
      /* Height of info navbar + tablet navbar */
      padding: 0 32px;
    }

    .search-overlay-background {
      top: 88px;
      /* Start below the search bar on tablet */
      height: calc(100vh - 88px);
      /* Height from search bar to bottom on tablet */
    }
  }

  @media (max-width: 767px) {
    .search-bar-container {
      top: 88px;
      /* Height of info navbar + mobile navbar */
      padding: 0 16px;
    }

    .search-overlay-background {
      top: 88px;
      /* Start below the search bar on mobile */
      height: calc(100vh - 88px);
      /* Height from search bar to bottom on mobile */
    }

    .search-bar-content {
      padding: 16px 0;
    }

    .search-placeholder-text p {
      font-size: 14px;
      letter-spacing: 1.4px;
    }
  }

  /* Responsive adjustments for mobile menu */
  @media (max-width: 767px) {
    .mobile-menu-header {
      padding: 0 16px;
    }

    .mobile-menu-search {
      padding: 16px;
    }

    .mobile-menu-navigation {
      padding: 0 16px;
    }

    .mobile-menu-bottom {
      gap: 12px;
      padding: 16px 0;
    }

    .bottom-link {
      font-size: 11px;
      letter-spacing: 1px;
    }
  }
{% endstylesheet %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuToggle = document.querySelector('[data-mobile-menu-toggle]');
  const mobileMenuOverlay = document.querySelector('[data-mobile-menu-overlay]');
  const mobileMenuClose = document.querySelector('[data-mobile-menu-close]');
  const body = document.body;

  // Open mobile menu
  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener('click', function() {
      mobileMenuOverlay.classList.add('active');
      body.style.overflow = 'hidden'; // Prevent background scrolling
    });
  }

  // Close mobile menu
  if (mobileMenuClose) {
    mobileMenuClose.addEventListener('click', function() {
      mobileMenuOverlay.classList.remove('active');
      body.style.overflow = ''; // Restore scrolling
    });
  }

  // Close mobile menu when clicking outside
  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function(e) {
      if (e.target === mobileMenuOverlay) {
        mobileMenuOverlay.classList.remove('active');
        body.style.overflow = ''; // Restore scrolling
      }
    });
  }

  // Close mobile menu on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && mobileMenuOverlay.classList.contains('active')) {
      mobileMenuOverlay.classList.remove('active');
      body.style.overflow = ''; // Restore scrolling
    }
  });

  // Close mobile menu when clicking on navigation links
  const mobileMenuLinks = mobileMenuOverlay.querySelectorAll('a');
  mobileMenuLinks.forEach(link => {
    link.addEventListener('click', function() {
      mobileMenuOverlay.classList.remove('active');
      body.style.overflow = ''; // Restore scrolling
    });
  });

  // Accordion functionality
  const accordionTriggers = document.querySelectorAll('[data-accordion-trigger]');
  accordionTriggers.forEach(trigger => {
    trigger.addEventListener('click', function() {
      const content = this.nextElementSibling;
      const isActive = this.classList.contains('active');
      
      // Close all other accordions
      accordionTriggers.forEach(otherTrigger => {
        if (otherTrigger !== this) {
          otherTrigger.classList.remove('active');
          otherTrigger.nextElementSibling.classList.remove('active');
        }
      });
      
      // Toggle current accordion
      if (isActive) {
        this.classList.remove('active');
        content.classList.remove('active');
      } else {
        this.classList.add('active');
        content.classList.add('active');
      }
    });
  });

  // Search overlay functionality
  const searchToggle = document.querySelector('[data-search-toggle]');
  const searchOverlay = document.querySelector('[data-search-overlay]');
  const searchBackground = document.querySelector('[data-search-background]');

  // Open search overlay
  if (searchToggle && searchOverlay) {
    searchToggle.addEventListener('click', function(e) {
      e.preventDefault();
      searchOverlay.classList.add('active');
      body.style.overflow = 'hidden'; // Prevent background scrolling
    });
  }

  // Close search overlay when clicking on the dark background
  if (searchBackground) {
    searchBackground.addEventListener('click', function(e) {
      searchOverlay.classList.remove('active');
      body.style.overflow = ''; // Restore scrolling
    });
  }

  // Close search overlay on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && searchOverlay && searchOverlay.classList.contains('active')) {
      searchOverlay.classList.remove('active');
      body.style.overflow = ''; // Restore scrolling
    }
  });
  });
</script>

{% schema %}
  {
    "name": "Header",
    "settings": [
      {
        "type": "link_list",
        "id": "menu",
        "label": "Navigation menu"
      }, {
        "type": "checkbox",
        "id": "show_language_selector",
        "label": "Show language selector",
        "default": true
      }
    ]
  }
{% endschema %}