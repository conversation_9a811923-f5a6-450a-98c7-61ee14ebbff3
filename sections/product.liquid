{% comment %}
  Product details section matching Ayla design system
  Includes: image gallery, product info, features, purchase options, delivery info, and accordion
{% endcomment %}

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Jost:wght@400;500&display=swap" rel="stylesheet">

<div class="product-details-container">
  <div class="product-details-wrapper">
    
    <!-- Product Images Section -->
    <div class="product-images-section">
      <div class="main-product-image">
        {% if product.featured_image %}
          <img src="{{ product.featured_image | img_url: '640x520' }}" alt="{{ product.featured_image.alt | escape }}">
        {% else %}
          <div class="product-image-placeholder">
            <svg width="100" height="100" viewBox="0 0 100 100" fill="none">
              <rect width="100" height="100" fill="#F9F9F4"/>
              <path d="M25 40h50v20H25V40z" fill="#E0E0C8"/>
            </svg>
          </div>
        {% endif %}
        
        <!-- Product icon badge -->
        <div class="product-icon-badge">
          <img src="{{ 'chicken-product-icon-circle.png' | asset_url }}" alt="Chicken" width="72" height="72">
        </div>
      </div>
      
      <!-- Image thumbnails -->
      <div class="product-image-thumbnails">
        {% comment %} Skip the first image and show the next 4 {% endcomment %}
        {% for image in product.images offset: 1 limit: 4 %}
          <div class="thumbnail-item">
            <img src="{{ image | img_url: '314x420' }}" alt="{{ image.alt | escape }}">
          </div>
        {% endfor %}
        
        {% comment %} If less than 5 total images (1 main + 4 thumbnails), add placeholder thumbnails {% endcomment %}
        {% assign images_count = product.images | size %}
        {% if images_count > 1 %}
          {% assign thumbnail_count = images_count | minus: 1 %}
        {% else %}
          {% assign thumbnail_count = 0 %}
        {% endif %}
        
        {% if thumbnail_count < 4 %}
          {% assign placeholders_needed = 4 | minus: thumbnail_count %}
          {% for i in (1..placeholders_needed) %}
            <div class="thumbnail-item thumbnail-placeholder">
              <div class="placeholder-content">
                <svg width="60" height="60" viewBox="0 0 60 60" fill="none">
                  <circle cx="30" cy="30" r="30" fill="#E0E0C8" opacity="0.3"/>
                  <path d="M25 28h10v4H25v-4z" fill="#6A9981" opacity="0.5"/>
                </svg>
              </div>
            </div>
          {% endfor %}
        {% endif %}
      </div>
    </div>

    <!-- Product Info Section -->
    <div class="product-info-section">
      
      <!-- Availability Badge with Dynamic Product Line -->
      <div class="availability-section">
        <div class="ayla-logo-container">
          {% comment %} Determine product line based on product tags or type {% endcomment %}
          {% assign product_line = 'delight' %}
          {% if product.tags contains 'help' or product.tags contains 'ayla-help' %}
            {% assign product_line = 'help' %}
          {% elsif product.tags contains 'rescue' or product.tags contains 'ayla-rescue' %}
            {% assign product_line = 'rescue' %}
          {% endif %}
          
          <div class="ayla-logo-text">
            <svg width="67" height="40" viewBox="0 0 67 40" fill="none">
              <path d="M11.4702 28.1122C10.8961 28.5552 10.0971 29.0232 9.12301 29.216C7.71554 29.4911 5.53506 28.9481 5.53506 24.7521C5.53506 24.6176 5.53506 24.4926 5.5434 24.3675C5.69342 21.6337 8.04059 19.5357 10.7628 19.5357H11.4702V28.1122ZM10.6961 8.04085H5.77676V11.0091H8.10726C9.96374 11.0091 11.4702 12.5225 11.4702 14.386V16.5768H9.65537C5.18502 16.5768 0 18.9166 0 25.2951C0 29.3077 1.8471 31.6402 5.77676 31.6402C8.27395 31.6402 10.3044 30.6532 11.4702 29.9351V31.0378H17.2886V14.6538C17.2886 11.0091 14.3341 8.04085 10.6961 8.04085Z" fill="#2D4F40"/>
              <path d="M20.2942 20.4466C20.4015 24.9689 18.7117 30.9878 27.444 30.9878C33.7042 30.9878 34.5783 21.2991 34.5783 21.2991H32.7134C32.7134 27.1013 29.5422 28.0122 29.5422 28.0122C29.5422 28.0122 26.2376 29.0315 26.1209 20.4466V7.92413H20.2858L20.2942 20.4466Z" fill="#2D4F40"/>
              <path d="M32.6635 7.92413V32.6929C32.6635 34.3823 31.8977 36.0041 30.5319 36.9733C29.7996 37.4913 28.8588 37.8759 27.6607 37.8759L28.1098 39.8739C28.1098 39.8739 38.4986 41.4789 38.4986 33.2369C38.4986 32.5678 38.4986 7.92413 38.4986 7.92413H32.6635Z" fill="#2D4F40"/>
              <path d="M41.7781 0H47.6049V30.9794H41.7781V0Z" fill="#2D4F40"/>
              <path d="M61.1899 28.1122C60.6159 28.5552 59.8168 29.0232 58.8427 29.216C57.4363 29.4911 55.2548 28.9481 55.2548 24.7521C55.2548 24.6176 55.2548 24.4926 55.2631 24.3675C55.4131 21.6337 57.7603 19.5357 60.4825 19.5357H61.1899V28.1122ZM60.4158 8.04085H55.4965V11.0091H57.827C59.6835 11.0091 61.1899 12.5225 61.1899 14.386V16.5768H59.3751C54.9058 16.5768 49.7114 18.9083 49.7114 25.2951C49.7114 29.3077 51.5595 31.6402 55.4881 31.6402C57.9853 31.6402 60.0158 30.6532 61.1816 29.9351V31.0378H67V14.6538C67 11.0091 64.0538 8.04085 60.4158 8.04085Z" fill="#2D4F40"/>
            </svg>
            <span class="product-line-text">{{ product_line }}</span>
          </div>
        </div>
        <div class="availability-badge">
          <span class="availability-text">Dostępne</span>
        </div>
      </div>

      <!-- Product Description -->
      <div class="product-description-section">
        <!-- Rating positioned at top right -->
        <div class="product-rating">
          <div class="rating-stars">
            {% for i in (1..5) %}
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M7.61958 1.98137C7.73932 1.61284 8.26068 1.61284 8.38042 1.98137L9.70631 6.06202C9.75986 6.22683 9.91344 6.33841 10.0867 6.33841H14.3774C14.7649 6.33841 14.926 6.83426 14.6125 7.06202L11.1413 9.584C11.0011 9.68586 10.9424 9.8664 10.996 10.0312L12.3219 14.1119C12.4416 14.4804 12.0198 14.7868 11.7063 14.5591L8.23511 12.0371C8.09492 11.9352 7.90508 11.9352 7.76489 12.0371L4.29368 14.5591C3.98019 14.7868 3.5584 14.4804 3.67814 14.1119L5.00402 10.0312C5.05757 9.8664 4.99891 9.68586 4.85872 9.584L1.38751 7.06202C1.07402 6.83426 1.23513 6.33841 1.62262 6.33841H5.91327C6.08656 6.33841 6.24014 6.22683 6.29369 6.06202L7.61958 1.98137Z" fill="#D9B573"/>
              </svg>
            {% endfor %}
          </div>
          <span class="rating-value">4.95</span>
        </div>
        
        <!-- Product title and subtitle -->
        <div class="product-highlights">
          <h1 class="product-title">{{ product.title | default: "Liofilizowane przysmaki dla psa z filetu z piersi kurczaka" }}</h1>
          <div class="product-subtitle-row">
            <span class="product-subtitle">100% filet z piersi kurczaka</span>
            <div class="highlight-separator"></div>
            <span class="product-weight">28 g</span>
          </div>
        </div>
      </div>

      <!-- Product Features -->
      <div class="product-features-section">
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-content">
              <div class="feature-text">
                <h3 class="feature-title">Jakość Human-Grade</h3>
                <p class="feature-subtitle">Wysokiej klasy mięso i produkcja</p>
              </div>
              <div class="feature-icon">
                <img src="{{ 'human-grade-badge-icon.svg' | asset_url }}" alt="Human-Grade" width="40" height="40">
              </div>
            </div>
          </div>
          
          <div class="feature-separator"></div>
          
          <div class="feature-item">
            <div class="feature-content">
              <div class="feature-text">
                <h3 class="feature-title">Hipoalergiczne</h3>
                <p class="feature-subtitle">Odpowiednie dla wrażliwych pupili</p>
              </div>
              <div class="feature-icon">
                <img src="{{ 'hipoalergiczny-icon.png' | asset_url }}" alt="Hipoalergiczne" width="40" height="40">
              </div>
            </div>
          </div>
        </div>
        
        <div class="features-separator"></div>
        
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-content">
              <div class="feature-text">
                <h3 class="feature-title">Ekologia</h3>
                <p class="feature-subtitle">Opakowania w całości do recyklingu</p>
              </div>
              <div class="feature-icon">
                <img src="{{ 'ekologia-icon.png' | asset_url }}" alt="Ekologia" width="40" height="40">
              </div>
            </div>
          </div>
          
          <div class="feature-separator"></div>
          
          <div class="feature-item">
            <div class="feature-content">
              <div class="feature-text">
                <h3 class="feature-title">Czysty Skład</h3>
                <p class="feature-subtitle">Bez szkodliwych składników, wypełniaczy i konserwantów</p>
              </div>
              <div class="feature-icon">
                <img src="{{ 'czystz-sklad-icons.png' | asset_url }}" alt="Czysty Skład" width="40" height="40">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Price Section -->
      <div class="price-section">
        <div class="price-details">
          <div class="price-controls">
            <div class="quantity-selector">
              <button class="quantity-btn quantity-decrease" type="button">
                <svg width="12" height="2" viewBox="0 0 12 2" fill="none">
                  <path d="M1 1H11" stroke="#2D4F40" stroke-linecap="round"/>
                </svg>
              </button>
              <span class="quantity-value">1</span>
              <button class="quantity-btn quantity-increase" type="button">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M6 1V11" stroke="#2D4F40" stroke-linecap="round"/>
                  <path d="M1 6H11" stroke="#2D4F40" stroke-linecap="round"/>
                </svg>
              </button>
            </div>
            <div class="price-amount">
              <span class="current-price">{{ product.price | money | default: "23,90 zł" }}</span>
            </div>
          </div>
          <div class="price-note">
            <span>Najniższa cena towaru w okresie 30 dni: {{ product.price | money | default: "23,90 zł" }}</span>
          </div>
        </div>

        <!-- Purchase Options -->
        <div class="purchase-options">
          {% form 'product', product %}
            <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
            <input type="hidden" name="quantity" value="1" class="quantity-input">
            
            <button type="submit" class="add-to-cart-btn">
              <span>Dodaj do koszyka</span>
            </button>
          {% endform %}
          
          <button class="buy-now-btn" type="button">
            <span>Kup Teraz</span>
          </button>
        </div>
      </div>

      <!-- Delivery Info -->
      <div class="delivery-info-section">
        <div class="delivery-details">
          <div class="delivery-item">
            <div class="delivery-content">
              <img src="{{ 'Delivery icon.png' | asset_url }}" alt="Darmowa dostawa" width="24" height="24">
              <div class="delivery-text">
                <h4 class="delivery-title">Darmowa dostawa</h4>
                <p class="delivery-subtitle">od 149zł do paczkomatu</p>
              </div>
            </div>
          </div>
          
          <div class="delivery-separator"></div>
          
          <div class="delivery-item">
            <div class="delivery-content">
              <img src="{{ 'Delivery icon2.png' | asset_url }}" alt="Wysyłka w 24h" width="24" height="24">
              <div class="delivery-text">
                <h4 class="delivery-title">Wysyłka w 24h</h4>
                <p class="delivery-subtitle">Inpost & DHL</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="payment-info">
          <div class="payment-content">
            <h4 class="payment-title">Bezpieczne płatności</h4>
            <div class="payment-methods">
              <div class="payment-method">
                <img src="https://api.builder.io/api/v1/image/assets/TEMP/ed3db9fa9c52651defa634636f27cca728159ac6?width=70" alt="Visa" width="35" height="19">
              </div>
              <div class="payment-method">
                <img src="https://api.builder.io/api/v1/image/assets/TEMP/e86d0a91f8c5214ecbd9caf3b15b10898ff65b2a?width=74" alt="Mastercard" width="37" height="18">
              </div>
              <div class="payment-method">
                <img src="https://api.builder.io/api/v1/image/assets/TEMP/99475d8d7ffe8a110bc98f0f2d09e8c20e81da79?width=72" alt="PayU" width="36" height="18">
              </div>
              <div class="payment-method">
                <img src="https://api.builder.io/api/v1/image/assets/TEMP/49a8d522d512b2eec0f223d4f774bf277e300a43?width=56" alt="BLIK" width="28" height="22">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Details Accordion -->
      <div class="product-accordion">
        
        {% comment %} Opis section - using metafield or fallback content {% endcomment %}
        {% if product.metafields.custom.opis or product.description %}
          <div class="accordion-item">
            <button class="accordion-header" data-accordion="description">
              <div class="accordion-title">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5002 10.9093C18.585 9.64037 20.4616 8.21558 23.3191 8.21558C28.3161 8.21558 31.6694 12.9072 31.6694 17.276C31.6694 26.4088 20.2994 33.4052 17.5002 33.4052C14.701 33.4052 3.33105 26.4088 3.33105 17.276C3.33105 12.9072 6.68443 8.21558 11.6814 8.21558C14.5389 8.21558 16.4155 9.64037 17.5002 10.9093Z" stroke="#2D4F40" stroke-linecap="round"/>
                  <path d="M22.0478 17.7502L17.0699 22.7281C16.6794 23.1187 16.0462 23.1187 15.6557 22.7281L12.9512 20.0236" stroke="#2D4F40" stroke-linecap="round"/>
                </svg>
                <span>Opis</span>
              </div>
              <svg class="accordion-toggle" width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path d="M14 20H26" stroke="#2D4F40" stroke-linecap="round"/>
                <path class="vertical-line" d="M20 14V26" stroke="#2D4F40" stroke-linecap="round"/>
              </svg>
            </button>
            <div class="accordion-content">
              <p>
                {% if product.metafields.custom.opis %}
                  {{ product.metafields.custom.opis }}
                {% elsif product.description %}
                  {{ product.description }}
                {% else %}
                  Liofilizowany filet z piersi kurczaka zawiera lekkostrawne i łatwo przyswajalne białko oraz mikroelementy takie jak fosfor, potas, sód, magnez, żelazo, a także witaminy A, C, D, E i K oraz witaminy z grupy B, które są niezbędne do prawidłowego funkcjonowania układu nerwowego, krwionośnego i odpornościowego oraz utrzymanie dobrej kondycji skóry i sierści.
                {% endif %}
              </p>
            </div>
          </div>
        {% endif %}

        {% comment %} Skład section - using metafield {% endcomment %}
        {% if product.metafields.custom.sklad %}
          <div class="accordion-item">
            <button class="accordion-header" data-accordion="ingredients">
              <div class="accordion-title">
                <img src="{{ 'icon-sklad.png' | asset_url }}" alt="Skład" width="40" height="40">
                <span>Skład</span>
              </div>
              <svg class="accordion-toggle" width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path d="M14 20H26" stroke="#2D4F40" stroke-linecap="round"/>
                <path class="vertical-line" d="M20 14V26" stroke="#2D4F40" stroke-linecap="round"/>
              </svg>
            </button>
            <div class="accordion-content">
              <p>{{ product.metafields.custom.sklad | newline_to_br }}</p>
            </div>
          </div>
        {% endif %}

        {% comment %} Liofilizacja section - using metafield {% endcomment %}
        {% if product.metafields.custom.liofilizacja %}
          <div class="accordion-item">
            <button class="accordion-header" data-accordion="freeze-drying">
              <div class="accordion-title">
                <img src="{{ 'icon-liofilizacja.png' | asset_url }}" alt="Liofilizacja" width="40" height="40">
                <span>Liofilizacja</span>
              </div>
              <svg class="accordion-toggle" width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path d="M14 20H26" stroke="#2D4F40" stroke-linecap="round"/>
                <path class="vertical-line" d="M20 14V26" stroke="#2D4F40" stroke-linecap="round"/>
              </svg>
            </button>
            <div class="accordion-content">
              <p>{{ product.metafields.custom.liofilizacja }}</p>
            </div>
          </div>
        {% endif %}

        {% comment %} Opakowanie i przechowywanie section - using metafield {% endcomment %}
        {% if product.metafields.custom.opakowanie_i_przechowywanie %}
          <div class="accordion-item">
            <button class="accordion-header" data-accordion="packaging">
              <div class="accordion-title">
                <img src="{{ 'icon-opakowanie.png' | asset_url }}" alt="Opakowanie" width="40" height="40">
                <span>Opakowanie i przechowywanie</span>
              </div>
              <svg class="accordion-toggle" width="40" height="40" viewBox="0 0 40 40" fill="none">
                <path d="M14 20H26" stroke="#2D4F40" stroke-linecap="round"/>
                <path class="vertical-line" d="M20 14V26" stroke="#2D4F40" stroke-linecap="round"/>
              </svg>
            </button>
            <div class="accordion-content">
              <p>{{ product.metafields.custom.opakowanie_i_przechowywanie }}</p>
            </div>
          </div>
        {% endif %}

      </div>
    </div>
  </div>
</div>

{% stylesheet %}
  :root {
    --Primary-Color-P700---Main: #2D4F40;
    --Primary-Color-P400: #6A9981;
    --Primary-Color-P500: #4C7D67;
    --Primary-Color-P0: #FFF;
    --Primary-Color-P50: #F2F7F4;
    --Bezowe-B50: #F9F9F4;
    --Bezowe-B100: #F0F0E4;
    --Bezowe-B200: #E0E0C8;
  }

  .product-details-container {
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    position: relative;
    width: 100%;
    padding: 49px 120px 80px;
    min-height: 100vh;
    background-color: var(--Primary-Color-P0);
  }

  .product-details-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 80px;
    max-width: 1200px;
    margin: 0 auto;
  }

  /* Product Images Section */
  .product-images-section {
    display: flex;
    width: 639px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .main-product-image {
    width: 640px;
    height: 520px;
    border-radius: 12px;
    background: var(--Bezowe-B50);
    position: relative;
    overflow: hidden;
  }

  .main-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
  }

  .product-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--Bezowe-B50);
    border-radius: 12px;
  }

  .product-icon-badge {
    position: absolute;
    top: 39px;
    right: 124px;
    width: 72px;
    height: 72px;
  }
  
  .product-icon-badge img {
    width: 72px;
    height: 72px;
    object-fit: contain;
  }

  .product-image-thumbnails {
    display: grid;
    grid-template-columns: repeat(2, 314px);
    grid-template-rows: repeat(2, 420px);
    gap: 12px;
    width: 640px;
  }

  .thumbnail-item {
    width: 314px;
    height: 420px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    background: var(--Bezowe-B50);
  }

  .thumbnail-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--Bezowe-B50);
  }

  .placeholder-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
  }

  /* Product Info Section */
  .product-info-section {
    display: flex;
    width: 481px;
    flex-direction: column;
    align-items: flex-start;
    gap: 40px;
  }

  /* Availability Section */
  .availability-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  }

  .ayla-logo-container {
    width: 88px;
    height: 51px;
    position: relative;
  }
  
  .ayla-logo-text {
    width: 88px;
    height: 51px;
    position: relative;
  }
  
  .ayla-logo-text svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 67px;
    height: 40px;
  }
  
  .product-line-text {
    position: absolute;
    left: 0;
    top: 33px;
    padding-left: 39px;
    font-family: 'Jost', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 117.5%;
    color: var(--Primary-Color-P700---Main);
    height: 19px;
  }

  .availability-badge {
    display: flex;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    border-radius: 24px;
    background: var(--Primary-Color-P50);
  }

  .availability-text {
    color: var(--Primary-Color-P500);
    font-size: 12px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    margin: 0;
  }

  /* Product Description */
  .product-description-section {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
  }

  .product-rating {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }

  .product-highlights {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding-right: 100px; /* Space for rating */
  }

  .product-title {
    color: var(--Primary-Color-P700---Main);
    font-size: 48px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -1.44px;
    margin: 0 0 12px 0;
    width: 100%;
  }

  .product-subtitle-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .product-subtitle {
    color: var(--Primary-Color-P700---Main);
    font-size: 16px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.32px;
  }

  .highlight-separator {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: var(--Bezowe-B200);
  }

  .product-weight {
    color: var(--Primary-Color-P700---Main);
    font-size: 16px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.32px;
  }

  .rating-stars {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .rating-value {
    color: var(--Primary-Color-P700---Main);
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.28px;
  }

  /* Product Features */
  .product-features-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    border-radius: 12px;
    border: 1px solid var(--Bezowe-B100);
    background: var(--Primary-Color-P0);
  }

  .features-grid {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
  }

  .feature-item {
    width: 240px;
    height: 69px;
    box-sizing: border-box;
  }

  .feature-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px 16px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }

  .feature-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 4px;
    width: 160px;
    margin-right: 8px;
  }

  .feature-title {
    color: var(--Primary-Color-P700---Main);
    font-size: 14px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.42px;
    margin: 0;
  }

  .feature-subtitle {
    color: var(--Primary-Color-P700---Main);
    font-size: 10px;
    font-weight: 400;
    line-height: 120%;
    letter-spacing: -0.3px;
    opacity: 0.7;
    margin: 0;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .feature-icon img {
    width: 40px;
    height: 40px;
    object-fit: contain;
  }

  .feature-separator {
    width: 1px;
    min-width: 1px;
    height: 69px;
    background: var(--Bezowe-B100);
    flex-shrink: 0;
  }

  .features-separator {
    height: 1px;
    width: 100%;
    background: var(--Bezowe-B100);
  }

  /* Price Section */
  .price-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    width: 100%;
  }

  .price-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
  }

  .price-controls {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .quantity-selector {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 44px;
    border: 1px solid var(--Bezowe-B100);
    overflow: hidden;
  }

  .quantity-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--Primary-Color-P0);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  }

  .quantity-btn:hover {
    background: var(--Bezowe-B50);
  }

  .quantity-value {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--Primary-Color-P700---Main);
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    border-left: 1px solid var(--Bezowe-B100);
    border-right: 1px solid var(--Bezowe-B100);
  }

  .current-price {
    color: var(--Primary-Color-P700---Main);
    font-size: 24px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.72px;
  }

  .price-note {
    color: var(--Primary-Color-P700---Main);
    font-size: 12px;
    font-weight: 400;
    line-height: 110%;
    letter-spacing: -0.36px;
    opacity: 0.7;
  }

  .price-note span {
    margin: 0;
  }

  /* Purchase Options */
  .purchase-options {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
  }

  .add-to-cart-btn {
    display: flex;
    width: 318px;
    height: 52px;
    padding: 0;
    justify-content: center;
    align-items: center;
    gap: 12px;
    border-radius: 44px;
    background: var(--Primary-Color-P700---Main);
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .add-to-cart-btn:hover {
    background: #1e3729;
  }

  .add-to-cart-btn span {
    color: var(--Primary-Color-P0);
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    line-height: 125%;
    letter-spacing: 0.64px;
    text-transform: uppercase;
  }

  .buy-now-btn {
    display: flex;
    width: 151px;
    height: 52px;
    padding: 0;
    justify-content: center;
    align-items: center;
    gap: 12px;
    border-radius: 44px;
    border: 1px solid var(--Bezowe-B100);
    background: var(--Primary-Color-P0);
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .buy-now-btn:hover {
    background: var(--Bezowe-B50);
  }

  .buy-now-btn span {
    color: var(--Primary-Color-P700---Main);
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    line-height: 125%;
    letter-spacing: 0.64px;
    text-transform: uppercase;
  }

  /* Delivery Info */
  .delivery-info-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    border-radius: 12px;
    border: 1px solid var(--Bezowe-B100);
    overflow: hidden;
  }

  .delivery-details {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    width: 100%;
  }

  .delivery-item {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .delivery-content {
    display: flex;
    padding: 16px;
    justify-content: center;
    align-items: center;
    gap: 16px;
    flex: 1 0 0;
  }

  .delivery-content img {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    object-fit: contain;
  }

  .delivery-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 2px;
  }

  .delivery-title {
    color: var(--Primary-Color-P700---Main);
    font-size: 16px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: -0.32px;
    margin: 0;
  }

  .delivery-subtitle {
    color: var(--Primary-Color-P700---Main);
    font-size: 12px;
    font-weight: 400;
    line-height: 110%;
    letter-spacing: -0.36px;
    opacity: 0.7;
    margin: 0;
  }

  .delivery-separator {
    width: 1px;
    height: auto;
    align-self: stretch;
    background: var(--Bezowe-B100);
  }

  .payment-info {
    display: flex;
    padding: 12px 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    width: 100%;
    background: var(--Bezowe-B50);
  }

  .payment-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .payment-title {
    color: var(--Primary-Color-P700---Main);
    font-size: 16px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: -0.32px;
    margin: 0;
  }

  .payment-methods {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
  }

  .payment-method {
    display: flex;
    height: 32px;
    padding: 0 8px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: var(--Bezowe-B50);
  }

  .payment-method img {
    max-height: 22px;
    border-radius: 3px;
  }

  /* Product Accordion */
  .product-accordion {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }

  .accordion-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    border-bottom: 1px solid var(--Bezowe-B100);
    background: var(--Primary-Color-P0);
  }

  .accordion-header {
    display: flex;
    padding: 12px 0;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border: none;
    background: var(--Primary-Color-P0);
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .accordion-header:hover {
    background: var(--Bezowe-B50);
  }

  .accordion-title {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .accordion-title img {
    width: 40px;
    height: 40px;
    object-fit: contain;
  }

  .accordion-title span {
    color: var(--Primary-Color-P700---Main);
    font-size: 16px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.48px;
  }

  .accordion-toggle {
    width: 40px;
    height: 40px;
    transition: transform 0.3s ease;
  }

  .accordion-toggle .vertical-line {
    transition: opacity 0.3s ease;
  }

  .accordion-item.active .accordion-toggle .vertical-line {
    opacity: 0;
  }

  .accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding: 0 44px;
  }

  .accordion-item.active .accordion-content {
    /* Let JS control height for smooth animation */
    padding: 0 44px 16px;
  }

  .accordion-content p {
    color: var(--Primary-Color-P700---Main);
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.28px;
    margin: 0;
  }

  /* Responsive Design */
  
  /* Tablet */
  @media (max-width: 1200px) {
    .product-details-container {
      padding: 40px 60px;
    }
    
    .product-details-wrapper {
      gap: 60px;
      flex-direction: column;
      align-items: center;
    }
    
    .product-images-section {
      width: 100%;
      max-width: 640px;
    }
    
    .product-info-section {
      width: 100%;
      max-width: 640px;
    }
    
    .main-product-image {
      width: 100%;
      max-width: 640px;
    }
    
    .product-image-thumbnails {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 280px);
      width: 100%;
      max-width: 640px;
    }
    
    .thumbnail-item {
      width: 100%;
      height: 280px;
    }
  }

  /* Mobile */
  @media (max-width: 768px) {
    .product-details-container {
      padding: 20px 16px;
    }
    
    .product-details-wrapper {
      gap: 40px;
    }
    
    .product-title {
      font-size: 32px;
      line-height: 120%;
      letter-spacing: -0.96px;
      width: 100%;
      margin: 0 0 8px 0;
    }
    
    .product-highlights {
      padding-right: 0;
    }
    
    .product-description-section {
      gap: 16px;
    }
    
    .product-rating {
      position: static;
      align-self: flex-end;
      margin-bottom: 16px;
    }
    
    .main-product-image {
      height: 300px;
    }
    
    .product-icon-badge {
      position: absolute;
      top: 20px;
      right: 60px;
      width: 48px;
      height: 48px;
    }
    
    .product-icon-badge img {
      width: 48px;
      height: 48px;
    }
    
    .product-image-thumbnails {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 200px);
      gap: 8px;
    }
    
    .thumbnail-item {
      width: 100%;
      height: 200px;
    }
    
    .features-grid {
      flex-direction: column;
    }
    
    .feature-separator {
      width: 100%;
      height: 1px;
    }
    
    .purchase-options {
      flex-direction: column;
    }
    
    .buy-now-btn {
      width: 100%;
    }
    
    .delivery-details .delivery-item {
      flex-direction: column;
    }
    
    .delivery-separator {
      width: 100%;
      height: 1px;
    }
    
    .payment-content {
      flex-direction: column;
      gap: 12px;
      text-align: center;
    }
  }
{% endstylesheet %}

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Quantity selector functionality
  const quantityValue = document.querySelector('.quantity-value');
  const quantityInput = document.querySelector('.quantity-input');
  const decreaseBtn = document.querySelector('.quantity-decrease');
  const increaseBtn = document.querySelector('.quantity-increase');

  let currentQuantity = 1;

  function updateQuantity(newQuantity) {
    if (newQuantity >= 1) {
      currentQuantity = newQuantity;
      quantityValue.textContent = currentQuantity;
      if (quantityInput) {
        quantityInput.value = currentQuantity;
      }
    }
  }

  decreaseBtn.addEventListener('click', function() {
    updateQuantity(currentQuantity - 1);
  });

  increaseBtn.addEventListener('click', function() {
    updateQuantity(currentQuantity + 1);
  });

  // Accordion functionality - allows multiple sections open with dynamic height
  const accordionHeaders = document.querySelectorAll('.accordion-header');

  function setOpenHeight(contentEl) {
    if (!contentEl) return;
    // Temporarily reset to measure current scrollHeight accurately
    contentEl.style.maxHeight = 'none';
    const target = contentEl.scrollHeight;
    contentEl.style.maxHeight = target + 'px';
  }

  accordionHeaders.forEach(header => {
    header.addEventListener('click', function() {
      const accordionItem = this.parentElement;
      const content = accordionItem.querySelector('.accordion-content');
      const isOpening = !accordionItem.classList.contains('active');

      if (isOpening) {
        accordionItem.classList.add('active');
        setOpenHeight(content);
      } else {
        // Collapse
        if (content) {
          content.style.maxHeight = '0';
        }
        accordionItem.classList.remove('active');
      }
    });
  });

  // Open "Opis" (description) by default if present
  const defaultHeader = document.querySelector('.accordion-header[data-accordion="description"]');
  if (defaultHeader) {
    const item = defaultHeader.parentElement;
    const content = item.querySelector('.accordion-content');
    item.classList.add('active');
    setOpenHeight(content);
  }

  // Recalculate heights on resize to accommodate responsive line wrapping
  function recalcOpenHeights() {
    document.querySelectorAll('.accordion-item.active .accordion-content').forEach(setOpenHeight);
  }
  window.addEventListener('resize', recalcOpenHeights);

  // Buy now button functionality
  const buyNowBtn = document.querySelector('.buy-now-btn');
  if (buyNowBtn) {
    buyNowBtn.addEventListener('click', function() {
      // Add to cart first, then redirect to checkout
      const form = document.querySelector('form[action*="cart/add"]');
      if (form) {
        // Submit form via AJAX
        const formData = new FormData(form);
        
        fetch('/cart/add.js', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          // Redirect to checkout
          window.location.href = '/checkout';
        })
        .catch(error => {
          console.error('Error:', error);
          // Fallback to cart page
          window.location.href = '/cart';
        });
      }
    });
  }
});
</script>

{% schema %}
{
  "name": "Product Details",
  "settings": [
    {
      "type": "header",
      "content": "Product Display Settings"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "label": "Show product rating",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_features",
      "label": "Show product features",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_delivery_info",
      "label": "Show delivery information",
      "default": true
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
