{% comment %}
  Hero Ayla Delight Section - Figma Design Implementation
  Complex hero with text overlay, product images, and decorative elements
{% endcomment %}

<hero-ayla-delight
  class="hero-ayla-delight"
  data-total-slides="{{ section.blocks.size | default: 1 }}"
  data-autoplay-enabled="{{ section.settings.autoplay_enabled | default: true }}"
  data-autoplay-delay="{{ section.settings.autoplay_delay | default: 5 }}">
  <div class="hero-container">
    {% for block in section.blocks %}
      <div class="hero-slide{% if forloop.first %} active{% endif %}{% if block.type == 'purple_slide' %} theme-purple{% endif %}" data-slide-index="{{ forloop.index0 }}">
        <div
          class="hero-content-wrapper"
          {% if block.settings.background_image %}
          style="background-image: url('{{ block.settings.background_image | image_url: width: 1920 }}'); background-size: cover; background-position: center; background-repeat: no-repeat;"
          {% endif %}
          data-desktop-bg="{% if block.settings.background_image %}{{ block.settings.background_image | image_url: width: 1920 }}{% endif %}"
          data-tablet-bg="{% if block.settings.background_image_tablet %}{{ block.settings.background_image_tablet | image_url: width: 1200 }}{% endif %}"
          data-mobile-bg="{% if block.settings.background_image_mobile %}{{ block.settings.background_image_mobile | image_url: width: 1200 }}{% endif %}">
          {% comment %} Text Content Overlay {% endcomment %}
          <div class="hero-text-content">
            <div class="hero-text-inner">
              {% if block.settings.badge_text %}
                <div class="hero-badge">
                  <span>{{ block.settings.badge_text }}</span>
                </div>
              {% endif %}

              <div class="hero-text-group">
                <div class="hero-heading-wrapper">
                  <h1
                    class="hero-heading"
                    data-desktop-text="{% if block.type == 'purple_slide' %}Limitowana edycja AYLA Help z piersi z kaczką!{% else %}Przysmaki dla kotów i psów, które dbają o ich zdrowie{% endif %}"
                    data-tablet-text="{% if block.type == 'purple_slide' %}Limitowana edycja AYLA Help z piersi z kaczką!{% else %}Przysmaki dla kotów i psów, które dbają o ich zdrowie{% endif %}"
                    data-mobile-text="{% if block.type == 'purple_slide' %}Limitowana edycja AYLA Help z piersi z kaczką!{% else %}Przysmaki dla kotów i psów, które dbają o ich zdrowie{% endif %}">
                    {% if block.type == 'purple_slide' %}Limitowana edycja AYLA Help z piersi z kaczką!{% else %}Przysmaki dla kotów i psów, które dbają o ich zdrowie{% endif %}
                  </h1>
                </div>

                <p class="hero-description">
                  {{ block.settings.description | default: 'Pokochały nas tysiące psów i kotów! W 100% z mięsa najwyższej klasy, jakości human grade, bez żadnych dodatków i konserwantów.' }}
                </p>
              </div>

              {% if block.settings.button_text %}
                <a href="{{ block.settings.button_link | default: '#' }}" class="hero-cta-button">
                  <span data-desktop-text="{{ block.settings.button_text }}" data-tablet-text="{{ block.settings.button_text_tablet | default: block.settings.button_text }}">
                    {{ block.settings.button_text }}
                  </span>
                </a>
              {% endif %}
            </div>
          </div>

          {% comment %} Tablet Layout Product Images {% endcomment %}
          <div class="hero-product-package-1"></div>
          <div class="hero-product-package-2"></div>
          <div class="hero-product-package-3"></div>
          <div class="hero-gradient-overlay"></div>

        </div>
      </div>
    {% endfor %}

    {% if section.blocks.size == 0 %}
      {% comment %} Default slide when no blocks {% endcomment %}
      <div class="hero-slide active">
        <div class="hero-content-wrapper">
          <div class="hero-text-content">
            <div class="hero-text-inner">
              <div class="hero-badge">
                <span>BESTSELLER</span>
              </div>

              <div class="hero-text-group">
                <div class="hero-heading-wrapper">
                  <h1
                    class="hero-heading"
                    data-desktop-text="Przysmaki dla kotów i psów, które dbają o ich zdrowie"
                    data-tablet-text="Przysmaki dla kotów i psów, które dbają o ich zdrowie">
                    Przysmaki dla kotów i psów, które dbają o ich zdrowie
                  </h1>
                </div>

                <p class="hero-description">
                  Pokochały nas tysiące psów i kotów! W 100% z mięsa najwyższej klasy, jakości human grade, bez żadnych dodatków i konserwantów.
                </p>
              </div>

              <a href="#" class="hero-cta-button">
                <span data-desktop-text="ODKRYJ PRZYSMAKI AYLA DELIGHT" data-tablet-text="Odkryj przysmaki ayla delight">
                  ODKRYJ PRZYSMAKI AYLA DELIGHT
                </span>
              </a>
            </div>
          </div>

          <div class="hero-placeholder-message">
            <p>Configure hero slides in theme customizer</p>
          </div>
        </div>
      </div>
    {% endif %}
  </div>

  {% comment %} Navigation controls - outside hero container, underneath {% endcomment %}
  {% if section.blocks.size > 1 %}
    <div class="hero-slide-navigation">
      <button class="nav-button nav-prev" aria-label="Previous slide">
        <svg
          width="15"
          height="14"
          viewBox="0 0 15 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_prev)">
            <path
              d="M10.0713 2.5L4.44629 7.5625L10.0713 12.625"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </g>
          <defs>
            <clipPath id="clip0_prev">
              <rect
                width="13.5"
                height="13.5"
                rx="6.75"
                transform="matrix(-1 0 0 1 14.25 0.25)"
                fill="white" />
            </clipPath>
          </defs>
        </svg>
      </button>

      <div class="progress-indicators">
        <div class="progress-dot active" data-slide="0">
          <div class="progress-fill"></div>
        </div>
        <div class="progress-dot" data-slide="1">
          <div class="progress-fill"></div>
        </div>
      </div>

      <button class="nav-button nav-next" aria-label="Next slide">
        <svg
          width="15"
          height="14"
          viewBox="0 0 15 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_next)">
            <path
              d="M4.92871 2.5L10.5537 7.5625L4.92871 12.625"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </g>
          <defs>
            <clipPath id="clip0_next">
              <rect
                x="0.75"
                y="0.25"
                width="13.5"
                height="13.5"
                rx="6.75"
                fill="white" />
            </clipPath>
          </defs>
        </svg>
      </button>
    </div>
  {% endif %}
</hero-ayla-delight>

{% stylesheet %}
  .hero-ayla-delight {
    --primary-p0: #ffffff;
    --primary-p50: #f2f7f4;
    --primary-p500: #4c7d67;
    --primary-p700: #2d4f40;
    --bezowe-b50: #f9f9f4;
    --bezowe-b100: #f0f0e4;
    --bezowe-b200: #e0e0c8;
    --special-duck: #824386;

    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 8px 8px 24px;
    background: var(--primary-p0);
    position: relative;
    width: 100%;
  }

  .hero-container {
    width: 100%;
    max-width: 1200px;
    height: 560px;
    position: relative;
    border-radius: 24px;
    overflow: hidden;
    background: var(--primary-p50);
  }

  .hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
  }

  .hero-slide.active {
    opacity: 1;
  }

  /* Purple theme slide with background image */
  .hero-slide.theme-purple .hero-content-wrapper {
    border-radius: 24px;
    overflow: hidden;
  }

  .hero-slide.theme-purple .hero-badge {
    background: var(--special-duck);
    border: 1px solid var(--primary-p0);
  }

  .hero-slide.theme-purple .hero-badge span {
    color: var(--primary-p0);
  }

  .hero-slide.theme-purple .hero-heading,
  .hero-slide.theme-purple .hero-description {
    color: var(--primary-p0);
  }

  .hero-slide.theme-purple .hero-cta-button {
    background: var(--bezowe-b200);
  }

  .hero-slide.theme-purple .hero-cta-button span {
    color: var(--primary-p700);
  }

  .hero-slide.theme-purple .hero-cta-button:hover {
    background: var(--bezowe-b100);
  }

  .hero-content-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    /* Back to hidden to control what's visible */
  }

  /* Text Content Styling */
  .hero-text-content {
    position: absolute;
    left: 64px;
    top: 48px;
    width: 556px;
    z-index: 10;
  }

  .hero-text-inner {
    display: flex;
    flex-direction: column;
    gap: 32px;
    align-items: flex-start;
  }

  .hero-badge {
    display: inline-flex;
    padding: 8px 16px;
    border-radius: 24px;
    background: var(--primary-p500);
    align-items: center;
    justify-content: center;
  }

  .hero-badge span {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.4;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    color: var(--primary-p0);
  }

  .hero-text-group {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
  }

  .hero-heading {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 48px;
    line-height: 1.1;
    letter-spacing: -1.44px;
    color: var(--primary-p700);
    margin: 0;
    width: 100% !important;
    max-width: none !important;
    hyphens: none;
  }

  /* Force line break after comma in heading */
  .hero-heading::after {
    content: "";
    display: block;
  }

  /* Alternative approach: Use CSS to break at comma */
  .hero-heading {
    white-space: pre-line;
  }

  .hero-description {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 1.4;
    letter-spacing: -0.54px;
    color: var(--primary-p700);
    margin: 0;
    hyphens: none;
  }

  .hero-cta-button {
    display: inline-flex;
    padding: 16px 24px;
    border-radius: 44px;
    background: var(--primary-p700);
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: transform 0.2s ease
    , opacity 0.2s ease;
  }

  .hero-cta-button:hover {
    transform: scale(1.05);
    opacity: 0.9;
  }

  .hero-cta-button span {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0.64px;
    text-transform: uppercase;
    color: var(--primary-p0);
    white-space: nowrap;
  }


  /* Navigation Controls - positioned underneath hero container */
  .hero-slide-navigation {
    position: relative;
    margin: 0 auto;
    width: fit-content;
    z-index: 20;
    display: flex;
    padding: 4px;
    gap: 12px;
    align-items: center;
    justify-content: center;
    border-radius: 24px;
    border: 1px solid var(--bezowe-b100);
    background: var(--primary-p0);
    backdrop-filter: blur(2px);
  }

  .hero-slide-navigation .nav-button {
    display: flex;
    width: 36px;
    height: 36px;
    padding: 6px;
    align-items: center;
    justify-content: center;
    border-radius: 36px;
    background: var(--primary-p700);
    border: none;
    cursor: pointer;
    color: var(--primary-p0);
    transition: opacity 0.2s ease;
    box-sizing: border-box;
  }

  .hero-slide-navigation .nav-button:hover {
    opacity: 0.9 !important;
    background: var(--primary-p700) !important;
    transform: none !important;
  }

  .hero-slide-navigation .nav-button:active {
    opacity: 0.8 !important;
    background: var(--primary-p700) !important;
    transform: none !important;
  }

  .hero-slide-navigation .nav-button:disabled {
    opacity: 0.5 !important;
    background: var(--primary-p700) !important;
    transform: none !important;
    cursor: not-allowed !important;
  }

  .hero-slide-navigation .nav-button svg {
    width: 13.5px;
    height: 13.5px;
    transform: none !important;
  }

  .hero-slide-navigation .nav-button svg path {
    transform: none !important;
  }

  /* Force correct arrow directions - override any external CSS */
  .hero-slide-navigation .nav-prev {
    transform: none !important;
  }

  .hero-slide-navigation .nav-prev svg {
    transform: none !important;
  }

  .hero-slide-navigation .nav-prev svg path {
    transform: none !important;
  }

  .hero-slide-navigation .nav-next {
    transform: none !important;
  }

  .hero-slide-navigation .nav-next svg {
    transform: none !important;
  }

  .hero-slide-navigation .nav-next svg path {
    transform: none !important;
  }


  .progress-indicators {
    display: flex;
    align-items: center;
    gap: 8px;
  }


  .progress-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--bezowe-b200);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .progress-dot.active {
    width: 56px;
    height: 6px;
    border-radius: 3px;
    background: #e0e0c8;
    /* Figma design color */
  }

  .progress-fill {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: #2d4f40;
    /* Figma design color */
    border-radius: inherit;
    transition: width 0.1s ease;
  }

  /* Ensure inactive dots have no fill */
  .progress-dot:not(.active) .progress-fill {
    width: 0 !important;
    display: none;
  }

  .hero-placeholder-message {
    position: absolute;
    top: 50%;
    right: 100px;
    transform: translateY(-50%);
    text-align: center;
    color: var(--primary-p700);
    font-family: 'Jost'
    , sans-serif;
    font-size: 18px;
    opacity: 0.5;
  }


  /* Responsive Design - FIXED BREAKPOINTS */

  /* Large desktop view - 1101px and above */@media screen and (min-width: 1101px) {
    .hero-container {
      max-width: 1200px;
    }

    .hero-text-content {
      left: 64px;
      width: 556px;
    }
  }

  /* Tablet view - 1100px and below */
  @media screen and (max-width: 1100px) {
    .hero-ayla-delight {
      padding: 0;
      margin: 0 -50vw;
      width: 100vw;
      max-width: none;
      position: relative;
      left: 50%;
      right: 50%;
    }

    .hero-container {
      height: 699px;
      border-radius: 0;
      position: relative;
      overflow: hidden;
      width: 100vw;
      max-width: none;
      background-color: white;
    }

    .hero-text-content {
      left: 8px;
      top: 48px;
      width: calc(100% - 16px);
      max-width: calc(100vw - 16px);
      transform: none;
      z-index: 10;
      padding: 0 8px;
      box-sizing: border-box;
    }

    .hero-text-inner {
      gap: 32px;
    }

    .hero-heading {
      font-size: 48px;
      line-height: 1.1;
      letter-spacing: -1.44px;
      font-weight: 500;
      width: 100%;
    }

    .hero-description {
      font-size: clamp(16px, 3.5vw, 18px);
      line-height: 1.4;
      letter-spacing: -0.54px;
    }

    .hero-cta-button {
      padding: 16px 24px;
      border-radius: 44px;
    }

    .hero-cta-button span {
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 0.64px;
      text-transform: uppercase;
    }

    /* Tablet layout uses the configured background image from theme settings */
    .hero-content-wrapper {
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: scroll;
      width: calc(100% - 16px);
      margin: 0 8px;
      box-sizing: border-box;
    }

    /* Ensure tablet background image is properly sized and positioned */
    .hero-content-wrapper[data-current-view="tablet"] {
      background-size: cover !important;
      background-position: center center !important;
      background-repeat: no-repeat !important;
    }

    .hero-content-wrapper[data-current-view="tablet-fallback"] {
      background-size: cover !important;
      background-position: center center !important;
      background-repeat: no-repeat !important;
    }

    /* Gradient overlay at bottom */
    .hero-content-wrapper .hero-gradient-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 199px;
      background: linear-gradient(to bottom, rgba(242, 247, 244, 0) 0%, rgba(242, 247, 244, 0.25) 100%);
      z-index: 7;
    }
  }

  /* Desktop view - EXACTLY 801px and above */
  @media screen and (min-width: 801px) {
    /* Ensure uppercase text for desktop */
    .hero-ayla-delight .hero-cta-button span {
      text-transform: uppercase !important;
    }

    /* Add desktop indicator for debugging */
    .hero-ayla-delight::before {
      content: 'Desktop Mode (>800px)';
      position: fixed;
      top: 0;
      right: 0;
      background: #4CAF50;
      color: white;
      padding: 4px 8px;
      font-size: 12px;
      z-index: 99999;
      pointer-events: none;
      display: none;
      /* Remove 'none' to see debug info */
    }
  }

  /* Large screens (1281px-1440px) */
  @media screen and (min-width: 1281px) and(max-width: 1440px) {
    .hero-container {
      max-width: 1200px;
    }

    .hero-text-content {
      left: 48px;
      width: 556px;
    }
  }

  /* Medium-large screens (801px-1280px) */
  @media screen and (min-width: 801px) and(max-width: 1280px) {
    .hero-container {
      max-width: calc(100% - 32px);
      height: 480px;
    }

    .hero-text-content {
      left: 40px;
      top: 40px;
      width: 556px;
    }

    .hero-heading {
      font-size: 44px;
    }

    .hero-description {
      font-size: clamp(16px, 3vw, 17px);
    }
  }

  /* Tablet to mobile transition - 700px to 800px */
  @media screen and (min-width: 701px) and(max-width: 800px) {
    .hero-ayla-delight .hero-cta-button span {
      text-transform: uppercase !important;
    }
  }

  /* Mobile view - 700px and below */
  @media screen and (max-width: 700px) {
    /* Add mobile indicator for debugging */
    .hero-ayla-delight::before {
      content: 'Mobile Mode (≤700px)';
      position: fixed;
      top: 0;
      right: 0;
      background: #FF9800;
      color: white;
      padding: 4px 8px;
      font-size: 12px;
      z-index: 99999;
      pointer-events: none;
      display: none;
      /* Remove 'none' to see debug info */
    }

    .hero-ayla-delight {
      padding: 0;
      margin: 0 -50vw;
      width: 100vw;
      max-width: none;
      position: relative;
      left: 50%;
      right: 50%;
      gap: 0;
    }

    .hero-container {
      height: 699px;
      border-radius: 0;
      position: relative;
      overflow: hidden;
      width: 100vw;
      max-width: none;
      background-color: white;
    }

    .hero-text-content {
      left: 8px;
      top: 48px;
      width: calc(100% - 16px);
      max-width: calc(100vw - 16px);
      transform: none;
      padding: 0 8px;
      box-sizing: border-box;
    }

    .hero-heading {
      font-size: 48px;
      line-height: 1.1;
      letter-spacing: -1.44px;
      font-weight: 500;
    }

    .hero-description {
      font-size: clamp(16px, 3.5vw, 18px);
      line-height: 1.4;
      letter-spacing: -0.54px;
    }

    .hero-cta-button {
      padding: 16px 24px;
      border-radius: 44px;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 0.64px;
    }

    /* Force uppercase for mobile */
    .hero-ayla-delight .hero-cta-button span {
      text-transform: uppercase !important;
    }
  }

  /* Mobile phones (576px and below) */
  @media (max-width: 576px) {
    .hero-ayla-delight {
      padding: 0;
      margin: 0 -50vw;
      width: 100vw;
      max-width: none;
      position: relative;
      left: 50%;
      right: 50%;
      gap: 0;
    }

    .hero-container {
      height: 500px;
      border-radius: 0;
      position: relative;
      overflow: hidden;
      width: 100vw;
      max-width: none;
      background-color: white;
    }

    .hero-text-content {
      left: 8px;
      top: 24px;
      width: calc(100% - 16px);
      max-width: calc(100vw - 16px);
      transform: none;
      /* Leave space for product image */
      z-index: 15;
      /* Ensure text is above products on mobile */
      padding: 0 8px;
      box-sizing: border-box;
    }

    .hero-text-inner {
      gap: 24px;
    }

    .hero-heading {
      font-size: 28px;
      line-height: 1.2;
      letter-spacing: -0.84px;
    }

    .hero-description {
      font-size: clamp(12px, 3vw, 14px);
      line-height: 1.4;
      letter-spacing: -0.42px;
    }

    .hero-cta-button {
      padding: 12px 20px;
      border-radius: 36px;
    }

    .hero-ayla-delight .hero-cta-button span {
      font-size: 14px;
      line-height: 18px;
      letter-spacing: 0.56px;
      text-transform: uppercase !important;
      /* Force uppercase for mobile */
    }


    .hero-slide-navigation {
      bottom: 15px;
      gap: 8px;
      border-radius: 20px;
    }

    .hero-slide-navigation .nav-button {
      width: 36px;
      height: 36px;
      padding: 5px;
      border-radius: 36px;
    }

  }

  /* Mobile phones (max-width: 700px) */
  @media (max-width: 700px) {
    .hero-ayla-delight {
      padding: 0;
      margin: 0 -50vw;
      width: 100vw;
      max-width: none;
      position: relative;
      left: 50%;
      right: 50%;
      gap: 0;
    }

    .hero-container {
      border-radius: 0;
      height: 770px;
      position: relative;
      overflow: hidden;
      width: 100vw;
      max-width: none;
      background-color: white;
    }

    .hero-heading {
      text-align: left;
    }

    .hero-description {
      text-align: left;
    }

    /* Mobile background image handling */
    .hero-content-wrapper {
      width: calc(100% - 16px);
      height: 100%;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      background-attachment: scroll;
      margin: 0 8px;
      box-sizing: border-box;
    }

    /* Ensure mobile background image is properly sized and positioned */
    .hero-content-wrapper[data-current-view="mobile"] {
      background-size: cover !important;
      background-position: center center !important;
      background-repeat: no-repeat !important;
      /* Ensure the image covers the full container without being too zoomed */
      min-height: 100%;
      /* Better image quality on mobile */
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    .hero-content-wrapper[data-current-view="mobile-fallback-tablet"],
    .hero-content-wrapper[data-current-view="mobile-fallback-desktop"] {
      background-size: cover !important;
      background-position: center center !important;
      background-repeat: no-repeat !important;
      min-height: 100%;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    .hero-text-content {
      left: 8px !important;
      top: 32px !important;
      width: calc(100% - 16px) !important;
      max-width: calc(100vw - 16px) !important;
      transform: none !important;
      z-index: 15;
      overflow: hidden;
      padding: 0 8px;
      box-sizing: border-box;
    }

    .hero-text-inner {
      gap: 32px;
      width: 100%;
      overflow: hidden;
      box-sizing: border-box;
      max-width: 100%;
      text-align: left;
    }

    .hero-badge {
      padding: 8px 16px;
      border-radius: 24px;
    }

    .hero-badge span {
      font-size: 12px;
      letter-spacing: 1.2px;
      font-weight: 500;
    }

    .hero-heading {
      font-size: 40px;
      line-height: 1.1;
      letter-spacing: -1.2px;
      font-weight: 500;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: none;
      max-width: 100%;
    }

    .hero-description {
      font-size: 18px !important;
      line-height: 1.4;
      letter-spacing: -0.54px;
      margin-top: 24px;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: none;
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
    }

    .hero-cta-button {
      padding: 16px 24px;
      border-radius: 44px;
      margin-top: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      width: fit-content;
      margin-left: 0;
      margin-right: 0;
    }

    /* Purple theme slide - left align CTA button (same as default) */
    .hero-slide.theme-purple .hero-cta-button {
      margin-left: 0;
      margin-right: 0;
    }

    .hero-ayla-delight .hero-cta-button span {
      font-size: 16px;
      line-height: 20px;
      letter-spacing: 0.64px;
      text-transform: uppercase !important;
      font-weight: 500;
    }


    .hero-slide-navigation {
      bottom: 10px;
      gap: 6px;
      padding: 3px;
      border-radius: 16px;
    }

    .hero-slide-navigation .nav-button {
      width: 28px;
      height: 28px;
      padding: 4px;
      border-radius: 28px;
    }

    .hero-slide-navigation .nav-button svg {
      width: 10px;
      height: 10px;
    }

    .progress-bar {
      width: 40px;
    }

    .progress-fill {
      width: 16px;
    }
  }

  /* Very small mobile screens (max-width: 420px) - center green CTA button */
  @media (max-width: 420px) {
    .hero-cta-button {
      margin-left: auto;
      margin-right: auto;
    }
  }

  /* Very small mobile screens (max-width: 400px) */
  @media (max-width: 400px) {
    .hero-heading {
      font-size: 40px;
      line-height: 1.1;
      letter-spacing: -1.2px;
      font-weight: 500;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: none;
      max-width: 100%;
    }

    .hero-description {
      font-size: 18px !important;
      line-height: 1.4;
      letter-spacing: -0.54px;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: none;
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
    }

    .hero-text-content {
      left: 8px !important;
      width: calc(100% - 16px) !important;
      max-width: calc(100vw - 16px) !important;
      transform: none !important;
      padding: 0 8px;
      box-sizing: border-box;
    }
  }
{% endstylesheet %}

<script>
  (function() {
    'use strict';
    
    // Wait for DOM to be ready
    function initHeroResponsive() {
      const heroSlides = document.querySelectorAll('.hero-slide');
      
      if (!heroSlides.length) {
        console.warn('Hero slides not found, retrying...');
        setTimeout(initHeroResponsive, 100);
        return;
      }
      
      function updateResponsiveContent() {
        // Get actual viewport width - use document.documentElement.clientWidth as fallback
        const viewportWidth = Math.min(
          window.innerWidth || 0,
          document.documentElement.clientWidth || 0,
          document.body.clientWidth || 0
        );
        
        // CRITICAL FIX: Mobile view is 0-700px, Tablet is 701-1100px, Desktop is 1101px+
        const isMobile = viewportWidth <= 700;
        const isTablet = viewportWidth > 700 && viewportWidth <= 1100;
        const isDesktop = viewportWidth > 1100;
        const viewMode = isMobile ? 'mobile' : (isTablet ? 'tablet' : 'desktop');
        
        
        // Add data attribute to track current view mode
        document.documentElement.setAttribute('data-hero-view', viewMode);
        document.documentElement.setAttribute('data-viewport-width', viewportWidth);
        
        
        // Process each slide
        heroSlides.forEach((slide, index) => {
          const contentWrapper = slide.querySelector('.hero-content-wrapper');
          const heading = slide.querySelector('.hero-heading');
          const buttonText = slide.querySelector('.hero-cta-button span');
          
          // Update background image
          if (contentWrapper) {
            const mobileBg = contentWrapper.getAttribute('data-mobile-bg');
            const tabletBg = contentWrapper.getAttribute('data-tablet-bg');
            const desktopBg = contentWrapper.getAttribute('data-desktop-bg');
            
            // Apply appropriate background based on breakpoint
            const slideType = slide.classList.contains('theme-purple') ? 'Purple' : 'Green';
            
            if (isMobile) {
              // MOBILE MODE (0-480px)
              if (mobileBg) {
                contentWrapper.style.backgroundImage = `url('${mobileBg}')`;
                contentWrapper.style.backgroundSize = 'cover';
                contentWrapper.style.backgroundPosition = 'center center';
                contentWrapper.style.backgroundRepeat = 'no-repeat';
                contentWrapper.style.backgroundAttachment = 'scroll';
                contentWrapper.setAttribute('data-current-view', 'mobile');
              } else if (tabletBg) {
                // Fallback to tablet if no mobile image
                contentWrapper.style.backgroundImage = `url('${tabletBg}')`;
                contentWrapper.style.backgroundSize = 'cover';
                contentWrapper.style.backgroundPosition = 'center center';
                contentWrapper.style.backgroundRepeat = 'no-repeat';
                contentWrapper.style.backgroundAttachment = 'scroll';
                contentWrapper.setAttribute('data-current-view', 'mobile-fallback-tablet');
              } else if (desktopBg) {
                // Fallback to desktop if no mobile or tablet image
                contentWrapper.style.backgroundImage = `url('${desktopBg}')`;
                contentWrapper.style.backgroundSize = 'cover';
                contentWrapper.style.backgroundPosition = 'center center';
                contentWrapper.style.backgroundRepeat = 'no-repeat';
                contentWrapper.style.backgroundAttachment = 'scroll';
                contentWrapper.setAttribute('data-current-view', 'mobile-fallback-desktop');
  (`[Hero] Slide ${index} (${slideType}): No mobile/tablet bg, using desktop as fallback: ${desktopBg}`);
              } else {
  (`[Hero] Slide ${index} (${slideType}): No background images configured!`);
              }
            } else if (isTablet) {
              // TABLET MODE (481-1100px)
              if (tabletBg) {
                contentWrapper.style.backgroundImage = `url('${tabletBg}')`;
                contentWrapper.style.backgroundSize = 'cover';
                contentWrapper.style.backgroundPosition = 'center center';
                contentWrapper.style.backgroundRepeat = 'no-repeat';
                contentWrapper.style.backgroundAttachment = 'scroll';
                contentWrapper.setAttribute('data-current-view', 'tablet');
  (`[Hero] Slide ${index} (${slideType}): Applied tablet background: ${tabletBg}`);
              } else if (desktopBg) {
                // Fallback to desktop if no tablet image
                contentWrapper.style.backgroundImage = `url('${desktopBg}')`;
                contentWrapper.style.backgroundSize = 'cover';
                contentWrapper.style.backgroundPosition = 'center center';
                contentWrapper.style.backgroundRepeat = 'no-repeat';
                contentWrapper.style.backgroundAttachment = 'scroll';
                contentWrapper.setAttribute('data-current-view', 'tablet-fallback');
  (`[Hero] Slide ${index} (${slideType}): No tablet bg, using desktop as fallback: ${desktopBg}`);
              } else {
  (`[Hero] Slide ${index} (${slideType}): No background images configured!`);
              }
            } else {
              // DESKTOP MODE (1101px+)
              if (desktopBg) {
                contentWrapper.style.backgroundImage = `url('${desktopBg}')`;
                contentWrapper.style.backgroundSize = 'cover';
                contentWrapper.style.backgroundPosition = 'center center';
                contentWrapper.style.backgroundRepeat = 'no-repeat';
                contentWrapper.style.backgroundAttachment = 'scroll';
                contentWrapper.setAttribute('data-current-view', 'desktop');
  (`[Hero] Slide ${index} (${slideType}): Applied desktop background: ${desktopBg}`);
              } else {
  (`[Hero] Slide ${index} (${slideType}): No desktop background configured!`);
              }
            }
          }
          
          // Update heading text
          if (heading) {
            const desktopText = heading.getAttribute('data-desktop-text');
            const tabletText = heading.getAttribute('data-tablet-text');
            const mobileText = heading.getAttribute('data-mobile-text');
            
            if (isMobile) {
              // MOBILE MODE: Use mobile text if available, fallback to tablet, then desktop
              if (mobileText) {
                heading.textContent = mobileText;
  (`[Hero] Slide ${index}: Applied mobile heading`);
              } else if (tabletText) {
                heading.textContent = tabletText;
  (`[Hero] Slide ${index}: No mobile heading, using tablet for mobile`);
              } else if (desktopText) {
                heading.textContent = desktopText;
  (`[Hero] Slide ${index}: No mobile/tablet heading, using desktop for mobile`);
              }
            } else if (isTablet) {
              // TABLET MODE: Use tablet text if available
              if (tabletText) {
                heading.textContent = tabletText;
  (`[Hero] Slide ${index}: Applied tablet heading`);
              } else if (desktopText) {
                heading.textContent = desktopText;
  (`[Hero] Slide ${index}: No tablet heading, using desktop`);
              }
            } else {
              // DESKTOP MODE: Use desktop text
              if (desktopText) {
                heading.textContent = desktopText;
  (`[Hero] Slide ${index}: Applied desktop heading`);
              }
            }
          }
          
          // Update button text and enforce text transform
          if (buttonText) {
            const desktopText = buttonText.getAttribute('data-desktop-text');
            const tabletText = buttonText.getAttribute('data-tablet-text');
            
            if (isMobile) {
              // MOBILE MODE: Lowercase text (mobile uses tablet text for now)
              if (tabletText) {
                buttonText.textContent = tabletText;
              } else if (desktopText) {
                buttonText.textContent = desktopText;
              }
              // Force lowercase for mobile (0-700px)
              buttonText.style.textTransform = 'none';
              buttonText.style.textTransform = 'initial';
  (`[Hero] Slide ${index}: Button text set to lowercase (mobile)`);
            } else if (isTablet) {
              // TABLET MODE: Lowercase text
              if (tabletText) {
                buttonText.textContent = tabletText;
              } else if (desktopText) {
                buttonText.textContent = desktopText;
              }
              // Force uppercase for tablet (701-1100px)
              buttonText.style.textTransform = 'uppercase';
  (`[Hero] Slide ${index}: Button text set to uppercase (tablet)`);
            } else {
              // DESKTOP MODE: Uppercase text
              if (desktopText) {
                buttonText.textContent = desktopText;
              }
              // Force uppercase for desktop (1101px+)
              buttonText.style.textTransform = 'uppercase';
  (`[Hero] Slide ${index}: Button text set to UPPERCASE (desktop)`);
            }
          }
        });
        
        // Final verification log
  (`[Hero Responsive] Update complete. Final mode: ${viewMode} at ${viewportWidth}px`);
      }
      
      // Initial call
  ('[Hero Responsive] Initializing...');
      updateResponsiveContent();
      
      // Update on resize with debouncing
      let resizeTimeout;
      window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
  ('[Hero Responsive] Resize detected, updating...');
          updateResponsiveContent();
        }, 100);
      });
      
      // Also update on orientation change for mobile devices
      window.addEventListener('orientationchange', function() {
        setTimeout(function() {
  ('[Hero Responsive] Orientation change detected, updating...');
          updateResponsiveContent();
        }, 200);
      });
      
      // Force update on load complete (in case of race conditions)
      window.addEventListener('load', function() {
        setTimeout(updateResponsiveContent, 250);
      });
    }
    
    // Start initialization
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initHeroResponsive);
    } else {
      // DOM already loaded
      initHeroResponsive();
    }
  })();
</script>

{% javascript %}
  // Immediate syntax validation
  try {
    ('[Hero] JavaScript starting - syntax validation passed');
  } catch (e) {
    console.error('[Hero] Syntax error detected:', e);
    return;
  }

// Ensure DOM is ready before defining custom element
  function initHeroCustomElement() {
    class HeroAylaDelight extends HTMLElement {
      constructor() {
        super();
        this.currentSlide = 0;
        this.slides = [];
        this.autoPlayInterval = null;
        this.autoPlayEnabled = this.dataset.autoplayEnabled === 'true';
        this.autoPlayDelay = parseInt(this.dataset.autoplayDelay) * 1000 || 5000;
      }

      connectedCallback() {

// Use requestAnimationFrame to ensure DOM is fully ready
        requestAnimationFrame(() => {
          this.init();
        });
      }

      disconnectedCallback() {
        this.stopAutoPlay();
      }

      init() {
        this.slides = this.querySelectorAll('.hero-slide');
        this.totalSlides = parseInt(this.dataset.totalSlides) || this.slides.length;

// Check if navigation container exists
        const navigationContainer = this.querySelector('.hero-slide-navigation');

// Check slides in DOM
        const prevButton = this.querySelector('.nav-prev');
        const nextButton = this.querySelector('.nav-next');

        if (prevButton) {
          prevButton.addEventListener('click', (e) => {
            this.stopAutoPlay();
            this.previousSlide();
            if (this.autoPlayEnabled) 
              this.startAutoPlay();
            

          });
        }

        if (nextButton) {
          nextButton.addEventListener('click', (e) => {
            this.stopAutoPlay();
            this.nextSlide();
            if (this.autoPlayEnabled) 
              this.startAutoPlay();
            

          });
        }

        if (this.autoPlayEnabled && this.totalSlides > 1) {
          this.startAutoPlay();
        }

        this.updateProgress();

        this.addEventListener('keydown', (e) => {
          if (e.key === 'ArrowLeft') {
            this.stopAutoPlay();
            this.previousSlide();
            if (this.autoPlayEnabled) 
              this.startAutoPlay();
            


          } else if (e.key === 'ArrowRight') {
            this.stopAutoPlay();
            this.nextSlide();
            if (this.autoPlayEnabled) 
              this.startAutoPlay();
            


          }
        });

        if (this.autoPlayEnabled) {
          this.addEventListener('mouseenter', () => this.stopAutoPlay());
          this.addEventListener('mouseleave', () => {
            if (this.totalSlides > 1) {
              this.startAutoPlay();
            }
          });
        }
      }

      previousSlide() {
        this.changeSlide(
          this.currentSlide > 0
            ? this.currentSlide - 1
            : this.totalSlides - 1
        );
      }

      nextSlide() {
        this.changeSlide(this.currentSlide<this.totalSlides - 1 ? this.currentSlide + 1 : 0);
    }
    
    changeSlide(newIndex) {
      
      if (this.slides[this.currentSlide]) {
        this.slides[this.currentSlide].classList.remove('active');
      }
      
      this.currentSlide = newIndex;
      
      if (this.slides[this.currentSlide]) {
        this.slides[this.currentSlide].classList.add('active');
        
        const nextIndex = (this.currentSlide + 1) % this.totalSlides;
        const nextImage = this.slides[nextIndex]?.querySelector('img[loading="lazy"]');
        if (nextImage && !nextImage.complete) {
          nextImage.loading = 'eager';
        }
      }
      
      this.updateProgress();
    }
    
    updateProgress() {
      const progressFill = this.querySelector('.progress-fill');
      if (progressFill) {
        const fillWidth = ((this.currentSlide + 1) / this.totalSlides) * 56;
        progressFill.style.width = fillWidth + 'px';
      }
      
      const dots = this.querySelectorAll('.progress-dot');
      dots.forEach((dot, index) => {
          if (index === 0) {
            dot.classList.toggle('active', this.currentSlide === 0);
          } else if (index === 1) {
            dot.classList.toggle('active', this.currentSlide === 2);
          } else if (index === 2) {
            dot.classList.toggle('active', this.currentSlide === 3);
          }
        });
      }

      startAutoPlay() {
        if (this.totalSlides<= 1) return;
      
      this.stopAutoPlay();
      this.autoPlayInterval = setInterval(() => {
          this.nextSlide();
        }, this.autoPlayDelay) 


        


      }


    }

    stopAutoPlay() {
      if (this.autoPlayInterval) {
        clearInterval(this.autoPlayInterval);
        this.autoPlayInterval = null;
      }
    }
  }

// Check if hero element exists in DOM
  const heroElements = document.querySelectorAll('hero-ayla-delight');

// Ensure custom element is defined
  if (!customElements.get('hero-ayla-delight')) {
    customElements.define('hero-ayla-delight', HeroAylaDelight);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    initHeroCustomElement();
  });
} else {
  initHeroCustomElement();
}

// Simple direct approach - works without custom elements
function initHeroNavigation () {
  ('[Hero] Simple navigation initializer starting');

  const heroSection = document.querySelector('hero-ayla-delight') || document.querySelector('.hero-ayla-delight');
  if (! heroSection) {
    ('[Hero] No hero section found');
    return;
  }

  const slides = heroSection.querySelectorAll('.hero-slide');
  const prevButton = heroSection.querySelector('.nav-prev');
  const nextButton = heroSection.querySelector('.nav-next');

  ('[Hero] Simple init found:', {
    slides: slides.length,
    prevButton: !! prevButton,
    nextButton: !! nextButton
  });

  if (slides.length<= 1) {
('[Hero] Only one slide, navigation not needed');
      return;
    }
    
    let currentSlide = 0;
    
    function changeSlide(newIndex) {
('[Hero] Simple: Changing slide from', currentSlide, 'to', newIndex);
      
      // Remove active class from current slide
      slides[currentSlide]?.classList.remove('active');
      
      // Add active class to new slide
      currentSlide = newIndex;
      slides[currentSlide]?.classList.add('active');
      
      // Update progress indicators
      updateProgress();
    }
    
    function updateProgress() {
      const progressFill = heroSection.querySelector('.progress-fill');
      if (progressFill) {
        const fillWidth = ((currentSlide + 1) / slides.length) * 56;
        progressFill.style.width = fillWidth + 'px';
      }
      
      const dots = heroSection.querySelectorAll('.progress-dot');
      dots.forEach((dot, index) => {
    if (index === 0) {
      dot.classList.toggle('active', currentSlide === 0);
    } else if (index === 1) {
      dot.classList.toggle('active', currentSlide === 2);
    } else if (index === 2) {
      dot.classList.toggle('active', currentSlide === 3);
    }
  }) 


    if (prevButton) {
      prevButton.addEventListener('click', (e) => {
        ('[Hero] Simple: Previous button clicked');
        e.preventDefault();
        const newIndex = currentSlide > 0
          ? currentSlide - 1
          : slides.length - 1;
        changeSlide(newIndex);
      });
    }
  


  if (nextButton) {
    nextButton.addEventListener('click', (e) => {
      ('[Hero] Simple: Next button clicked');
      e.preventDefault();
      const newIndex = currentSlide < slides.length - 1
        ? currentSlide + 1
        : 0;
      changeSlide(newIndex);
    });
  }

  ('[Hero] Simple navigation initialized successfully');
}

// Initialize simple navigation after a short delay
setTimeout(initHeroNavigation, 500);
{% endjavascript %}

<!-- BULLETPROOF NAVIGATION SCRIPT - DO NOT EDIT -->
<script>
  (function() {
  'use strict';
  
  // Prevent duplicate initialization
  if (window.heroBulletproofInitialized) {
  ('[Hero Bulletproof] Already initialized, skipping');
    return;
  }
  window.heroBulletproofInitialized = true;
  
  ('[Hero Bulletproof] Starting bulletproof navigation system');
  
  function initBulletproofNavigation() {
    // Prevent duplicate initialization
    if (window.heroBulletproofNavigationInitialized) {
  ('[Hero Bulletproof] Navigation already initialized, skipping');
      return;
    }
    window.heroBulletproofNavigationInitialized = true;
    
    // Find hero section with multiple approaches
    let heroSection = document.querySelector('hero-ayla-delight');
    if (!heroSection) {
      heroSection = document.querySelector('.hero-ayla-delight');
    }
    if (!heroSection) {
      heroSection = document.querySelector('[class*="hero"]');
    }
    
    if (!heroSection) {
  ('[Hero Bulletproof] No hero section found');
      return;
    }
    
    // Find slides and buttons
    const slides = heroSection.querySelectorAll('.hero-slide');
    const prevButton = heroSection.querySelector('.nav-prev');
    const nextButton = heroSection.querySelector('.nav-next');
    
    if (slides.length <= 1) {
      return;
    }
    
    let currentSlide = 0;
    let autoSlideInterval = null;
    let progressAnimation = null;
    
    function changeSlide(newIndex) {
      
      // Remove active from current
      if (slides[currentSlide]) {
        slides[currentSlide].classList.remove('active');
      }
      
      // Add active to new
      currentSlide = newIndex;
      if (slides[currentSlide]) {
        slides[currentSlide].classList.add('active');
      }
      
      // Update progress
      updateProgress();
      
      // Restart progress animation for new slide
      setTimeout(() => {
        startProgressAnimation();
      }, 100); // Small delay to ensure DOM updates
    }
    
    function nextSlide() {
      const newIndex = currentSlide < slides.length - 1 ? currentSlide + 1 : 0;
      changeSlide(newIndex);
    }
    
    function startAutoSlide() {
      stopAutoSlide(); // Clear any existing interval
      
      // Prevent duplicate intervals
      if (autoSlideInterval) {
        return;
      }
      
      autoSlideInterval = setInterval(nextSlide, 5000); // 5 seconds
      startProgressAnimation();
    }
    
    function stopAutoSlide() {
      if (autoSlideInterval) {
        clearInterval(autoSlideInterval);
        autoSlideInterval = null;
      }
      stopProgressAnimation();
    }
    
    function startProgressAnimation() {
      stopProgressAnimation(); // Clear any existing animation first
      
      // Find the currently active dot
      const activeDot = heroSection.querySelector('.progress-dot.active');
      if (!activeDot) {
        return;
      }
      
      const progressFill = activeDot.querySelector('.progress-fill');
      if (!progressFill) {
        return;
      }
      
      // Reset progress to 0
      progressFill.style.width = '0px';
      
      // Animate progress over 5 seconds
      const startTime = Date.now();
      const duration = 5000; // 5 seconds
      
      function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Calculate width based on progress (56px is the full width from the design)
        const width = progress * 56;
        progressFill.style.width = width + 'px';
        
        if (progress < 1) {
          progressAnimation = requestAnimationFrame(animate);
        } else {
          progressAnimation = null; // Reset for next animation
        }
      }
      
      progressAnimation = requestAnimationFrame(animate);
    }
    
    function stopProgressAnimation() {
      if (progressAnimation) {
        cancelAnimationFrame(progressAnimation);
        progressAnimation = null;
      }
    }
    
    function updateProgress() {
      // Update dots - active dot becomes progress bar
      const dots = heroSection.querySelectorAll('.progress-dot');
      dots.forEach((dot, index) => {
        const isActive = index === currentSlide;
        dot.classList.toggle('active', isActive);
        
        // Reset progress fill for all dots
        const progressFill = dot.querySelector('.progress-fill');
        if (progressFill) {
          progressFill.style.width = '0px';
        }
      });
    }
    
    // Add click listeners
    if (prevButton) {
      prevButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const newIndex = currentSlide > 0 ? currentSlide - 1 : slides.length - 1;
        changeSlide(newIndex);
        // Restart auto-slide timer to prevent double slide change
        startAutoSlide();
      });
    }
    
    if (nextButton) {
      nextButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const newIndex = currentSlide < slides.length - 1 ? currentSlide + 1 : 0;
        changeSlide(newIndex);
        // Restart auto-slide timer to prevent double slide change
        startAutoSlide();
      });
    }
    
    // Hover pause functionality removed - progress animation runs continuously
    
    // Start auto-slide after a short delay
    setTimeout(function() {
      startAutoSlide();
    }, 2000);
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBulletproofNavigation);
  } else {
    initBulletproofNavigation();
  }
  })();
</script>

{% schema %}
  {
    "name": "Hero Ayla Delight",
    "tag": "section",
    "class": "shopify-section-hero-ayla-delight-complex",
    "settings": [
      {
        "type": "header",
        "content": "Slideshow Settings"
      }, {
        "type": "checkbox",
        "id": "autoplay_enabled",
        "label": "Auto-rotate slides",
        "default": true
      }, {
        "type": "range",
        "id": "autoplay_delay",
        "label": "Auto-rotate slides every",
        "min": 3,
        "max": 10,
        "step": 1,
        "unit": "s",
        "default": 5
      }
    ],
    "blocks": [
      {
        "type": "green_slide",
        "name": "🟢 Green Hero Slide",
        "limit": 4,
        "settings": [
          {
            "type": "header",
            "content": "Text Content"
          },
          {
            "type": "text",
            "id": "badge_text",
            "label": "Badge Text",
            "default": "BESTSELLER",
            "info": "Small badge above heading"
          },
          {
            "type": "textarea",
            "id": "heading",
            "label": "Heading",
            "default": "Przysmaki dla kotów i psów,<br>które dbają o ich zdrowie"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Description",
            "default": "Pokochały nas tysiące psów i kotów! W 100% z mięsa najwyższej klasy, jakości human grade, bez żadnych dodatków i konserwantów."
          }, {
            "type": "text",
            "id": "button_text",
            "label": "Button Text",
            "default": "ODKRYJ PRZYSMAKI AYLA DELIGHT"
          }, {
            "type": "url",
            "id": "button_link",
            "label": "Button Link"
          }, {
            "type": "header",
            "content": "Green Theme Background"
          }, {
            "type": "image_picker",
            "id": "background_image",
            "label": "Desktop Background Image",
            "info": "Upload the complete background with product images and treats. Recommended: 1200x560px"
          }, {
            "type": "image_picker",
            "id": "background_image_tablet",
            "label": "Tablet Background Image",
            "info": "Upload the tablet-optimized background. Recommended: 800x787px"
          }, {
            "type": "image_picker",
            "id": "background_image_mobile",
            "label": "Mobile Background Image",
            "info": "Upload the mobile-optimized background. Recommended: 1200x600px for crisp quality on all mobile devices including retina displays"
          }, {
            "type": "header",
            "content": "Tablet Text Content"
          }, {
            "type": "textarea",
            "id": "heading_tablet",
            "label": "Tablet Heading",
            "default": "Przysmaki dla kotów i psów,<br>które dbają o ich zdrowie",
            "info": "Text optimized for tablet view"
          }, {
            "type": "text",
            "id": "button_text_tablet",
            "label": "Tablet Button Text",
            "default": "Odkryj przysmaki ayla delight",
            "info": "Button text optimized for tablet view"
          }
        ]
      }, {
        "type": "purple_slide",
        "name": "🟣 Purple Hero Slide",
        "limit": 4,
        "settings": [
          {
            "type": "header",
            "content": "Text Content"
          },
          {
            "type": "text",
            "id": "badge_text",
            "label": "Badge Text",
            "default": "NOWOŚĆ",
            "info": "Small badge above heading"
          },
          {
            "type": "textarea",
            "id": "heading",
            "label": "Heading",
            "default": "Limitowana edycja AYLA Help z piersi z kaczką!"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Description",
            "default": "Innowacyjny produkt wspierający żywienie w okresie rekonwalescencji, zaburzeń odżywiania dla psów i kotów, poprawiający ich ogólny dobrostan."
          }, {
            "type": "text",
            "id": "button_text",
            "label": "Button Text",
            "default": "SPRAWDŹ TERAZ"
          }, {
            "type": "url",
            "id": "button_link",
            "label": "Button Link"
          }, {
            "type": "header",
            "content": "Purple Theme Background"
          }, {
            "type": "image_picker",
            "id": "background_image",
            "label": "Desktop Background Image",
            "info": "Upload the complete background with decorative elements and product image. Recommended: 1200x560px"
          }, {
            "type": "image_picker",
            "id": "background_image_tablet",
            "label": "Tablet Background Image",
            "info": "Upload the tablet-optimized background. Recommended: 800x787px"
          }, {
            "type": "image_picker",
            "id": "background_image_mobile",
            "label": "Mobile Background Image",
            "info": "Upload the mobile-optimized background. Recommended: 1200x600px for crisp quality on all mobile devices including retina displays"
          }, {
            "type": "header",
            "content": "Tablet Text Content"
          }, {
            "type": "textarea",
            "id": "heading_tablet",
            "label": "Tablet Heading",
            "default": "Limitowana edycja AYLA Help z piersi z kaczką!",
            "info": "Text optimized for tablet view"
          }, {
            "type": "text",
            "id": "button_text_tablet",
            "label": "Tablet Button Text",
            "default": "Sprawdź teraz",
            "info": "Button text optimized for tablet view"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Hero Ayla Delight",
        "category": "Hero",
        "blocks": [
          {
            "type": "green_slide",
            "settings": {
              "badge_text": "BESTSELLER",
              "heading": "Przysmaki dla kotów i psów,<br>które dbają o ich zdrowie",
              "description": "Pokochały nas tysiące psów i kotów! W 100% z mięsa najwyższej klasy, jakości human grade, bez żadnych dodatków i konserwantów.",
              "button_text": "ODKRYJ PRZYSMAKI AYLA DELIGHT"
            }
          }, {
            "type": "purple_slide",
            "settings": {
              "badge_text": "NOWOŚĆ",
              "heading": "Limitowana edycja AYLA Help z piersi z kaczką!",
              "description": "Innowacyjny produkt wspierający żywienie w okresie rekonwalescencji, zaburzeń odżywiania dla psów i kotów, poprawiający ich ogólny dobrostan.",
              "button_text": "SPRAWDŹ TERAZ"
            }
          }
        ]
      }
    ]
  }
{% endschema %}