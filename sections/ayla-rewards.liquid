{% comment %}
  AYLA Rewards Section - Figma Design Implementation
  "Nagrody i wyróżnienia" - Awards and recognition section with three achievement cards
  Matches Figma node-id 4809-21346 exactly with proper colors, typography, and layout
{% endcomment %}

<ayla-rewards class="ayla-rewards">
  <div class="section-container">
    {% comment %} Main heading {% endcomment %}
    <div class="section-header">
      <h2 class="section-title">
        {{ section.settings.main_title | default: 'Nagrody i wyróżnienia' }}
      </h2>
    </div>

    {% comment %} Awards container {% endcomment %}
    <div class="awards-container">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'award_card' %}
            <div class="award-card" {{ block.shopify_attributes }}>
              {% comment %} Award image container {% endcomment %}
              <div
                class="award-image-container"
                {% if block.settings.background_image %}
                style="background-image: url('{{ block.settings.background_image | image_url: width: 400 }}'); background-size: contain; background-position: center; background-repeat: no-repeat;"
                {% endif %}></div>

              {% comment %} Award text below image {% endcomment %}
              <div class="award-content">
                <div class="award-description">
                  {{ block.settings.award_description | default: 'Produkt roku Plebiscyt' }}
                </div>
                <div class="award-title">
                  {{ block.settings.award_title | default: 'Sfinksy' }}
                </div>
              </div>
            </div>
        {% endcase %}
      {% endfor %}
    </div>
  </div>
</ayla-rewards>

{% stylesheet %}
  .ayla-rewards {
    --primary-p700: #2d4f40;
    --primary-p0: #ffffff;
    --bezowe-b100: #f0f0e4;
    --bezowe-b200: #e0e0c8;
    --text-primary: #212121;
    --text-secondary: rgba(33, 33, 33, 0.7);

    background: var(--primary-p0);
    padding: 32px 120px;
    box-sizing: border-box;
  }

  .section-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  /* Section Header */
  .section-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 0 0;
  }

  .ayla-rewards .section-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 24px;
    line-height: 1.1;
    letter-spacing: -0.72px;
    color: var(--primary-p700);
    text-align: center;
    margin: 0;
    white-space: nowrap;
    text-transform: none;
  }

  /* Awards Container */
  .awards-container {
    display: flex;
    flex-direction: row;
    gap: 52.42px;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  /* Award Card */
  .award-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
    flex: 0 0 auto;
    width: 197.58px;
    height: auto;
  }

  /* Award Image Container */
  .award-image-container {
    width: 197.58px;
    height: 129.83px;
    display: flex;
    align-items: center;
    justify-content: center;
  }


  /* Award Content */
  .award-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 2px;
    text-align: center;
    width: 100%;
  }

  .award-description {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: -0.28px;
    color: var(--text-secondary);
    text-align: center;
    white-space: nowrap;
  }

  .award-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 18px;
    line-height: 1.4;
    letter-spacing: -0.36px;
    color: var(--text-primary);
    text-align: center;
    white-space: nowrap;
  }

  /* Responsive Design */
  @media (max-width: 1280px) {
    .ayla-rewards {
      padding: 32px 80px;
    }

    .section-title {
      font-size: 22px;
    }

    .awards-container {
      gap: 45px;
    }

    .award-card {
      width: 170px;
      height: auto;
    }

    .award-image-container {
      width: 170px;
      height: 112px;
    }
  }

  @media (max-width: 1024px) {
    .ayla-rewards {
      padding: 32px 60px;
    }

    .section-title {
      font-size: 20px;
    }

    .awards-container {
      gap: 32px;
    }

    .award-card {
      width: 160px;
      height: auto;
    }

    .award-image-container {
      width: 160px;
      height: 105px;
    }

    .award-title {
      font-size: 16px;
    }

    .award-description {
      font-size: 13px;
    }
  }

  @media (max-width: 768px) {
    .ayla-rewards {
      padding: 24px;
    }

    .section-container {
      gap: 20px;
    }

    .section-title {
      font-size: 18px;
      line-height: 1.2;
    }

    .awards-container {
      flex-direction: column;
      gap: 32px;
    }

    .award-card {
      width: 197.58px;
      height: auto;
    }

    .award-image-container {
      width: 197.58px;
      height: 129.83px;
    }

    .award-title {
      font-size: 18px;
    }

    .award-description {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    .ayla-rewards {
      padding: 20px 16px;
    }

    .section-title {
      font-size: 16px;
    }

    .award-card {
      width: 170px;
      height: auto;
    }

    .award-image-container {
      width: 170px;
      height: 112px;
    }

    .award-title {
      font-size: 16px;
    }

    .award-description {
      font-size: 12px;
    }
  }
{% endstylesheet %}

{% schema %}
  {
    "name": "AYLA Rewards",
    "tag": "section",
    "class": "shopify-section-ayla-rewards",
    "settings": [
      {
        "type": "header",
        "content": "Section Settings"
      }, {
        "type": "text",
        "id": "main_title",
        "label": "Main Title",
        "default": "Nagrody i wyróżnienia"
      }
    ],
    "blocks": [
      {
        "type": "award_card",
        "name": "Award Card",
        "limit": 5,
        "settings": [
          {
            "type": "header",
            "content": "Award Information"
          },
          {
            "type": "text",
            "id": "award_title",
            "label": "Award Title",
            "default": "Sfinksy"
          },
          {
            "type": "text",
            "id": "award_description",
            "label": "Award Description",
            "default": "Produkt roku Plebiscyt"
          },
          {
            "type": "header",
            "content": "Background Image"
          }, {
            "type": "image_picker",
            "id": "background_image",
            "label": "Complete Award Background",
            "info": "Upload the complete award background with logo and decorative elements (recommended: 200x130px)"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "AYLA Rewards",
        "category": "Brand",
        "blocks": [
          {
            "type": "award_card",
            "settings": {
              "award_title": "Sfinksy",
              "award_description": "Produkt roku Plebiscyt"
            }
          }, {
            "type": "award_card",
            "settings": {
              "award_title": "Top for Dog",
              "award_description": "Nagroda Kategoria Must Have"
            }
          }, {
            "type": "award_card",
            "settings": {
              "award_title": "Diamenty Zoologii",
              "award_description": "Rekomendacja ekspertów"
            }
          }
        ]
      }
    ]
  }
{% endschema %}