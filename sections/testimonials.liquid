{% comment %}
  Testimonials Section - Reviews and Expert Opinions Carousel
  Polecani przez specjalistów, pokochane przez klientów!
{% endcomment %}

<testimonials-section class="testimonials-section" data-total-slides="{{ section.blocks.size | default: 1 }}" data-autoplay-enabled="{{ section.settings.autoplay_enabled | default: false }}" data-autoplay-delay="{{ section.settings.autoplay_delay | default: 6 }}">
  <div class="testimonials-container">
    {% comment %} Section Header {% endcomment %}
    <div class="testimonials-header">
      <h2 class="testimonials-title">
        {{ section.settings.main_title | default: 'Polecani przez specjalistów, pokochane przez klientów!' }}
      </h2>
    </div>

    {% comment %} Testimonials Carousel Container {% endcomment %}
    <div class="testimonials-carousel">
      <div class="testimonials-track" style="transform: translateX(0%);">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'client_testimonial' %}
              <div class="testimonial-card client-card" {{ block.shopify_attributes }}>
                <div class="testimonial-content">
                  <div class="testimonial-label">
                    <span class="label-text">{{ block.settings.testimonial_type | default: 'KLIENT' }}</span>
                  </div>
                  
                  <div class="testimonial-quote">
                    <h3 class="quote-main">{{ block.settings.quote_main | default: 'Eryk zachwycony, my też, bo trudno znaleźć na rynku przysmak monobiałkowy w tylu wersjach i takiej super jakości.' }}</h3>
                    {% if block.settings.quote_secondary %}
                      <p class="quote-secondary">{{ block.settings.quote_secondary }}</p>
                    {% endif %}
                  </div>
                </div>
                
                {% comment %} Optional decorative image for client testimonials {% endcomment %}
                {% if block.settings.decoration_image %}
                  <div class="testimonial-decoration">
                    <img src="{{ block.settings.decoration_image | img_url: '280x' }}" 
                         alt="" 
                         loading="lazy"
                         width="139"
                         height="211">
                  </div>
                {% endif %}
              </div>

            {% when 'expert_testimonial' %}
              <div class="testimonial-card expert-card" {{ block.shopify_attributes }}>
                <div class="testimonial-content">
                  <div class="testimonial-label">
                    <span class="label-text">{{ block.settings.testimonial_type | default: 'EKSPERT' }}</span>
                  </div>
                  
                  <div class="testimonial-quote">
                    <h3 class="quote-main">{{ block.settings.quote_main | default: 'Główną zaletą smaczków Ayla jest wysoka jakość surowca, z którego powstają. Jest to idealny produkt dla psów i kotów.' }}</h3>
                    {% if block.settings.quote_secondary %}
                      <p class="quote-secondary">{{ block.settings.quote_secondary }}</p>
                    {% endif %}
                  </div>
                </div>

                {% comment %} Expert Profile Section {% endcomment %}
                <div class="expert-profile">
                  {% if block.settings.expert_photo %}
                    <img src="{{ block.settings.expert_photo | img_url: '112x112' }}" 
                         alt="{{ block.settings.expert_name | default: 'Expert' }}" 
                         class="expert-photo" 
                         width="56" 
                         height="56" 
                         loading="lazy">
                  {% else %}
                    <div class="expert-photo-placeholder"></div>
                  {% endif %}
                  
                  <div class="expert-info">
                    <h4 class="expert-name">{{ block.settings.expert_name | default: 'Expert Name' }}</h4>
                    <p class="expert-title">{{ block.settings.expert_title | default: 'Expert Title' }}</p>
                  </div>
                </div>
              </div>
          {% endcase %}
        {% endfor %}
      </div>
    </div>

    {% comment %} Navigation Controls {% endcomment %}
    {% if section.blocks.size > 1 %}
      <div class="testimonials-navigation">
        <button class="nav-button nav-prev" aria-label="Previous testimonial">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 3L6 12L16 21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <button class="nav-button nav-next" aria-label="Next testimonial">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 3L18 12L8 21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    {% endif %}

    {% comment %} Fallback content when no blocks are configured {% endcomment %}
    {% if section.blocks.size == 0 %}
      <div class="testimonials-placeholder">
        <p>Configure testimonials in the theme customizer</p>
      </div>
    {% endif %}
  </div>
</testimonials-section>

{% stylesheet %}
  .testimonials-section {
    --primary-p700: #2D4F40;
    --primary-p0: #FFF;
    --primary-p200: #C2D8CB;
    --bezowe-b50: #F9F9F4;
    --bezowe-b200: #E0E0C8;
    --client-color: #D56855;
    --expert-color: #7D4386;
    --text-primary: #212121;
    --text-secondary: rgba(33, 33, 33, 0.7);
    
    background: var(--bezowe-b50);
    padding: 72px 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 70px;
    position: relative;
    overflow: hidden; /* Hide cards that extend beyond section bounds */
  }

  .testimonials-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    flex-direction: column;
    align-items: center;
    gap: 70px;
    margin: 0 auto;
    position: relative;
    min-height: 600px; /* Prevent container from resizing */
    overflow: visible; /* Allow carousel to show partial cards */
  }

  /* Header Styles */
  .testimonials-header {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
  }

  .testimonials-title {
    max-width: 637px;
    width: 100%;
    flex-shrink: 0;
    color: var(--primary-p700);
    text-align: center;
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 48px;
    font-style: normal;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -1.44px;
    margin: 0 auto;
  }

  /* Carousel Styles */
  .testimonials-carousel {
    width: calc(100% + 240px); /* Extend more to fill gaps */
    max-width: 1440px;
    height: 480px; /* Fixed height to accommodate active card */
    overflow: hidden; /* Clip cards at carousel boundaries */
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center; /* Center cards vertically */
    padding: 0; /* Ensure no padding interferes */
    margin-left: -120px; /* Pull carousel left more to eliminate gap */
  }

  .testimonials-track {
    display: flex;
    gap: 12px;
    transition: transform 0.5s ease;
    align-items: center; /* Center cards vertically in track */
    justify-content: flex-start;
    height: 100%;
  }
  
  /* Fade effect for outer cards */
  .testimonial-card {
    opacity: 1;
    transform: scale(1);
    transition: background 0.5s cubic-bezier(0.4, 0, 0.2, 1), 
                height 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Apply fade/scale to outer cards */
  .testimonial-card.is-side {
    opacity: 0.8; /* More visible than before */
    transform: scale(1); /* No scaling to match Figma */
  }
  
  .testimonial-card.is-far {
    opacity: 0.5; /* Partially visible */
    transform: scale(1); /* No scaling to match Figma */
  }

  /* Testimonial Card Base Styles */
  .testimonial-card {
    display: flex;
    width: 379px;
    height: 420px;
    min-height: 420px;
    padding: 28px;
    flex-direction: column;
    flex-shrink: 0;
    border-radius: 12px;
    background: var(--primary-p0);
    position: relative;
    box-sizing: border-box;
    transition: background 0.5s cubic-bezier(0.4, 0, 0.2, 1), height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  .testimonial-card.client-card {
    align-items: flex-start;
    justify-content: space-between;
  }

  .testimonial-card.expert-card {
    justify-content: space-between;
    align-items: flex-start;
  }

  /* Middle card (active) gets taller height and green background */
  .testimonial-card.is-active {
    height: 480px;
    min-height: 480px;
    background: var(--primary-p700, #2D4F40) !important; /* All cards get green when active */
  }

  /* When cards are active, update their text colors for better contrast on green background */
  .testimonial-card.is-active .label-text {
    color: var(--primary-p200);
  }

  .testimonial-card.is-active .quote-main,
  .testimonial-card.is-active .quote-secondary {
    color: var(--primary-p0);
  }

  .testimonial-card.is-active .expert-name,
  .testimonial-card.is-active .expert-title {
    color: var(--primary-p0);
  }

  /* Client cards maintain normal gap spacing */
  .testimonial-card.client-card .testimonial-content {
    flex: 1;
  }

  .testimonial-card.client-card .testimonial-decoration {
    flex-shrink: 0;
  }

  /* Smooth transitions for all testimonial cards */
  .testimonial-card .label-text,
  .testimonial-card .quote-main,
  .testimonial-card .quote-secondary,
  .testimonial-card .expert-name,
  .testimonial-card .expert-title {
    transition: color 0.5s ease;
  }

  /* Content Styles */
  .testimonial-content {
    display: flex;
    width: 323px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    flex-shrink: 0;
  }

  .testimonial-label {
    margin-bottom: 0;
  }

  .label-text {
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 1.6px;
    text-transform: uppercase;
  }

  .client-card .label-text {
    color: var(--client-color);
  }

  .expert-card .label-text {
    color: var(--expert-color);
  }

  .quote-main {
    width: 323px;
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: -0.4px;
    margin: 0;
    color: var(--text-primary);
  }

  .quote-secondary {
    width: 323px;
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.28px;
    margin: 0;
    color: var(--text-primary);
  }

  /* Expert Profile Styles */
  .expert-profile {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;
  }

  .expert-photo {
    width: 56px;
    height: 56px;
    border-radius: 50px;
    object-fit: cover;
  }

  .expert-photo-placeholder {
    width: 56px;
    height: 56px;
    border-radius: 50px;
    background: var(--bezowe-b200);
  }

  .expert-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 2px;
  }

  .expert-name {
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: -0.36px;
    margin: 0;
  }

  .expert-title {
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.36px;
    margin: 0;
  }

  .expert-name {
    color: var(--text-primary);
  }

  .expert-title {
    color: var(--text-secondary);
  }

  /* Decoration Styles */
  .testimonial-decoration {
    position: absolute;
    right: 28px;
    bottom: 28px;
    width: 139px;
    height: 211px;
    pointer-events: none;
  }

  .testimonial-decoration img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  /* Navigation Styles */
  .testimonials-navigation {
    display: flex;
    gap: 478px;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 10;
  }

  .nav-button {
    display: flex;
    width: 56px;
    height: 56px;
    padding: 12px;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
    border-radius: 56px;
    background: var(--primary-p700);
    border: none;
    cursor: pointer;
    pointer-events: auto;
    transition: background-color 0.3s ease, transform 0.2s ease;
  }

  .nav-button:hover {
    background: var(--primary-p200);
    transform: scale(1.05);
  }

  .nav-button:active {
    transform: scale(0.95);
  }

  .nav-button svg {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    color: var(--bezowe-b50);
  }

  .nav-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--primary-p700);
    transform: none;
  }

  /* Placeholder Styles */
  .testimonials-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--text-secondary);
    font-family: 'Jost', -apple-system, Roboto, Helvetica, sans-serif;
    font-size: 16px;
  }

  /* Responsive Design */
  @media (max-width: 1440px) {
    .testimonials-section {
      padding: 64px 80px;
    }
    
    .testimonials-carousel {
      width: 100%;
      max-width: 1199px;
    }
    
    .testimonials-navigation {
      gap: 420px;
    }
  }

  @media (max-width: 1024px) {
    .testimonials-section {
      padding: 48px 40px;
      gap: 50px;
    }

    .testimonials-title {
      width: 100%;
      font-size: 40px;
      letter-spacing: -1.2px;
    }
    
    .testimonials-carousel {
      height: 440px; /* Slightly smaller height for tablet */
    }

    .testimonials-navigation {
      position: static;
      margin: 0;
      width: 200px;
      justify-content: space-between;
    }

    .testimonials-track {
      justify-content: center;
    }

    .testimonial-card {
      width: 340px;
      height: 380px;
      min-height: 380px;
    }
    
    .testimonial-card.is-active {
      height: 440px;
      min-height: 440px;
    }

    .testimonial-content {
      width: 284px;
    }

    .quote-main,
    .quote-secondary {
      width: 284px;
    }
  }

  @media (max-width: 768px) {
    .testimonials-section {
      padding: 32px 20px;
      gap: 32px;
    }

    .testimonials-title {
      font-size: 32px;
      letter-spacing: -0.96px;
      line-height: 120%;
    }
    
    .testimonials-carousel {
      width: 100%;
      height: 420px;
    }
    
    .testimonials-container {
      min-height: 500px;
    }

    .testimonial-card {
      width: 300px;
      height: 360px;
      min-height: 360px;
      padding: 24px;
    }
    
    .testimonial-card.is-active {
      height: 420px;
      min-height: 420px;
    }

    .testimonial-card.client-card {
      gap: 150px;
    }

    .testimonial-content {
      width: 252px;
    }

    .quote-main,
    .quote-secondary {
      width: 252px;
    }

    .quote-main {
      font-size: 18px;
    }

    .quote-secondary {
      font-size: 13px;
    }

    .testimonials-track {
      gap: 16px;
    }

    .testimonials-navigation {
      width: 140px;
      gap: 92px;
    }

    .nav-button {
      width: 48px;
      height: 48px;
      padding: 10px;
    }

    .nav-button svg {
      width: 20px;
      height: 20px;
    }
  }
{% endstylesheet %}

{% javascript %}
  class TestimonialsSection extends HTMLElement {
    constructor() {
      super();
      this.activeIndex = 0; // Track which original card is in the spotlight
      this.totalSlides = parseInt(this.dataset.totalSlides) || 1;
      this.autoplayEnabled = this.dataset.autoplayEnabled === 'true';
      this.autoplayDelay = parseInt(this.dataset.autoplayDelay) || 6000;
      this.autoplayTimer = null;
      this.isAnimating = false;
      
      this.track = this.querySelector('.testimonials-track');
      this.cards = Array.from(this.querySelectorAll('.testimonial-card'));
      this.prevButton = this.querySelector('.nav-prev');
      this.nextButton = this.querySelector('.nav-next');
      
      this.init();
    }

    init() {
      if (this.totalSlides <= 3) {
        this.setupStaticDisplay();
        return;
      }
      
      this.setupCarousel();
      this.setupEventListeners();
      
      // Add debug method to window for testing
      window.debugTestimonials = () => this.debugCardStates();
      
      if (this.autoplayEnabled) {
        this.startAutoplay();
        this.setupAutoplayControls();
      }
    }
    
    debugCardStates() {
      console.log('=== TESTIMONIALS DEBUG ===');
      console.log('Total slides:', this.totalSlides);
      console.log('Current trackPosition:', this.trackPosition);
      console.log('Active Index:', this.activeIndex);
      
      // Check each card
      this.allCards.forEach((card, index) => {
        const isExpert = card.classList.contains('expert-card');
        const isActive = card.classList.contains('is-active');
        const originalIndex = card.dataset.originalIndex;
        const cloneSet = card.dataset.cloneSet;
        const computedBg = window.getComputedStyle(card).backgroundColor;
        
        if (isActive) {
          console.log(`Card ${index}: [ACTIVE] ${isExpert ? 'EXPERT' : 'CLIENT'} | Original: ${originalIndex} | Set: ${cloneSet} | BG: ${computedBg}`);
        }
      });
      
      // Check CSS variable
      const rootStyles = getComputedStyle(document.documentElement);
      console.log('--primary-p700 value:', rootStyles.getPropertyValue('--primary-p700') || 'NOT FOUND');
      
      return 'Debug complete - check console';
    }

    setupStaticDisplay() {
      const cardWidth = 379;
      const gap = 12;
      const totalWidth = cardWidth * this.totalSlides + gap * (this.totalSlides - 1);
      const carouselEl = this.querySelector('.testimonials-carousel');
      const carouselWidth = carouselEl ? carouselEl.offsetWidth : 1200;
      const startX = (carouselWidth - totalWidth) / 2;
      
      const middleIndex = Math.floor(this.totalSlides / 2);
      this.cards.forEach((card, index) => {
        if (index === middleIndex) {
          card.classList.add('is-active');
        }
      });
      
      this.track.style.transform = `translateX(${startX}px)`;
    }

    setupCarousel() {
      // Create a circular array by cloning cards
      this.allCards = [];
      this.track.innerHTML = '';
      
      // Create a reasonable number of clones for smooth infinite scrolling
      // 3 sets before, current set, and 3 sets after = 7 sets total
      const totalSets = 7; // Much more reasonable number
      const middleSet = Math.floor(totalSets / 2);
      
      for (let i = 0; i < totalSets; i++) {
        this.cards.forEach((card, index) => {
          const clone = card.cloneNode(true);
          clone.dataset.originalIndex = index;
          clone.dataset.cloneSet = i;
          clone.classList.remove('is-active');
          this.allCards.push(clone);
          this.track.appendChild(clone);
        });
      }
      
      // Start in the middle of all sets
      this.trackPosition = middleSet * this.totalSlides;
      
      this.updateDisplay();
    }

    updateDisplay(skipActiveUpdate = false) {
      const cardWidth = 379;
      const gap = 12;
      const slideWidth = cardWidth + gap;
      
      // Get actual carousel width (now wider to show partial cards)
      const carouselEl = this.querySelector('.testimonials-carousel');
      const carouselWidth = carouselEl ? carouselEl.offsetWidth : 1440;
      
      // We want to show 5 cards: partial-full-center-full-partial
      // Calculate position to center the active card
      // Account for the margin offset we added
      const centerPosition = carouselWidth / 2;
      
      // Position calculation to show partial cards on sides
      const translateX = centerPosition - (cardWidth / 2) - (this.trackPosition * slideWidth);
      
      this.track.style.transform = `translateX(${translateX}px)`;
      
      // Skip active state updates during position resets
      if (skipActiveUpdate) return;
      
      // Update active states immediately
      this.allCards.forEach((card, index) => {
        card.classList.remove('is-active', 'is-side', 'is-far');
        
        // The card at trackPosition is visually in the middle
        if (index === this.trackPosition) {
          card.classList.add('is-active');
        }
        // Cards immediately adjacent are visible
        else if (index === this.trackPosition - 1 || index === this.trackPosition + 1) {
          card.classList.add('is-side');
        }
        // Cards further out are partially visible
        else if (index === this.trackPosition - 2 || index === this.trackPosition + 2) {
          card.classList.add('is-far');
        }
      });
    }

    nextSlide() {
      if (this.isAnimating) return;
      this.isAnimating = true;
      
      // Move to next position
      this.trackPosition++;
      this.activeIndex = (this.activeIndex + 1) % this.totalSlides;
      
      // Animate the transition
      this.track.style.transition = 'transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
      this.updateDisplay();
      
      // Check if we need to reset position (wrap around)
      // Reset when we're getting close to the end
      const totalCards = this.allCards.length;
      const resetThreshold = totalCards - this.totalSlides * 2;
      
      if (this.trackPosition >= resetThreshold) {
        setTimeout(() => {
          this.track.style.transition = 'none';
          this.trackPosition = this.totalSlides * 2; // Reset to near beginning
          this.updateDisplay(true); // Skip active update to prevent flash
          setTimeout(() => {
            this.track.style.transition = '';
          }, 10);
        }, 500);
      }
      
      // After animation completes
      setTimeout(() => {
        this.isAnimating = false;
      }, 500);
      
      this.resetAutoplay();
    }

    prevSlide() {
      if (this.isAnimating) return;
      this.isAnimating = true;
      
      // Move to previous position
      this.trackPosition--;
      this.activeIndex = (this.activeIndex - 1 + this.totalSlides) % this.totalSlides;
      
      // Animate the transition
      this.track.style.transition = 'transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
      this.updateDisplay();
      
      // Check if we need to reset position (wrap around)
      // Reset when we're getting close to the beginning
      const resetThreshold = this.totalSlides;
      
      if (this.trackPosition <= resetThreshold) {
        setTimeout(() => {
          this.track.style.transition = 'none';
          this.trackPosition = this.allCards.length - this.totalSlides * 2; // Reset to near end
          this.updateDisplay(true); // Skip active update to prevent flash
          setTimeout(() => {
            this.track.style.transition = '';
          }, 10);
        }, 500);
      }
      
      // After animation completes
      setTimeout(() => {
        this.isAnimating = false;
      }, 500);
      
      this.resetAutoplay();
    }

    setupEventListeners() {
      if (this.prevButton) {
        this.prevButton.addEventListener('click', () => this.prevSlide());
      }
      
      if (this.nextButton) {
        this.nextButton.addEventListener('click', () => this.nextSlide());
      }

      // Keyboard navigation
      this.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
          e.preventDefault();
          this.prevSlide();
        } else if (e.key === 'ArrowRight') {
          e.preventDefault();
          this.nextSlide();
        }
      });

      // Touch support
      let startX = null;
      let currentX = null;
      
      this.track.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
      }, { passive: true });
      
      this.track.addEventListener('touchmove', (e) => {
        if (!startX) return;
        currentX = e.touches[0].clientX;
      }, { passive: true });

      this.track.addEventListener('touchend', () => {
        if (!startX || !currentX) return;
        
        const diff = startX - currentX;
        if (Math.abs(diff) > 50) {
          if (diff > 0) {
            this.nextSlide();
          } else {
            this.prevSlide();
          }
        }
        
        startX = null;
        currentX = null;
      });
    }

    setupAutoplayControls() {
      this.addEventListener('mouseenter', () => this.stopAutoplay());
      this.addEventListener('mouseleave', () => {
        if (this.autoplayEnabled) this.startAutoplay();
      });

      this.addEventListener('focusin', () => this.stopAutoplay());
      this.addEventListener('focusout', () => {
        if (this.autoplayEnabled) this.startAutoplay();
      });
    }

    startAutoplay() {
      if (!this.autoplayEnabled || this.totalSlides <= 3) return;
      
      this.stopAutoplay();
      this.autoplayTimer = setInterval(() => {
        if (!this.isAnimating) {
          this.nextSlide();
        }
      }, this.autoplayDelay);
    }

    stopAutoplay() {
      if (this.autoplayTimer) {
        clearInterval(this.autoplayTimer);
        this.autoplayTimer = null;
      }
    }

    resetAutoplay() {
      if (this.autoplayEnabled) {
        this.startAutoplay();
      }
    }

    connectedCallback() {
      this.setAttribute('tabindex', '0');
      this.setAttribute('role', 'region');
      this.setAttribute('aria-label', 'Testimonials carousel');
    }

    disconnectedCallback() {
      this.stopAutoplay();
    }
  }

  customElements.define('testimonials-section', TestimonialsSection);
{% endjavascript %}

{% schema %}
{
  "name": "Testimonials",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "main_title",
      "label": "Section Title",
      "default": "Polecani przez specjalistów, pokochane przez klientów!",
      "info": "Main heading for the testimonials section"
    },
    {
      "type": "checkbox",
      "id": "autoplay_enabled",
      "label": "Enable Autoplay",
      "default": false,
      "info": "Automatically advance to the next testimonial"
    },
    {
      "type": "range",
      "id": "autoplay_delay",
      "min": 3,
      "max": 10,
      "step": 1,
      "unit": "sec",
      "label": "Autoplay Delay",
      "default": 6,
      "info": "Time between automatic slide transitions"
    }
  ],
  "blocks": [
    {
      "type": "client_testimonial",
      "name": "Client Testimonial",
      "settings": [
        {
          "type": "text",
          "id": "testimonial_type",
          "label": "Testimonial Type",
          "default": "KLIENT",
          "info": "Label shown above the testimonial (e.g., KLIENT, CUSTOMER)"
        },
        {
          "type": "textarea",
          "id": "quote_main",
          "label": "Main Quote",
          "default": "Eryk zachwycony, my też, bo trudno znaleźć na rynku przysmak monobiałkowy w tylu wersjach i takiej super jakości.",
          "info": "Primary testimonial text"
        },
        {
          "type": "textarea",
          "id": "quote_secondary",
          "label": "Secondary Quote",
          "info": "Additional testimonial text (optional)"
        },
        {
          "type": "image_picker",
          "id": "decoration_image",
          "label": "Decoration Image",
          "info": "Optional decorative image (e.g., dog or cat silhouette)"
        }
      ]
    },
    {
      "type": "expert_testimonial",
      "name": "Expert Testimonial",
      "settings": [
        {
          "type": "text",
          "id": "testimonial_type",
          "label": "Testimonial Type",
          "default": "EKSPERT",
          "info": "Label shown above the testimonial (e.g., EKSPERT, EXPERT)"
        },
        {
          "type": "textarea",
          "id": "quote_main",
          "label": "Main Quote",
          "default": "Główną zaletą smaczków Ayla jest wysoka jakość surowca, z którego powstają. Jest to idealny produkt dla psów i kotów.",
          "info": "Primary testimonial text"
        },
        {
          "type": "textarea",
          "id": "quote_secondary",
          "label": "Secondary Quote",
          "info": "Additional testimonial text (optional)"
        },
        {
          "type": "image_picker",
          "id": "expert_photo",
          "label": "Expert Photo",
          "info": "Professional headshot of the expert"
        },
        {
          "type": "text",
          "id": "expert_name",
          "label": "Expert Name",
          "default": "Expert Name",
          "info": "Full name of the expert"
        },
        {
          "type": "text",
          "id": "expert_title",
          "label": "Expert Title",
          "default": "Expert Title",
          "info": "Professional title or credentials"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Testimonials",
      "blocks": [
        {
          "type": "client_testimonial",
          "settings": {
            "testimonial_type": "KLIENT",
            "quote_main": "Eryk zachwycony, my też, bo trudno znaleźć na rynku przysmak monobiałkowy w tylu wersjach i takiej super jakości.",
            "quote_secondary": "A dla nas to tym bardziej ważne, że Eryk jest alergikiem. Fajnie, że są takie firmy jak Ayla!"
          }
        },
        {
          "type": "expert_testimonial",
          "settings": {
            "testimonial_type": "EKSPERT",
            "quote_main": "Główną zaletą smaczków Ayla jest wysoka jakość surowca, z którego powstają. Jest to idealny produkt dla psów i kotów.",
            "expert_name": "Agnieszka Dąbrowiecka",
            "expert_title": "Zoopsycholog, Trenerka Psów"
          }
        },
        {
          "type": "expert_testimonial",
          "settings": {
            "testimonial_type": "EKSPERT",
            "quote_main": "Główną zaletą smaczków Ayla jest wysoka jakość surowca, z którego powstają. Jest to idealny produkt dla psów i kotów.",
            "quote_secondary": "Mój pies z atopowym zapaleniem skóry przyjął je świetnie! Polecam je ze względu na bardzo dobry skład i wysoką smakowitość.",
            "expert_name": "Martyna Macko-Szymanek",
            "expert_title": "Lekarz Weterynarii"
          }
        },
        {
          "type": "client_testimonial",
          "settings": {
            "testimonial_type": "KLIENT",
            "quote_main": "To motywator ostatniej instancji, jak wszystkie inne przysmaki zawiodą podczas treningów."
          }
        },
        {
          "type": "client_testimonial",
          "settings": {
            "testimonial_type": "KLIENT",
            "quote_main": "Tak jak szaleje za nimi nie szalała za niczym innym od dawna!",
            "quote_secondary": "Zrobi dla nich dosłownie wszystko, naprawdę! Skład jest genialny, bez konserwantów i zbędnych dodatków!"
          }
        }
      ]
    }
  ]
}
{% endschema %}
