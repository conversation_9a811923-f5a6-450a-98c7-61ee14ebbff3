{% comment %}
  This section is used in the article template to render the article page, which
  displays the full content of the blog post and can optionally include a
  comments section for customers (not shown in this example).

  https://shopify.dev/docs/storefronts/themes/architecture/templates/article
{% endcomment %}

{% if article.image %}
  {{ article.image | image_url: width: 1000 | image_tag }}
{% endif %}

<h1>{{ article.title }}</h1>

{% assign date = article.published_at | time_tag: format: 'date' %}
<p>{{ 'blog.article_metadata_html' | t: date: date, author: article.author }}</p>

{{ article.content }}

{% if blog.comments_enabled? %}
  <h2>{{ 'blog.article_comments' | t }}</h2>

  <div id="comments">
    {% paginate article.comments by 10 %}
      {% for comment in article.comments %}
        <div>
          <p>{{ comment.author }}</p>
          <p>
            {{- comment.created_at | time_tag: format: 'date' -}}
          </p>
          <p>{{ comment.content }}</p>
        </div>
      {% endfor %}

      {{ paginate | default_pagination: anchor: 'comments' }}
    {% endpaginate %}
  </div>

  {% form 'new_comment', article %}
    <h2>{{ 'blog.comment_form_title' | t }}</h2>

    {{ form.errors | default_errors }}

    <div>
      <label for="author">{{ 'blog.comment_form_name' | t }}</label>
      <input type="text" name="comment[author]" id="author" value="{{ form.author }}" required>
    </div>

    <div>
      <label for="email">{{ 'blog.comment_form_email' | t }}</label>
      <input type="email" name="comment[email]" id="email" value="{{ form.email }}" required>
    </div>

    <div>
      <label for="body">{{ 'blog.comment_form_body' | t }}</label>
      <textarea name="comment[body]" id="body" required>{{ form.body }}</textarea>
    </div>

    <input type="submit" value="{{ 'blog.comment_form_submit' | t }}">
  {% endform %}
{% endif %}

{% schema %}
{
  "name": "t:general.article",
  "settings": []
}
{% endschema %}
