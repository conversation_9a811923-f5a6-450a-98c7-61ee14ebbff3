{% comment %}
  AYLA Care Benefits Section - Figma Design Implementation
  "Dlaczego AYLA Care jest wyjątkowa?" - Benefits showcase with 4 feature cards
  Matches Figma node-id 4809-18909 exactly with proper colors, typography, and layout
{% endcomment %}

<ayla-care-benefits class="ayla-care-benefits">
  <div class="section-container">
    {% comment %} Main heading {% endcomment %}
    <div class="section-header">
      <h2 class="section-title">
        {{ section.settings.main_title | default: 'Dlaczego AYLA Care jest wyjątkowa?' }}
      </h2>
      <p class="section-subtitle">
        {{ section.settings.subtitle | default: 'Wszystko co najlepsze, w misji stworzenia jak najlepszych produktów dla psów i kotów' }}
      </p>
    </div>

    {% comment %} Benefits cards container {% endcomment %}
    <div class="benefits-cards-container">
      <div class="cards-row cards-row-1">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'orange_card' %}
              <div class="benefit-card orange-card" {{ block.shopify_attributes }}>
                {% comment %} Use background image instead of complex elements {% endcomment %}
                <div
                  class="card-background-image"
                  style="background-image: url('{{ 'benefitssection-orangecard-desktop.png' | asset_url }}');"
                  data-tablet-bg="{{ 'benefitssection-orangecard-tablet.png' | asset_url }}"
                  data-mobile-bg="{{ 'benefitssection-orangecard-mobile.png' | asset_url }}"
                  data-desktop-bg="{{ 'benefitssection-orangecard-desktop.png' | asset_url }}"></div>
              </div>

            {% when 'green_dark_card' %}
              <div
                class="benefit-card green-dark-card"
                {{ block.shopify_attributes }}
                style="background-image: url('{{ 'benefitsection-darkgreencard-desktop.png' | asset_url }}'); background-size: contain; background-position: center; background-repeat: no-repeat;"
                data-tablet-bg="{{ 'benefitssection-darkgreencard-tablet.png' | asset_url }}"
                data-mobile-bg="{{ 'benefitssection-darkgreencard-mobile.png' | asset_url }}"
                data-desktop-bg="{{ 'benefitsection-darkgreencard-desktop.png' | asset_url }}"></div>
          {% endcase %}
        {% endfor %}
      </div>

      <div class="cards-row cards-row-2">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'beige_card' %}
              <div class="benefit-card beige-card" {{ block.shopify_attributes }}>
                {% comment %} Use background image instead of complex elements {% endcomment %}
                <div
                  class="card-background-image"
                  style="background-image: url('{{ 'benefitsection-liofilicard-desktop.png' | asset_url }}');"
                  data-tablet-bg="{{ 'benefitssection-liofilicard-tablet.png' | asset_url }}"
                  data-mobile-bg="{{ 'benefitssection-liofilicard-mobile.png' | asset_url }}"
                  data-desktop-bg="{{ 'benefitsection-liofilicard-desktop.png' | asset_url }}"></div>
              </div>

            {% when 'green_medium_card' %}
              <div class="benefit-card green-medium-card" {{ block.shopify_attributes }}>
                {% comment %} Use background image instead of complex elements {% endcomment %}
                <div
                  class="card-background-image"
                  style="background-image: url('{{ 'benefitsection-greencard-desktop.png' | asset_url }}');"
                  data-tablet-bg="{{ 'benefitssection-greencard-tablet.png' | asset_url }}"
                  data-mobile-bg="{{ 'benefitssection-greencard-mobile.png' | asset_url }}"
                  data-desktop-bg="{{ 'benefitsection-greencard-desktop.png' | asset_url }}"></div>
              </div>
          {% endcase %}
        {% endfor %}
      </div>

      <div class="cards-row cards-row-3">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'brgs_certificate_card' %}
              <div class="benefit-card brgs-certificate-card" {{ block.shopify_attributes }}>
                <div class="card-image-container">
                  <img
                    src="{{ 'BRGS-Certyfikat-jakosci-logo.png' | asset_url }}"
                    alt="BRGS Food Safety Certificate"
                    class="card-top-image"
                    width="80"
                    height="80" />
                </div>
                <h3 class="card-title">{{ block.settings.title | default: 'Tylko mięso z certyfikatem jakości' }}</h3>
                <p class="card-description">{{ block.settings.description | default: 'Nasz produkt zdobył certyfikat BRCGS, co potwierdza jego najwyższą jakość.' }}</p>
              </div>

            {% when 'quality_precision_card' %}
              <div class="benefit-card quality-precision-card" {{ block.shopify_attributes }}>
                <div class="card-image-container">
                  <img
                    src="{{ 'precycja-jakosci-icons.png' | asset_url }}"
                    alt="Quality Precision Icons"
                    class="card-top-image"
                    width="80"
                    height="80" />
                </div>
                <h3 class="card-title">{{ block.settings.title | default: 'Precyzja na każdym etapie produkcji' }}</h3>
                <p class="card-description">{{ block.settings.description | default: 'Od najlepszych dostawców, przez staranny proces produkcji i transport w kontrolowanej temperaturze. Wszystko po to, aby dostarczyć najlepszy produkt dla smakoszy wśród kotów i psów.' }}</p>
              </div>
          {% endcase %}
        {% endfor %}
      </div>
    </div>
  </div>
</ayla-care-benefits>

<script src="{{ 'mobile-images.js' | asset_url }}" defer></script>

{% stylesheet %}
  .ayla-care-benefits {
    --primary-p700: #2d4f40;
    --primary-p0: #ffffff;
    --po500: #d56855;
    --primary-p600: #38634f;
    --primary-p500: #4c7d67;
    --primary-p400: #6a9981;
    --primary-p800: #273f34;
    --bezowe-b50: #f9f9f4;
    --bezowe-b100: #f0f0e4;
    --text-primary: #212121;

    background: var(--primary-p0);
    padding: 40px 120px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .section-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    margin: 0 auto;
  }

  /* Section Header */
  .section-header {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
  }

  .ayla-care-benefits .section-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 48px;
    line-height: 1.1;
    letter-spacing: -1.44px;
    color: var(--primary-p700);
    text-align: center;
    margin: 0;
    width: 100%;
    max-width: 100%;
  }

  .section-subtitle {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 1.4;
    letter-spacing: -0.36px;
    color: var(--primary-p700);
    text-align: center;
    margin: 0 auto;
    width: 100%;
    max-width: 100%;
    text-transform: none;
  }

  /* Benefits Cards Container */
  .benefits-cards-container {
    display: grid;
    grid-template-columns: 378fr 600fr;
    grid-template-rows: 400px 400px 260px;
    gap: 12px;
    width: 100%;
    max-width: 990px;
    margin: 0 auto;
  }

  /* Grid positioning for cards - Row 1: Orange + Green Dark */
  .orange-card {
    grid-column: 1;
    grid-row: 1;
  }

  .green-dark-card {
    grid-column: 2;
    grid-row: 1;
  }

  /* Grid positioning for cards - Row 2: Beige (full width) + Green Medium */
  .beige-card {
    grid-column: 1 / 3;
    grid-row: 2;
    justify-self: start;
  }

  .green-medium-card {
    grid-column: 1 / 3;
    grid-row: 2;
    width: 378px;
    justify-self: end;
  }

  .cards-row {
    display: contents;
  }

  .cards-row-1 {
    display: contents;
  }

  .cards-row-2 {
    display: contents;
  }

  /* Base Benefit Card */
  .benefit-card {
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 400px;
  }

  /* Orange Card */
  .orange-card {
    height: 400px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
  }

  /* Remove old complex orange card styling - now using background image */

  /* Green Dark Card - Now using background image only */.green-dark-card {
    position: relative;
  }


  /* Beige Card */
  .beige-card {
    height: 400px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
  }

  /* Remove old complex beige card styling - now using background image */

  /* Remove old complex beige card visual content styling - now using background image */

  /* Green Medium Card */.green-medium-card {
    width: 378px;
    height: 400px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
  }

  .card-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  /* Remove old complex styling - now using background image */

  /* Remove old complex quality circle styling - now using background image */

  /* Removed old unused styling */

  /* Row 3: New BRGS Certificate and Quality Precision Cards */.cards-row-3 {
    display: contents;
  }

  .brgs-certificate-card,
  .quality-precision-card {
    height: 260px;
    background: var(--primary-p0);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px;
    gap: 16px;
    box-sizing: border-box;
    border: 1px solid #E8E8E8;
  }

  .brgs-certificate-card {
    grid-column: 1;
    grid-row: 3;
  }

  .quality-precision-card {
    grid-column: 2;
    grid-row: 3;
  }

  .card-image-container {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .card-top-image {
    max-height: 80px;
    width: auto;
    object-fit: contain;
  }

  .brgs-certificate-card .card-title,
  .quality-precision-card .card-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 24px;
    line-height: 1.2;
    letter-spacing: -0.72px;
    color: var(--primary-p700);
    margin: 0;
    text-align: center;
    width: 100%;
  }

  .brgs-certificate-card .card-description,
  .quality-precision-card .card-description {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: -0.28px;
    color: var(--primary-p700);
    margin: 0;
    text-align: center;
    width: 100%;
    padding: 0 20px;
  }

  /* Feature ribbon moved to separate section file */

  /* Title wrapping breakpoint - when title needs to wrap */@media (max-width: 900px) {
    .ayla-care-benefits .section-title {
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
  }

  /* Responsive Design */
  @media (max-width: 1280px) {
    .ayla-care-benefits {
      padding: 32px 80px 64px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .ayla-care-benefits .section-title {
      font-size: 42px;
      letter-spacing: -1.26px;
      max-width: 100%;
    }

    .benefits-cards-container {
      grid-template-columns: 340fr 540fr;
      grid-template-rows: 400px 400px 240px;
      gap: 12px;
      max-width: 892px;
    }

    .beige-card {
      height: 510px;
    }

    .green-medium-card {
      width: 340px;
    }

    .brgs-certificate-card,
    .quality-precision-card {
      height: 240px;
    };

    /* Green dark card now uses background image only - no text content */
  }

  /* Small Tablet View - Responsive tablet sizing */
  @media (max-width: 768px) and (min-width: 501px) {
    .ayla-care-benefits {
      padding: 20px 24px 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .section-container {
      width: 100%;
    }

    .ayla-care-benefits .section-title {
      font-size: 36px;
      letter-spacing: -1.08px;
      max-width: 100%;
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
      padding: 0 12px;
      text-align: center;
    }

    .section-subtitle {
      font-size: 16px;
      padding: 0 12px;
      text-align: center;
    }

    /* Tablet: Single column layout */
    .benefits-cards-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-width: 100%;
      width: 100%;
    }

    .cards-row {
      display: contents;
    }

    .benefit-card {
      width: 100% !important;
      max-width: 100%;
      margin: 0;
      height: 400px;
    }

    .beige-card {
      height: 510px;
    }

    /* Specific card adjustments for small tablet */
    .orange-card,
    .green-dark-card,
    .beige-card,
    .green-medium-card {
      width: 100% !important;
      max-width: 100% !important;
      grid-column: 1 !important;
      grid-row: auto !important;
      justify-self: stretch !important;
    }

    .brgs-certificate-card,
    .quality-precision-card {
      width: 100%;
      height: 260px;
      margin: 0;
    }

    /* Small tablet background images */
    .orange-card .card-background-image,
    .beige-card .card-background-image,
    .green-medium-card .card-background-image {
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }

    .green-dark-card {
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    };

    /* Green dark card now uses background image only - no text content */
  }

  /* Tablet View - Cards stacked vertically (matching Figma design) */
  @media (max-width: 1024px) and (min-width: 769px) {
    .ayla-care-benefits {
      padding: 20px 32px 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .section-container {
      width: 100%;
    }

    .ayla-care-benefits .section-title {
      font-size: 36px;
      letter-spacing: -1.08px;
      max-width: 100%;
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
      padding: 0 16px;
      text-align: center;
    }

    .section-subtitle {
      font-size: 17px;
      padding: 0 16px;
      text-align: center;
    }

    .benefits-cards-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-width: 736px;
      width: 100%;
    }

    .cards-row {
      display: contents;
    }

    .benefit-card {
      width: 100% !important;
      max-width: 736px;
      margin: 0;
      height: 400px;
    }

    /* Specific card adjustments for tablet */
    .orange-card,
    .green-dark-card,
    .green-medium-card {
      width: 100% !important;
      max-width: 736px !important;
      height: 400px;
      grid-column: 1 !important;
      grid-row: auto !important;
      justify-self: stretch !important;
    }

    .beige-card {
      width: 100% !important;
      max-width: 736px !important;
      height: 510px;
      grid-column: 1 !important;
      grid-row: auto !important;
      justify-self: stretch !important;
    }

    .brgs-certificate-card,
    .quality-precision-card {
      width: 100%;
      height: 260px;
      margin: 0;
    }

    /* Tablet background images - will be handled by JavaScript */
    .orange-card .card-background-image,
    .beige-card .card-background-image,
    .green-medium-card .card-background-image {
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }

    .green-dark-card {
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    };

    /* Green dark card now uses background image only - no text content */
  }

  @media (max-width: 500px) {
    .ayla-care-benefits {
      padding: 20px 16px 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .section-container {
      width: 100%;
      gap: 12px;
    }

    .ayla-care-benefits .section-title {
      font-size: 32px;
      line-height: 1.1;
      letter-spacing: -0.96px;
      padding: 0 20px;
      max-width: 100%;
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
      text-align: center;
    }

    .section-subtitle {
      font-size: 16px;
      padding: 0 20px;
      text-align: center;
    }

    /* Mobile: Single column layout matching Figma design */
    .benefits-cards-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-width: 358px;
      width: 100%;
      margin: 0 auto;
    }

    .cards-row {
      display: contents;
    }

    .benefit-card {
      width: 100% !important;
      max-width: 358px !important;
      height: 400px;
      margin: 0;
      border-radius: 12px;
      overflow: hidden;
    }

    /* Specific card adjustments for mobile */
    .orange-card,
    .green-dark-card,
    .green-medium-card {
      width: 100% !important;
      max-width: 358px !important;
      height: 400px;
      grid-column: 1 !important;
      grid-row: auto !important;
      justify-self: stretch !important;
    }

    .beige-card {
      width: 100% !important;
      max-width: 358px !important;
      height: 510px;
      grid-column: 1 !important;
      grid-row: auto !important;
      justify-self: stretch !important;
    }

    .brgs-certificate-card,
    .quality-precision-card {
      width: 100%;
      height: 260px;
      margin: 0;
      padding: 24px;
    }

    /* Mobile background images */
    .orange-card .card-background-image,
    .beige-card .card-background-image,
    .green-medium-card .card-background-image {
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }

    .green-dark-card {
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }

    .card-title {
      font-size: 24px !important;
    }

    .card-description {
      font-size: 13px !important;
    };

    /* Green dark card now uses background image only - no text content */

    /* Feature ribbon styles moved to separate section */
  }

  @media (max-width: 400px) {
    .ayla-care-benefits .section-title {
      font-size: 28px;
      letter-spacing: -0.84px;
      max-width: 100%;
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
  }

  @media (max-width: 480px) {
    .ayla-care-benefits {
      padding: 16px 16px 32px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .ayla-care-benefits .section-title {
      font-size: 28px;
      letter-spacing: -0.84px;
      max-width: 100%;
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .card-icons {
      gap: 8px;
    }

    .icon-circle {
      width: 56px;
      height: 56px;
    }


    .quality-circle {
      width: 100px;
      height: 100px;
    };

    /* Green dark card now uses background image only - no text content */
  }
{% endstylesheet %}

{% schema %}
  {
    "name": "AYLA Care Benefits",
    "tag": "section",
    "class": "shopify-section-ayla-care-benefits",
    "settings": [
      {
        "type": "header",
        "content": "Section Settings"
      }, {
        "type": "text",
        "id": "main_title",
        "label": "Main Title",
        "default": "Dlaczego AYLA Care jest wyjątkowa?"
      }, {
        "type": "textarea",
        "id": "subtitle",
        "label": "Subtitle",
        "default": "Wszystko co najlepsze, w misji stworzenia jak najlepszych produktów dla psów i kotów"
      }
    ],
    "blocks": [
      {
        "type": "orange_card",
        "name": "🟠 Orange Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Card Title",
            "default": "Czysty skład, bez żadnych dodatków"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Card Description",
            "default": "Krótki skład i bez jakichkolwiek dodatków. Bez GMO, pszenicy, antybiotyków, konserwantów, barwników."
          },
          {
            "type": "image_picker",
            "id": "background_image",
            "label": "Desktop Background Image",
            "info": "Custom background image for desktop view (recommended: 756x800px for high resolution). If not set, uses default orange-frame-benefit.png"
          },
          {
            "type": "image_picker",
            "id": "tablet_background_image",
            "label": "Tablet Background Image",
            "info": "Custom background image for tablet view (recommended: 1472x800px for high resolution). If not set, uses desktop image."
          }, {
            "type": "image_picker",
            "id": "mobile_background_image",
            "label": "Mobile Background Image",
            "info": "Custom background image for mobile view (≤500px width, recommended: 358x400px for high resolution). If not set, uses tablet image."
          }, {
            "type": "image_picker",
            "id": "icon_2",
            "label": "Icon 2"
          }, {
            "type": "image_picker",
            "id": "icon_3",
            "label": "Icon 3"
          }, {
            "type": "image_picker",
            "id": "icon_4",
            "label": "Icon 4"
          }
        ]
      },
      {
        "type": "green_dark_card",
        "name": "🟢 Green Dark Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Card Title",
            "default": "Odpowiednie i bezpieczne na każdym etapie życia"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Card Description",
            "default": "Nasze produkty odpowiednie są do podania zarówno dla szczeniaków i seniorów, również dla psów z alergiami, dietami eliminacyjnymi i w procesie rekonwalestencji."
          },
          {
            "type": "image_picker",
            "id": "background_image",
            "label": "Desktop Background Image",
            "info": "Upload the complete background with pet images for desktop view (recommended: 1200x800px for high resolution)"
          },
          {
            "type": "image_picker",
            "id": "tablet_background_image",
            "label": "Tablet Background Image",
            "info": "Upload the complete background with pet images for tablet view (recommended: 1472x800px for high resolution). If not set, uses desktop image."
          }, {
            "type": "image_picker",
            "id": "mobile_background_image",
            "label": "Mobile Background Image",
            "info": "Upload the complete background with pet images for mobile view (recommended: 358x400px for high resolution). If not set, uses tablet image."
          }
        ]
      },
      {
        "type": "beige_card",
        "name": "🟤 Beige Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "label",
            "label": "Card Label",
            "default": "LIOFILIZACJA"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Card Title",
            "default": "Wszystkie wartości odżywcze\nzachowane w przysmakach"
          },
          {
            "type": "textarea",
            "id": "description",
            "label": "Card Description",
            "default": "Nasze produkty są liofilizowane, dzięki temu zachowują wszystkie wartości odżywcze, oraz długą trwałość bez zachowania konserwantów. Mogą być przechowywane poza lodówką."
          },
          {
            "type": "text",
            "id": "badge_text",
            "label": "Badge Text",
            "default": "FREEZE DRYING"
          }, {
            "type": "image_picker",
            "id": "background_image",
            "label": "Desktop Background Image",
            "info": "Custom background image for desktop view (recommended: 756x800px for high resolution). If not set, uses default beige-card-benefits.png"
          }, {
            "type": "image_picker",
            "id": "tablet_background_image",
            "label": "Tablet Background Image",
            "info": "Custom background image for tablet view (recommended: 1472x800px for high resolution). If not set, uses desktop image."
          }, {
            "type": "image_picker",
            "id": "mobile_background_image",
            "label": "Mobile Background Image",
            "info": "Custom background image for mobile view (≤500px width, recommended: 358x510px for high resolution). If not set, uses tablet image."
          }, {
            "type": "image_picker",
            "id": "treats_image_1",
            "label": "Treats Image 1"
          }, {
            "type": "image_picker",
            "id": "treats_image_2",
            "label": "Treats Image 2"
          }
        ]
      },
      {
        "type": "green_medium_card",
        "name": "🌿 Green Medium Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Card Title",
            "default": "Najwyższa jakość"
          },
          {
            "type": "text",
            "id": "grade_text",
            "label": "Grade Text",
            "default": "HUMAN GRADE"
          },
          {
            "type": "image_picker",
            "id": "background_image",
            "label": "Desktop Background Image",
            "info": "Custom background image for desktop view (recommended: 756x800px for high resolution). If not set, uses default light-green-card-benefit.png"
          },
          {
            "type": "image_picker",
            "id": "tablet_background_image",
            "label": "Tablet Background Image",
            "info": "Custom background image for tablet view (recommended: 1472x800px for high resolution). If not set, uses desktop image."
          }, {
            "type": "image_picker",
            "id": "mobile_background_image",
            "label": "Mobile Background Image",
            "info": "Custom background image for mobile view (≤500px width, recommended: 358x400px for high resolution). If not set, uses tablet image."
          }, {
            "type": "text",
            "id": "badge_1_text",
            "label": "Badge 1 Text",
            "default": "Bogate w witaminy"
          }, {
            "type": "image_picker",
            "id": "badge_1_icon",
            "label": "Badge 1 Icon"
          }, {
            "type": "text",
            "id": "badge_2_text",
            "label": "Badge 2 Text",
            "default": "Monobiałkowe"
          }, {
            "type": "image_picker",
            "id": "badge_2_icon",
            "label": "Badge 2 Icon"
          }, {
            "type": "text",
            "id": "badge_3_text",
            "label": "Badge 3 Text",
            "default": "Hipoalergiczny skład"
          }, {
            "type": "image_picker",
            "id": "badge_3_icon",
            "label": "Badge 3 Icon"
          }, {
            "type": "text",
            "id": "bottom_feature_1_text",
            "label": "Bottom Feature 1 Text"
          }, {
            "type": "image_picker",
            "id": "bottom_feature_1_icon",
            "label": "Bottom Feature 1 Icon"
          }, {
            "type": "text",
            "id": "bottom_feature_2_text",
            "label": "Bottom Feature 2 Text"
          }, {
            "type": "image_picker",
            "id": "bottom_feature_2_icon",
            "label": "Bottom Feature 2 Icon"
          }
        ]
      }, {
        "type": "brgs_certificate_card",
        "name": "🏆 BRGS Certificate Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Card Title",
            "default": "Tylko mięso z certyfikatem jakości"
          }, {
            "type": "textarea",
            "id": "description",
            "label": "Card Description",
            "default": "Nasz produkt zdobył certyfikat BRCGS, co potwierdza jego najwyższą jakość."
          }
        ]
      }, {
        "type": "quality_precision_card",
        "name": "✨ Quality Precision Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Card Title",
            "default": "Precyzja na każdym etapie produkcji"
          }, {
            "type": "textarea",
            "id": "description",
            "label": "Card Description",
            "default": "Od najlepszych dostawców, przez staranny proces produkcji i transport w kontrolowanej temperaturze. Wszystko po to, aby dostarczyć najlepszy produkt dla smakoszy wśród kotów i psów."
          }
        ]
      }, {
        "type": "feature_ribbon",
        "name": "🎗️ Features Ribbon",
        "limit": 1,
        "settings": [
          {
            "type": "header",
            "content": "Feature Items"
          },
          {
            "type": "text",
            "id": "feature_1_text",
            "label": "Feature 1 Text",
            "default": "Monobiałkowe"
          },
          {
            "type": "image_picker",
            "id": "feature_1_icon",
            "label": "Feature 1 Icon"
          },
          {
            "type": "text",
            "id": "feature_2_text",
            "label": "Feature 2 Text",
            "default": "Bez antybiotyków"
          }, {
            "type": "image_picker",
            "id": "feature_2_icon",
            "label": "Feature 2 Icon"
          }, {
            "type": "text",
            "id": "feature_3_text",
            "label": "Feature 3 Text",
            "default": "Bez konserwantów"
          }, {
            "type": "image_picker",
            "id": "feature_3_icon",
            "label": "Feature 3 Icon"
          }, {
            "type": "text",
            "id": "feature_4_text",
            "label": "Feature 4 Text",
            "default": "Bez barwników"
          }, {
            "type": "image_picker",
            "id": "feature_4_icon",
            "label": "Feature 4 Icon"
          }, {
            "type": "text",
            "id": "feature_5_text",
            "label": "Feature 5 Text",
            "default": "Jakość Human-Grade"
          }, {
            "type": "image_picker",
            "id": "feature_5_icon",
            "label": "Feature 5 Icon"
          }, {
            "type": "text",
            "id": "feature_6_text",
            "label": "Feature 6 Text",
            "default": "Hipoalergiczne"
          }, {
            "type": "image_picker",
            "id": "feature_6_icon",
            "label": "Feature 6 Icon"
          }, {
            "type": "text",
            "id": "feature_7_text",
            "label": "Feature 7 Text",
            "default": "Dla wrażliwych"
          }, {
            "type": "image_picker",
            "id": "feature_7_icon",
            "label": "Feature 7 Icon"
          }, {
            "type": "text",
            "id": "feature_8_text",
            "label": "Feature 8 Text"
          }, {
            "type": "image_picker",
            "id": "feature_8_icon",
            "label": "Feature 8 Icon"
          }, {
            "type": "text",
            "id": "feature_9_text",
            "label": "Feature 9 Text"
          }, {
            "type": "image_picker",
            "id": "feature_9_icon",
            "label": "Feature 9 Icon"
          }, {
            "type": "text",
            "id": "feature_10_text",
            "label": "Feature 10 Text"
          }, {
            "type": "image_picker",
            "id": "feature_10_icon",
            "label": "Feature 10 Icon"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "AYLA Care Benefits",
        "category": "Product",
        "blocks": [
          {
            "type": "orange_card",
            "settings": {
              "title": "Czysty skład, bez żadnych dodatków",
              "description": "Krótki skład i bez jakichkolwiek dodatków. Bez GMO, pszenicy, antybiotyków, konserwantów, barwników."
            }
          },
          {
            "type": "green_dark_card",
            "settings": {
              "title": "Odpowiednie i bezpieczne na każdym etapie życia",
              "description": "Nasze produkty odpowiednie są do podania zarówno dla szczeniaków i seniorów, również dla psów z alergiami, dietami eliminacyjnymi i w procesie rekonwalestencji."
            }
          },
          {
            "type": "beige_card",
            "settings": {
              "label": "LIOFILIZACJA",
              "title": "Wszystkie wartości odżywcze\nzachowane w przysmakach",
              "description": "Nasze produkty są liofilizowane, dzięki temu zachowują wszystkie wartości odżywcze, oraz długą trwałość bez zachowania konserwantów. Mogą być przechowywane poza lodówką.",
              "badge_text": "FREEZE DRYING"
            }
          },
          {
            "type": "green_medium_card",
            "settings": {
              "title": "Najwyższa jakość",
              "grade_text": "HUMAN GRADE",
              "badge_1_text": "Bogate w witaminy",
              "badge_2_text": "Monobiałkowe",
              "badge_3_text": "Hipoalergiczny skład"
            }
          }, {
            "type": "brgs_certificate_card",
            "settings": {
              "title": "Tylko mięso z certyfikatem jakości",
              "description": "Nasz produkt zdobył certyfikat BRCGS, co potwierdza jego najwyższą jakość."
            }
          }, {
            "type": "quality_precision_card",
            "settings": {
              "title": "Precyzja na każdym etapie produkcji",
              "description": "Od najlepszych dostawców, przez staranny proces produkcji i transport w kontrolowanej temperaturze. Wszystko po to, aby dostarczyć najlepszy produkt dla smakoszy wśród kotów i psów."
            }
          }, {
            "type": "feature_ribbon",
            "settings": {
              "feature_1_text": "Monobiałkowe",
              "feature_2_text": "Bez antybiotyków",
              "feature_3_text": "Bez konserwantów",
              "feature_4_text": "Bez barwników",
              "feature_5_text": "Jakość Human-Grade",
              "feature_6_text": "Hipoalergiczne",
              "feature_7_text": "Dla wrażliwych"
            }
          }
        ]
      }
    ]
  }
{% endschema %}