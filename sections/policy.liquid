{% comment %}
  Renders the store Privacy Policy.
  If a policy is configured in Admin > Settings > Policies, it uses shop.privacy_policy.
  Otherwise, it falls back to the page's own content so merchants can author a custom page.
{% endcomment %}

{%- liquid
  assign policy = shop.privacy_policy
-%}

<article class="policy policy--privacy">
  <header class="policy__header">
    <h1 class="policy__title">
      {%- if policy and policy.title != blank -%}
        {{ policy.title }}
      {%- else -%}
        {{ page.title | default: 'Privacy Policy' }}
      {%- endif -%}
    </h1>
  </header>

  <div class="policy__content rte">
    {%- if policy and policy.body != blank -%}
      {{ policy.body }}
    {%- else -%}
      {{ page.content }}
    {%- endif -%}
  </div>
</article>

{% schema %}
{
  "name": "Privacy policy",
  "settings": []
}
{% endschema %}

