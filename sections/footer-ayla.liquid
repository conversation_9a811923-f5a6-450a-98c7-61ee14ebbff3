{% comment %}
  AYLA footer section with features, navigation and branding
{% endcomment %}

<link rel="preconnect" href="https://fonts.googleapis.com">
<link
  rel="preconnect"
  href="https://fonts.gstatic.com"
  crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Jost:wght@400;500;600&display=swap" rel="stylesheet">

<footer class="ayla-footer">
  <div class="footer-container">
    <!-- Main Footer Content -->
    <div class="footer-content">
      <!-- Header Section with Logo and Social Media -->
      <div class="footer-header">
        <div class="footer-brand">
          <div class="footer-logo">
            {% render 'ayla-logo-svg' %}
          </div>
          <div class="footer-tagline">
            {{ section.settings.tagline | default: 'Naturalne przysmaki i żywność dla Psów i Kotów' }}
          </div>
        </div>
        <div class="footer-social">
          <div class="social-text">{{ section.settings.social_text | default: 'Obserwuj nas na' }}</div>
          <div class="social-icons">
            <a
              href="{{ section.settings.facebook_url | default: '#' }}"
              class="social-icon"
              target="_blank"
              rel="noopener">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M24 12C24 5.37258 18.6274 0 12 0S0 5.37258 0 12C0 17.9895 4.3882 22.954 10.125 23.8542V15.4688H7.07812V12H10.125V9.35625C10.125 6.34875 11.9166 4.6875 14.6576 4.6875C15.9701 4.6875 17.3438 4.92188 17.3438 4.92188V7.875H15.8306C14.34 7.875 13.875 8.80008 13.875 9.75V12H17.2031L16.6711 15.4688H13.875V23.8542C19.6118 22.954 24 17.9895 24 12Z" fill="white" />
              </svg>
            </a>
            <a
              href="{{ section.settings.instagram_url | default: '#' }}"
              class="social-icon"
              target="_blank"
              rel="noopener">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2.16094C15.2063 2.16094 15.5859 2.175 16.8469 2.23125C18.0188 2.28281 18.6516 2.47969 19.0734 2.64375C19.6313 2.85938 20.0344 3.12188 20.4516 3.53906C20.8734 3.96094 21.1313 4.35938 21.3469 4.91719C21.5109 5.33906 21.7078 5.97656 21.7594 7.14375C21.8156 8.40937 21.8297 8.78906 21.8297 12C21.8297 15.2109 21.8156 15.5906 21.7594 16.8562C21.7078 18.0234 21.5109 18.6609 21.3469 19.0828C21.1313 19.6406 20.8687 20.0437 20.4516 20.4609C20.0297 20.8828 19.6313 21.1406 19.0734 21.3562C18.6516 21.5203 18.0141 21.7172 16.8469 21.7688C15.5813 21.825 15.2016 21.8391 12 21.8391C8.79844 21.8391 8.41875 21.825 7.15313 21.7688C5.98594 21.7172 5.34844 21.5203 4.92656 21.3562C4.36875 21.1406 3.96563 20.8781 3.54844 20.4609C3.12656 20.0391 2.86875 19.6406 2.65313 19.0828C2.48906 18.6609 2.29219 18.0234 2.24063 16.8562C2.18438 15.5906 2.17031 15.2109 2.17031 12C2.17031 8.78906 2.18438 8.40937 2.24063 7.14375C2.29219 5.97656 2.48906 5.33906 2.65313 4.91719C2.86875 4.35938 3.13125 3.95625 3.54844 3.53906C3.97031 3.11719 4.36875 2.85938 4.92656 2.64375C5.34844 2.47969 5.98125 2.28281 7.15313 2.23125C8.41406 2.175 8.79375 2.16094 12 2.16094ZM12 0C8.74219 0 8.33438 0.0140625 7.05469 0.0703125C5.77969 0.126563 4.90313 0.332031 4.14375 0.628125C3.35156 0.9375 2.68125 1.34531 2.01563 2.01562C1.34531 2.68125 0.9375 3.35156 0.628125 4.13906C0.332031 4.90313 0.126563 5.77969 0.0703125 7.05469C0.0140625 8.33906 0 8.74219 0 12C0 15.2578 0.0140625 15.6609 0.0703125 16.9453C0.126563 18.2203 0.332031 19.0969 0.628125 19.8562C0.9375 20.6484 1.34531 21.3188 2.01563 21.9844C2.68125 22.65 3.35156 23.0625 4.13906 23.3719C4.90313 23.668 5.77969 23.8734 7.05469 23.9297C8.33438 23.9859 8.74219 24 12 24C15.2578 24 15.6609 23.9859 16.9453 23.9297C18.2203 23.8734 19.0969 23.668 19.8562 23.3719C20.6484 23.0625 21.3188 22.65 21.9844 21.9844C22.65 21.3188 23.0625 20.6484 23.3719 19.8609C23.668 19.0969 23.8734 18.2203 23.9297 16.9453C23.9859 15.6656 24 15.2578 24 12C24 8.74219 23.9859 8.33906 23.9297 7.05469C23.8734 5.77969 23.668 4.90313 23.3719 4.14375C23.0625 3.35156 22.6547 2.68125 21.9844 2.01562C21.3188 1.35 20.6484 0.9375 19.8609 0.628125C19.0969 0.332031 18.2203 0.126563 16.9453 0.0703125C15.6609 0.0140625 15.2578 0 12 0Z" fill="white" />
                <path d="M12 5.83594C8.59688 5.83594 5.83594 8.59688 5.83594 12C5.83594 15.4031 8.59688 18.1641 12 18.1641C15.4031 18.1641 18.1641 15.4031 18.1641 12C18.1641 8.59688 15.4031 5.83594 12 5.83594ZM12 15.9984C9.79219 15.9984 8.00156 14.2078 8.00156 12C8.00156 9.79219 9.79219 8.00156 12 8.00156C14.2078 8.00156 15.9984 9.79219 15.9984 12C15.9984 14.2078 14.2078 15.9984 12 15.9984Z" fill="white" />
                <path d="M19.8516 5.59375C19.8516 6.39844 19.1953 7.05469 18.3906 7.05469C17.5859 7.05469 16.9297 6.39844 16.9297 5.59375C16.9297 4.78906 17.5859 4.13281 18.3906 4.13281C19.1953 4.13281 19.8516 4.78906 19.8516 5.59375Z" fill="white" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Features Section -->
      <div class="footer-features">
        <div class="feature-item">
          <div class="feature-icon">
            <img
              src="{{ 'icon-opakowanie.png' | asset_url }}"
              alt="Darmowa dostawa"
              width="64"
              height="64">
          </div>
          <div class="feature-content">
            <div class="feature-title">{{ section.settings.feature1_title | default: 'Darmowa dostawa' }}</div>
            <div class="feature-subtitle">{{ section.settings.feature1_subtitle | default: 'od 149zł' }}</div>
          </div>
        </div>

        <div class="feature-divider"></div>

        <div class="feature-item">
          <div class="feature-icon">
            <img
              src="{{ 'faste-delivery-icon.svg' | asset_url }}"
              alt="Szybka dostawa"
              width="64"
              height="64">
          </div>
          <div class="feature-content">
            <div class="feature-title">{{ section.settings.feature2_title | default: 'Szybka dostawa' }}</div>
            <div class="feature-subtitle">{{ section.settings.feature2_subtitle | default: 'Inpost, DHL' }}</div>
          </div>
        </div>

        <div class="feature-divider"></div>

        <div class="feature-item">
          <div class="feature-icon">
            <img
              src="{{ 'ekologiczne-icon.svg' | asset_url }}"
              alt="Ekologicznie zapakowane"
              width="64"
              height="64">
          </div>
          <div class="feature-content">
            <div class="feature-title">{{ section.settings.feature3_title | default: 'Ekologicznie zapakowane' }}</div>
            <div class="feature-subtitle">{{ section.settings.feature3_subtitle | default: 'W 100% do recyklingu' }}</div>
          </div>
        </div>

        <div class="feature-divider"></div>

        <div class="feature-item">
          <div class="feature-icon">
            <img
              src="{{ 'payment-icons.svg' | asset_url }}"
              alt="Bezpieczne płatności"
              width="64"
              height="64">
          </div>
          <div class="feature-content">
            <div class="feature-title">{{ section.settings.feature4_title | default: 'Bezpieczne płatności' }}</div>
            <div class="feature-subtitle">{{ section.settings.feature4_subtitle | default: 'BLIK, Przelewy 24 i więcej' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Section -->
    <div class="footer-contact">
      <button class="contact-button">
        {{ section.settings.contact_button_text | default: 'SKONTAKTUJ SIĘ' }}
      </button>
      <div class="contact-info">
        <a href="tel:{{ section.settings.phone | default: '+48 506 444 646' }}" class="contact-link">
          {{ section.settings.phone | default: '+48 506 444 646' }}
        </a>
        <a href="mailto:{{ section.settings.email | default: '<EMAIL>' }}" class="contact-link">
          {{ section.settings.email | default: '<EMAIL>' }}
        </a>
      </div>
    </div>

    <!-- Navigation Links -->
    <div class="footer-navigation">
      <!-- Products Column -->
      <div class="nav-column">
        <h3 class="nav-title">{{ section.settings.products_title | default: 'PRODUKTY' }}</h3>
        <div class="nav-links">
          <a href="/collections/all" class="nav-link">Wszystkie produkty</a>
          <a href="/collections/dogs" class="nav-link">Produkty dla Psa</a>
          <a href="/collections/cats" class="nav-link">Produkty dla Kota</a>
        </div>
        <div class="nav-divider"></div>
        <div class="nav-links">
          <a href="/collections/ayla-delight" class="nav-link">AYLA Delight</a>
          <a href="/collections/ayla-help" class="nav-link">AYLA Help</a>
          <a href="/collections/ayla-rescue" class="nav-link">AYLA Rescue</a>
        </div>
      </div>

      <!-- Shop Column -->
      <div class="nav-column">
        <h3 class="nav-title">{{ section.settings.shop_title | default: 'SKLEP' }}</h3>
        <div class="nav-links">
          <a href="/pages/shipping" class="nav-link">Wysyłka i Płatność</a>
          <a href="/pages/faq" class="nav-link">FAQ - Często zadawane pytania</a>
          <a href="/pages/returns" class="nav-link">Zwroty</a>
        </div>
        <div class="nav-divider"></div>
        <div class="nav-links">
          <a href="/account" class="nav-link">Moje Konto</a>
          <a href="/account/orders" class="nav-link">Historia Zamówień</a>
          <a href="/pages/order-tracking" class="nav-link">Śledź zamówienie</a>
          <a href="/account/recover" class="nav-link">Nie pamiętam hasła</a>
        </div>
      </div>

      <!-- Cooperation Column -->
      <div class="nav-column">
        <h3 class="nav-title">{{ section.settings.cooperation_title | default: 'WSPÓŁPRACA' }}</h3>
        <div class="nav-links">
          <a href="/pages/b2b" class="nav-link">Współpraca B2B</a>
          <a href="/pages/veterinarians" class="nav-link">Dla lekarzy weterynarii</a>
        </div>
      </div>

      <!-- About Us Column -->
      <div class="nav-column">
        <h3 class="nav-title">{{ section.settings.about_title | default: 'O NAS' }}</h3>
        <div class="nav-links">
          <a href="/pages/team" class="nav-link">Zespół</a>
          <a href="/pages/history" class="nav-link">Historia</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Pet Silhouettes -->
  <div class="footer-pets">
    <img
      src="{{ 'cot-and-dog-footer-icon.png' | asset_url }}"
      alt="Dog and Cat"
      width="150"
      height="auto">
  </div>
</footer>

{% stylesheet %}
  :root  {
    --primary-color: #2d4f40;
    --background-color: #f9f9f4;
    --border-color: #e0e0c8;
    --white: #fff;
  }

  .ayla-footer {
    background-color: var(--background-color);
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    overflow: hidden;
    border-bottom: 4px solid var(--primary-color);
  }

  .footer-container {
    margin: 0 auto;
    padding: 56px 120px 40px;
    position: relative;
  }

  .footer-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 1200px;
    height: 222px;
    max-width: 100%;
    margin: 0 auto;
  }

  /* Desktop Responsiveness - only for very large screens */
  @media (min-width: 1441px) {
    .footer-container {
      padding: 56px 120px 40px;
    }

    .footer-content {
      width: 1200px;
      height: 222px;
    }
  }

  /* Header Section */
  .footer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .footer-brand {
    display: flex;
    align-items: center;
    gap: 40px;
  }

  .footer-logo .ayla-logo-svg {
    width: 103px;
    height: 62px;
    color: var(--primary-color);
    display: block;
  }

  .footer-tagline {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.4;
    letter-spacing: -0.4px;
    color: var(--primary-color);
  }

  .footer-social {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .social-text {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 18px;
    line-height: 1.1;
    letter-spacing: -0.54px;
    color: var(--primary-color);
  }

  .social-icons {
    display: flex;
    gap: 8px;
  }

  .social-icon {
    background-color: var(--primary-color);
    border-radius: 24px;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    transition: opacity 0.2s ease;
  }

  .social-icon svg {
    width: 24px;
    height: 24px;
  }

  .social-icon:hover {
    opacity: 0.8;
  }

  /* Features Section */
  .footer-features {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    width: 100%;
    box-sizing: border-box;
    gap: 0;
  }

  .feature-item {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 5px;
    position: relative;
    box-sizing: border-box;
    padding: 0 20px;
    justify-content: center;
    width: auto !important;
    height: auto !important;
    min-width: 0 !important;
    min-height: 0 !important;
    max-width: none !important;
    max-height: none !important;
  }

  .ayla-footer .feature-icon {
    width: 64px !important;
    height: 64px !important;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24px;
  }

  .ayla-footer .feature-icon img {
    width: 64px !important;
    height: 64px !important;
    object-fit: contain;
  }

  .ayla-footer .feature-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2px;
    padding-top: 11px;
    padding-bottom: 11px;
    height: auto;
    min-height: 42px;
    justify-content: center;
    flex: 1 1 auto;
    min-width: 0;
    width: auto;
    text-align: center;
  }

  .ayla-footer .feature-title {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 18px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: -0.54px;
    color: var(--primary-color);
    height: auto;
    min-height: 40px;
    display: block !important;
    width: auto;
  }

  .ayla-footer .feature-subtitle {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 18px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.54px;
    color: var(--primary-color);
    height: auto;
    min-height: 20px;
    display: block !important;
    width: auto;
  }

  .feature-divider {
    width: 1px;
    height: 48px;
    background-color: var(--border-color);
    border-radius: 1px;
    flex-shrink: 0;
  }

  /* Contact Section */
  .footer-contact {
    display: flex;
    align-items: center;
    gap: 24px;
    padding-top: 40px;
  }

  .contact-button {
    border: 1px solid var(--primary-color);
    border-radius: 44px;
    padding: 12px 24px;
    background: transparent;
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.56px;
    text-transform: uppercase;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .contact-button:hover {
    background-color: var(--primary-color);
    color: var(--white);
  }

  .contact-info {
    display: flex;
    gap: 24px;
  }

  .contact-link {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 16px;
    line-height: 1.4;
    letter-spacing: -0.32px;
    color: var(--primary-color);
    text-decoration: none;
    transition: opacity 0.2s ease;
  }

  .contact-link:hover {
    opacity: 0.7;
  }

  /* Navigation Section */
  .footer-navigation {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    margin-top: 40px;
  }

  .nav-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .nav-title {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 1.4;
    letter-spacing: 1.6px;
    text-transform: uppercase;
    color: var(--primary-color);
    margin: 0;
  }

  .nav-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .nav-link {
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 16px;
    line-height: 1.4;
    letter-spacing: -0.32px;
    color: var(--primary-color);
    text-decoration: none;
    transition: opacity 0.2s ease;
  }

  .nav-link:hover {
    opacity: 0.7;
  }

  .nav-divider {
    width: 100%;
    height: 1px;
    background-color: var(--border-color);
    border-radius: 1px;
  }

  /* Pet Silhouettes */
  .footer-pets {
    position: absolute;
    bottom: 0;
    right: calc(50% - 600px + 120px);
    pointer-events: none;
  }

  .footer-pets img {
    display: block;
    width: auto;
    height: 150px;
    object-fit: contain;
  }

  /* Tablet Responsiveness - Large Tablets */
  @media (max-width: 1440px) and (min-width: 1000px) {
    .footer-container {
      padding: 24px 40px;
    }

    .footer-content {
      width: 100%;
      height: auto;
      gap: 32px;
    }

    .footer-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 24px;
    }

    .footer-brand {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .footer-tagline {
      font-size: 18px;
    }

    .footer-social {
      align-self: flex-start;
    }

    .footer-features {
      padding: 24px 0;
      height: auto;
      gap: 0;
    }

    .feature-item {
      padding: 0 16px;
      flex: 1;
      justify-content: center;
    }

    .footer-contact {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding-top: 32px;
    }

    .contact-info {
      flex-direction: column;
      gap: 12px;
    }

    .footer-navigation {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
      margin-top: 32px;
    }

    .footer-pets {
      display: block;
      position: absolute;
      bottom: 0;
      right: 20px;
      pointer-events: none;
    }

    .footer-pets img {
      width: auto;
      height: 120px;
      object-fit: contain;
    }
  }

  /* Tablet Responsiveness - Small Tablets (Icons Above Text) */
  @media (max-width: 999px) and (min-width: 769px) {
    .footer-container {
      padding: 24px 40px;
    }

    .footer-content {
      width: 100%;
      height: auto;
      gap: 32px;
    }

    .footer-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 24px;
    }

    .footer-brand {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .footer-tagline {
      font-size: 18px;
    }

    .footer-social {
      align-self: flex-start;
    }

    .footer-features {
      padding: 24px 0;
      height: auto;
      gap: 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: stretch;
    }

    .feature-item {
      flex-direction: column;
      align-items: flex-start;
      padding: 8px 20px;
      flex: 1;
      min-width: 0;
      justify-content: flex-start;
      height: auto;
      gap: 10px;
      box-sizing: border-box;
      width: auto !important;
      min-height: 0 !important;
      max-width: none !important;
      max-height: none !important;
    }

    .ayla-footer .feature-icon {
      margin-right: 0;
      margin-bottom: 8px;
    }

    .ayla-footer .feature-content {
      text-align: left;
      padding-top: 0;
      padding-bottom: 0;
      gap: 6px;
      display: flex !important;
      flex-direction: column !important;
      align-items: flex-start;
      width: 100%;
      max-width: none;
    }

    .ayla-footer .feature-title {
      min-height: 40px;
      display: flex;
      align-items: flex-start;
    }

    .footer-contact {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding-top: 32px;
    }

    .contact-info {
      flex-direction: column;
      gap: 12px;
    }

    .footer-navigation {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
      margin-top: 32px;
    }

    .footer-pets {
      display: block;
      position: absolute;
      bottom: 0;
      right: 20px;
      pointer-events: none;
    }

    .footer-pets img {
      width: auto;
      height: 120px;
      object-fit: contain;
    }
  }

  /* Responsive adjustments for smaller tablets */
  @media (max-width: 850px) and (min-width: 769px) {
    .footer-features {
      padding: 20px 0;
    }

    .feature-item {
      padding: 6px 16px;
      gap: 6px;
      align-items: flex-start;
    }

    .ayla-footer .feature-icon {
      margin-bottom: 6px;
    }

    .ayla-footer .feature-content {
      text-align: left;
      align-items: flex-start;
      gap: 4px;
    }

    .ayla-footer .feature-title {
      font-size: 16px;
      min-height: 36px;
    }

    .ayla-footer .feature-subtitle {
      font-size: 16px;
    }
  }

  /* Mobile Layout - Vertical Stack */
  @media (max-width: 850px) {
    .footer-container {
      padding: 16px;
    }

    .footer-content {
      width: 100%;
      height: auto;
      gap: 24px;
    }

    .footer-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .footer-brand {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .footer-tagline {
      font-size: 16px;
    }

    .footer-social {
      align-self: flex-start;
    }

    .footer-features {
      display: flex;
      flex-direction: column;
      padding: 12px 0;
      height: auto;
      min-height: auto;
      gap: 0;
      align-items: center;
    }

    .feature-item {
      flex-direction: row;
      align-items: flex-start;
      padding: 12px 0;
      flex: none;
      min-height: auto;
      gap: 12px;
      width: 100%;
      justify-content: center;
      height: auto !important;
      min-width: 0 !important;
      max-width: none !important;
      max-height: none !important;
    }

    .ayla-footer .feature-icon {
      margin-right: 12px;
      margin-bottom: 0;
      flex-shrink: 0;
      margin-top: 2px;
    }

    .ayla-footer .feature-content {
      text-align: center;
      align-items: center;
      gap: 2px;
      flex: 1;
      min-width: 0;
    }

    .ayla-footer .feature-title {
      min-height: auto;
      font-size: 16px;
    }

    .ayla-footer .feature-subtitle {
      font-size: 16px;
    }

    .feature-divider {
      width: 100%;
      height: 1px;
      margin: 4px 0;
      background-color: #e0e0c8;
    }

    .footer-contact {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      padding-top: 20px;
    }

    .contact-info {
      flex-direction: column;
      gap: 8px;
    }

    .footer-navigation {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      margin-top: 24px;
    }

    .footer-pets {
      display: block;
      position: absolute;
      bottom: 0;
      right: 16px;
      pointer-events: none;
    }

    .footer-pets img {
      width: auto;
      height: 100px;
      object-fit: contain;
    }
  }

  /* Mobile Responsiveness */
  @media (max-width: 1200px) {
    .footer-container {
      padding: 40px 20px;
    }

    .footer-brand {
      gap: 24px;
    }

    .footer-tagline {
      font-size: 18px;
    }
  }


  @media (max-width: 480px) {
    .footer-navigation {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
{% endstylesheet %}

{% schema %}
  {
    "name": "Footer AYLA",
    "settings": [
      {
        "type": "text",
        "id": "tagline",
        "label": "Footer Tagline",
        "default": "Naturalne przysmaki i żywność dla Psów i Kotów"
      },
      {
        "type": "text",
        "id": "social_text",
        "label": "Social Media Text",
        "default": "Obserwuj nas na"
      },
      {
        "type": "url",
        "id": "facebook_url",
        "label": "Facebook URL"
      },
      {
        "type": "url",
        "id": "instagram_url",
        "label": "Instagram URL"
      }, {
        "type": "header",
        "content": "Features Section"
      }, {
        "type": "text",
        "id": "feature1_title",
        "label": "Feature 1 Title",
        "default": "Darmowa dostawa"
      }, {
        "type": "text",
        "id": "feature1_subtitle",
        "label": "Feature 1 Subtitle",
        "default": "od 149zł"
      }, {
        "type": "text",
        "id": "feature2_title",
        "label": "Feature 2 Title",
        "default": "Szybka dostawa"
      }, {
        "type": "text",
        "id": "feature2_subtitle",
        "label": "Feature 2 Subtitle",
        "default": "Inpost, DHL"
      }, {
        "type": "text",
        "id": "feature3_title",
        "label": "Feature 3 Title",
        "default": "Ekologicznie zapakowane"
      }, {
        "type": "text",
        "id": "feature3_subtitle",
        "label": "Feature 3 Subtitle",
        "default": "W 100% do recyklingu"
      }, {
        "type": "text",
        "id": "feature4_title",
        "label": "Feature 4 Title",
        "default": "Bezpieczne płatności"
      }, {
        "type": "text",
        "id": "feature4_subtitle",
        "label": "Feature 4 Subtitle",
        "default": "BLIK, Przelewy 24 i więcej"
      }, {
        "type": "header",
        "content": "Contact Information"
      }, {
        "type": "text",
        "id": "contact_button_text",
        "label": "Contact Button Text",
        "default": "SKONTAKTUJ SIĘ"
      }, {
        "type": "text",
        "id": "phone",
        "label": "Phone Number",
        "default": "+48 506 444 646"
      }, {
        "type": "text",
        "id": "email",
        "label": "Email Address",
        "default": "<EMAIL>"
      }, {
        "type": "header",
        "content": "Navigation Titles"
      }, {
        "type": "text",
        "id": "products_title",
        "label": "Products Column Title",
        "default": "PRODUKTY"
      }, {
        "type": "text",
        "id": "shop_title",
        "label": "Shop Column Title",
        "default": "SKLEP"
      }, {
        "type": "text",
        "id": "cooperation_title",
        "label": "Cooperation Column Title",
        "default": "WSPÓŁPRACA"
      }, {
        "type": "text",
        "id": "about_title",
        "label": "About Column Title",
        "default": "O NAS"
      }
    ],
    "presets": [
      {
        "name": "Footer AYLA"
      }
    ]
  }
{% endschema %}