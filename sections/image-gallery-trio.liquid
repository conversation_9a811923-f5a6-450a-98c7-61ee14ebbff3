{% comment %}
  Image Gallery Trio Section - Three Image Display
  Displays three images in a horizontal row with rounded corners
  Matches Figma design with exact dimensions and spacing
{% endcomment %}

<image-gallery-trio class="image-gallery-trio">
  <div class="gallery-container">
    <div class="image-wrapper">
      <img src="{{ 'ocena-485.png' | asset_url }}" 
           alt="Ocean Product 485" 
           class="gallery-image"
           loading="lazy" 
           width="378" 
           height="338" />
    </div>
    <div class="image-wrapper">
      <img src="{{ 'ocena100.png' | asset_url }}" 
           alt="Ocean Product 100" 
           class="gallery-image"
           loading="lazy" 
           width="378" 
           height="338" />
    </div>
    <div class="image-wrapper">
      <img src="{{ 'ocena75.png' | asset_url }}" 
           alt="Ocean Product 75" 
           class="gallery-image"
           loading="lazy" 
           width="378" 
           height="338" />
    </div>
  </div>
</image-gallery-trio>

{% stylesheet %}
  .image-gallery-trio {
    --primary-color-p0: #FFF;
    --primary-color-p600: #38634F;
    
    background: var(--primary-color-p0);
    padding: 0 120px 64px 120px;
    width: 100%;
    box-sizing: border-box;
  }

  .gallery-container {
    display: flex;
    align-items: flex-start;
    gap: 32px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  .image-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
    flex-shrink: 0;
  }

  .gallery-image {
    width: 378px;
    height: 338px;
    border-radius: 12px;
    object-fit: cover;
    display: block;
  }

  /* Responsive design */
  @media (max-width: 1280px) {
    .image-gallery-trio {
      padding: 0 80px 48px 80px;
    }
    
    .gallery-container {
      gap: 24px;
    }
    
    .gallery-image {
      width: 320px;
      height: 286px;
    }
  }

  @media (max-width: 1024px) {
    .image-gallery-trio {
      padding: 0 40px 32px 40px;
    }
    
    .gallery-container {
      gap: 20px;
    }
    
    .gallery-image {
      width: 280px;
      height: 250px;
    }
  }

  @media (max-width: 768px) {
    .image-gallery-trio {
      padding: 0 20px 24px 20px;
    }
    
    .gallery-container {
      flex-direction: column;
      gap: 16px;
      align-items: center;
    }
    
    .image-wrapper {
      width: 100%;
      max-width: 378px;
    }
    
    .gallery-image {
      width: 100%;
      height: 300px;
    }
  }

  @media (max-width: 480px) {
    .gallery-image {
      height: 250px;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Image Gallery Trio",
  "class": "section",
  "settings": [],
  "presets": [
    {
      "name": "Image Gallery Trio"
    }
  ]
}
{% endschema %}
