{% comment %}
  This section is used in the list-collections template to render a list of
  collections.

  https://shopify.dev/docs/storefronts/themes/architecture/templates/collection
{% endcomment %}

<h1>{{ 'collections.title' | t }}</h1>

<div
  class="collections {{ section.settings.grid_item_width }}"
  style="--grid-gap: {{ section.settings.grid_gap }}px"
>
  {% for collection in collections %}
    <a class="collection-card" href="{{ collection.url }}">
      {% if collection.featured_image %}
        {% render 'image',
          class: 'collection-card__image',
          image: collection.featured_image,
          width: 600,
          height: 600,
          crop: 'center'
        %}
      {% endif %}

      <div class="collection-card__content">
        <p>{{ collection.title }}</p>

        {% if collection.description %}
          <p>{{ collection.description | strip_html | truncatewords: 15 }}</p>
        {% endif %}
      </div>
    </a>
  {% endfor %}
</div>

{% stylesheet %}
  .collections {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(min(var(--collection-card-size), 100%), 1fr));
    gap: var(--grid-gap);
  }
  .collections--compact {
    --collection-card-size: 160px;
  }
  .collections--full {
    --collection-card-size: 280px;
  }
  .collection-card {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:general.collections_grid",
  "settings": [
    {
      "type": "select",
      "id": "grid_item_width",
      "label": "t:labels.grid_item_width",
      "options": [
        { "value": "collections--compact", "label": "t:options.size.small" },
        { "value": "collections--full", "label": "t:options.size.large" }
      ],
      "default": "collections--full"
    },
    {
      "type": "range",
      "id": "grid_gap",
      "label": "t:labels.grid_gap",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "default": 10
    }
  ],
  "presets": [{ "name": "t:general.collections_grid" }]
}
{% endschema %}
