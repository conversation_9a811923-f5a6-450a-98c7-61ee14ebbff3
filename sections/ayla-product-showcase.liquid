{% comment %}
  AYLA Product Showcase Section - Figma Design Implementation
  "Liderzy wśród kocich i psich smakoszy!" - Product showcase with 3 bestseller items
  Matches Figma node-id 4809-18668 exactly with proper colors, typography, and layout
{% endcomment %}

<ayla-product-showcase class="ayla-product-showcase">
  <div class="section-container">
    {% comment %} Main heading {% endcomment %}
    <div class="section-header">
      <h2 class="section-title">
        {{ section.settings.main_title | default: 'Liderzy wśród kocich i psich smakoszy!' }}
      </h2>
    </div>

    {% comment %} Product showcase container {% endcomment %}
    <div class="products-showcase-container">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'product_showcase' %}
            <div class="product-showcase-card" {{ block.shopify_attributes }}>
              {% comment %} Product image container - Single background image {% endcomment %}
              <div
                class="product-image-container"
                {% if block.settings.background_image %}
                style="background-image: url('{{ block.settings.background_image | image_url: width: 400 }}'); background-size: cover; background-position: center; background-repeat: no-repeat;"
                {% endif %}>

                {% comment %} Bestseller badge {% endcomment %}
                {% if block.settings.show_bestseller %}
                  <div class="bestseller-badge">
                    <span class="badge-text">{{ block.settings.badge_text | default: 'BESTSELLER' }}</span>
                  </div>
                {% endif %}

                {% comment %} Animal type indicator {% endcomment %}
                {% if block.settings.animal_type %}
                  <div class="animal-type-indicator">
                    <span class="animal-text">{{ block.settings.animal_type | upcase }}</span>
                    {% if block.settings.animal_icon %}
                      <img
                        src="{{ block.settings.animal_icon | image_url: width: 24 }}"
                        alt="{{ block.settings.animal_type }}"
                        class="animal-icon"
                        width="24"
                        height="24">
                    {% else %}
                      {% comment %} Default animal icons {% endcomment %}
                      {% if block.settings.animal_type contains 'kot' %}
                        <svg
                          class="animal-icon cat-icon"
                          width="16"
                          height="24"
                          viewBox="0 0 16 24"
                          fill="none">
                          <path d="M8 0C3.6 0 0 3.6 0 8v8c0 4.4 3.6 8 8 8s8-4.4 8-8V8c0-4.4-3.6-8-8-8z" fill="currentColor" />
                        </svg>
                      {% else %}
                        <svg
                          class="animal-icon dog-icon"
                          width="15"
                          height="24"
                          viewBox="0 0 15 24"
                          fill="none">
                          <path d="M7.5 0C3.3 0 0 3.3 0 7.5v9c0 4.1 3.3 7.5 7.5 7.5s7.5-3.4 7.5-7.5v-9C15 3.3 11.7 0 7.5 0z" fill="currentColor" />
                        </svg>
                      {% endif %}
                    {% endif %}
                  </div>
                {% endif %}
              </div>

              {% comment %} Product text section - SEPARATE from buy button {% endcomment %}
              <div class="product-text">
                <h3 class="product-title">{{ block.settings.product_title | default: 'Liofilizowane przysmaki dla psa z filetu z piersi kurczaka' }}</h3>
                <p class="product-description">{{ block.settings.product_description | default: 'Naturalne liofilizowane przysmaki' }}</p>
              </div>

              {% comment %} Buy button section - SEPARATE from text {% endcomment %}
              <div class="product-purchase">
                <a href="{{ block.settings.product_url | default: '#' }}" class="buy-button">
                  <div class="buy-content">
                    <div class="buy-icon">
                      <svg
                        width="28"
                        height="28"
                        viewBox="0 0 28 28"
                        fill="none">
                        <path
                          d="M6 8H22C22.55 8 23 8.45 23 9V20C23 21.1 22.1 22 21 22H7C5.9 22 5 21.1 5 20V9C5 8.45 5.45 8 6 8Z"
                          stroke="currentColor"
                          stroke-width="1"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          fill="none" />
                        <path
                          d="M9.5 8V7.5C9.5 5 11.5 3 14 3C16.5 3 18.5 5 18.5 7.5V8"
                          stroke="currentColor"
                          stroke-width="1"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                        <circle
                          cx="9.5"
                          cy="12"
                          r="0.5"
                          fill="currentColor" />
                        <circle
                          cx="18.5"
                          cy="12"
                          r="0.5"
                          fill="currentColor" />
                      </svg>
                    </div>
                    <span class="buy-text">{{ block.settings.buy_text | default: 'Dodaj do koszyka' }}</span>
                  </div>
                  <span class="product-price">{{ block.settings.product_price | default: '23,90 zł' }}</span>
                </a>
              </div>
            </div>
        {% endcase %}
      {% endfor %}
    </div>
  </div>
</ayla-product-showcase>

{% stylesheet %}
  .ayla-product-showcase {
    --primary-p700: #2d4f40;
    --primary-p0: #ffffff;
    --primary-p500: #4c7d67;
    --bezowe-b50: #f9f9f4;
    --bezowe-b100: #f0f0e4;
    --text-primary: #212121;

    background: var(--primary-p0);
    padding: 64px 120px;
    box-sizing: border-box;
    overflow: visible;
    position: relative;
    min-height: 666px;
  }

  .section-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 32px;
    width: 100%;
    max-width: 1320px;
    margin: 0 auto;
  }

  /* Section Header */
  .section-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
  }

  .ayla-product-showcase .section-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 48px;
    line-height: 1.1;
    letter-spacing: -1.44px;
    color: var(--primary-p700);
    text-align: center;
    margin: 0;
    white-space: nowrap;
  }

  /* Products Showcase Container */
  .products-showcase-container {
    display: flex;
    flex-direction: row;
    gap: 40px;
    align-items: flex-start;
    width: 100%;
    max-width: 1199px;
    min-height: 581px;
    height: auto;
    padding: 16px;
  }

  /* Tablet responsive container with horizontal scroll */
  .products-showcase-container.tablet-scroll {
    overflow-x: auto;
    overflow-y: visible;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--bezowe-b200) transparent;
    padding-bottom: 20px;
  }

  .products-showcase-container.tablet-scroll::-webkit-scrollbar {
    height: 8px;
  }

  .products-showcase-container.tablet-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .products-showcase-container.tablet-scroll::-webkit-scrollbar-thumb {
    background: var(--bezowe-b200);
    border-radius: 4px;
  }

  .products-showcase-container.tablet-scroll::-webkit-scrollbar-thumb:hover {
    background: var(--primary-p400);
  }

  /* Product Showcase Card */
  .product-showcase-card {
    flex: 0 0 379px;
    width: 379px;
    height: 581px;
    display: grid;
    grid-template-rows: 380px 24px auto 24px 60px;
    grid-template-areas: "image" "gap1" "text" "gap2" "button";
    align-items: start;
    overflow: visible;
    box-sizing: border-box;
  }

  /* Product Image Container */
  .product-image-container {
    grid-area: image;
    width: 379px;
    height: 380px;
    background: var(--bezowe-b50);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
  }


  /* Bestseller Badge */
  .bestseller-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background: var(--primary-p500);
    border-radius: 24px;
    padding: 8px 16px;
    z-index: 10;
  }

  .badge-text {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.4;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    color: var(--primary-p0);
  }

  /* Animal Type Indicator */
  .animal-type-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    background: var(--bezowe-b100);
    border-top-right-radius: 16px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 10;
  }

  .animal-text {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    color: var(--primary-p700);
  }

  .animal-icon {
    width: 16px;
    height: 24px;
    color: var(--primary-p700);
  }

  /* Product Text Section */
  .product-text {
    grid-area: text;
    display: flex;
    flex-direction: column;
    gap: 11px;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
  }

  .ayla-product-showcase .product-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 28px !important;
    line-height: 1.1;
    letter-spacing: -0.84px;
    color: var(--primary-p700);
    margin: 0;
    width: min-content;
    min-width: 100%;
  }

  .product-description {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: -0.28px;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
  }

  /* Product Purchase Section */
  .product-purchase {
    grid-area: button;
  }

  /* Buy Button */
  .buy-button {
    display: flex;
    padding: 16px 32px;
    justify-content: center;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    width: 379px;
    height: 60px;
    border: 1px solid var(--bezowe-b100);
    border-radius: 44px;
    text-decoration: none;
    transition: all 0.2s ease;
    box-sizing: border-box;
  }

  .buy-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .buy-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    color: var(--primary-p700);
  }

  .buy-text {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.25;
    letter-spacing: 0.64px;
    text-transform: uppercase;
    color: var(--primary-p700);
  }

  .product-price {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 18px;
    line-height: 1.1;
    letter-spacing: -0.54px;
    color: var(--primary-p700);
  }

  .buy-button:hover {
    background: var(--bezowe-b100);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(45, 79, 64, 0.1);
  }

  /* Desktop to Tablet Transition (1440px-1250px) - Even distribution */
  @media (max-width: 1439px) and (min-width: 1250px) {
    .ayla-product-showcase {
      padding: 48px 0;
      min-height: auto;
      height: auto;
      overflow: visible;
    }

    .section-container {
      max-width: 100%;
      height: auto;
      padding: 0 60px;
    }

    .ayla-product-showcase .section-title {
      font-size: 42px;
      white-space: normal;
      text-align: center;
    }

    .products-showcase-container {
      gap: 20px;
      max-width: none;
      width: 100%;
      overflow: visible;
      padding: 16px;
      justify-content: space-between;
      height: auto;
      margin: 0;
    }

    .product-showcase-card {
      flex: 0 0 calc(33.333% - 14px);
      width: calc(33.333% - 14px);
      min-width: 0;
      height: 525px;
      grid-template-rows: 358px 12px auto 12px 44px;
    }

    .product-image-container {
      width: 100%;
      height: 358px;
    }

    .buy-button {
      width: 100%;
      height: 44px;
    }

    .ayla-product-showcase .product-title {
      color: var(--primary-p700) !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 20px !important;
      font-style: normal !important;
      font-weight: 500 !important;
      line-height: 110% !important;
      letter-spacing: -0.6px !important;
    }

    .ayla-product-showcase .product-description {
      color: #212121 !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 14px !important;
      font-style: normal !important;
      font-weight: 400 !important;
      line-height: 140% !important;
      letter-spacing: -0.28px !important;
    }
  }

  /* Tablet View - Responsive with horizontal scroll (below 1250px) */
  @media (max-width: 1249px) {
    .ayla-product-showcase {
      padding: 48px 16px;
      min-height: auto;
      height: auto;
      overflow: visible;
    }

    .section-container {
      max-width: 100%;
      height: auto;
      padding: 0;
    }

    .ayla-product-showcase .section-title {
      font-size: 42px;
      white-space: normal;
      text-align: center;
    }

    .products-showcase-container {
      gap: 16px;
      max-width: none;
      width: 100vw;
      overflow-x: auto;
      overflow-y: hidden;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: thin;
      scrollbar-color: var(--bezowe-b200) transparent;
      padding: 16px 16px 20px 76px;
      justify-content: flex-start;
      height: auto;
      margin-left: calc(-50vw + 50%);
      margin-right: calc(-50vw + 50%);
    }

    .products-showcase-container::-webkit-scrollbar {
      height: 8px;
    }

    .products-showcase-container::-webkit-scrollbar-track {
      background: transparent;
    }

    .products-showcase-container::-webkit-scrollbar-thumb {
      background: var(--bezowe-b200);
      border-radius: 4px;
    }

    .products-showcase-container::-webkit-scrollbar-thumb:hover {
      background: var(--primary-p400);
    }

    .product-showcase-card {
      flex: 0 0 358px;
      width: 358px;
      min-width: 358px;
      height: 525px;
      grid-template-rows: 358px 12px auto 12px 44px;
    }

    .product-image-container {
      width: 358px;
      height: 358px;
    }

    .buy-button {
      width: 358px;
      height: 44px;
    }

    .ayla-product-showcase .product-title {
      color: var(--primary-p700) !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 20px !important;
      font-style: normal !important;
      font-weight: 500 !important;
      line-height: 110% !important;
      letter-spacing: -0.6px !important;
    }

    .ayla-product-showcase .product-description {
      color: #212121 !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 14px !important;
      font-style: normal !important;
      font-weight: 400 !important;
      line-height: 140% !important;
      letter-spacing: -0.28px !important;
    }
  }

  /* Desktop Transition (1280px-1439px) */
  @media (max-width: 1280px) and (min-width: 1025px) {
    .ayla-product-showcase {
      padding: 48px 0;
      min-height: auto;
      height: auto;
      overflow: visible;
    }

    .section-container {
      max-width: 100%;
      height: auto;
      padding: 0 80px;
    }

    .ayla-product-showcase .section-title {
      font-size: 42px;
    }

    .products-showcase-container {
      gap: 16px;
      max-width: none;
      width: 100vw;
      overflow-x: auto;
      overflow-y: hidden;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      padding: 16px 16px 20px 96px;
      margin-left: calc(-50vw + 50%);
      margin-right: calc(-50vw + 50%);
    }

    .product-showcase-card {
      flex: 0 0 358px;
      width: 358px;
      min-width: 358px;
      height: 525px;
      grid-template-rows: 358px 12px auto 12px 44px;
    }

    .product-image-container {
      width: 358px;
      height: 358px;
    }

    .buy-button {
      width: 358px;
      height: 44px;
    }

    .ayla-product-showcase .product-title {
      color: var(--primary-p700) !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 20px !important;
      font-style: normal !important;
      font-weight: 500 !important;
      line-height: 110% !important;
      letter-spacing: -0.6px !important;
    }

    .ayla-product-showcase .product-description {
      color: #212121 !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 14px !important;
      font-style: normal !important;
      font-weight: 400 !important;
      line-height: 140% !important;
      letter-spacing: -0.28px !important;
    }
  }

  @media (max-width: 1024px) {
    .ayla-product-showcase {
      padding: 40px 16px;
      min-height: auto;
      height: auto;
      overflow: visible;
    }

    .section-container {
      max-width: 100%;
      height: auto;
      padding: 0;
    }

    .ayla-product-showcase .section-title {
      font-size: 36px;
    }

    .products-showcase-container {
      gap: 16px;
      max-width: none;
      width: 100vw;
      overflow-x: auto;
      overflow-y: hidden;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      padding: 16px 16px 20px;
      margin-left: calc(-50vw + 50%);
      margin-right: calc(-50vw + 50%);
    }

    .product-showcase-card {
      flex: 0 0 358px;
      width: 358px;
      min-width: 358px;
      height: 525px;
      grid-template-rows: 358px 12px auto 12px 44px;
    }

    .product-image-container {
      width: 358px;
      height: 358px;
    }

    .buy-button {
      width: 358px;
      height: 44px;
    }

    .ayla-product-showcase .product-title {
      color: var(--primary-p700) !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 20px !important;
      font-style: normal !important;
      font-weight: 500 !important;
      line-height: 110% !important;
      letter-spacing: -0.6px !important;
    }

    .ayla-product-showcase .product-description {
      color: #212121 !important;
      font-family: 'Jost'
      , sans-serif !important;
      font-size: 14px !important;
      font-style: normal !important;
      font-weight: 400 !important;
      line-height: 140% !important;
      letter-spacing: -0.28px !important;
    }
  }

  @media (max-width: 768px) {
    .ayla-product-showcase {
      padding: 32px 16px;
      min-height: auto;
      height: auto;
      overflow: visible;
    }

    .section-container {
      gap: 24px;
      height: auto;
    }

    .ayla-product-showcase .section-title {
      font-size: 32px;
      line-height: 1.2;
    }

    .products-showcase-container {
      flex-direction: column;
      gap: 32px;
      align-items: center;
      overflow-x: visible;
      overflow-y: visible;
      height: auto;
    }

    .product-showcase-card {
      width: 100%;
      max-width: 400px;
      flex: none;
      min-width: auto;
    }

    .product-image-container {
      width: 100%;
      height: 380px;
    }

    .buy-button {
      width: 100%;
      padding: 12px 24px;
    }

    .product-title {
      line-height: 1.2;
      padding-top: 24px;
    }
  }

  @media (max-width: 480px) {
    .ayla-product-showcase {
      padding: 24px 16px;
      min-height: auto;
      height: auto;
      overflow: visible;
    }

    .ayla-product-showcase .section-title {
      font-size: 28px;
    }

    .products-showcase-container {
      overflow-x: visible;
      overflow-y: visible;
      height: auto;
    }

    .product-image-container {
      width: 100%;
      height: 380px;
    }

    .buy-button {
      width: 100%;
    }

    .product-description {
      font-size: 13px;
    }

    .buy-text {
      font-size: 14px;
    }

    .product-price {
      font-size: 16px;
    }
  }
{% endstylesheet %}

{% schema %}
  {
    "name": "AYLA Product Showcase",
    "tag": "section",
    "class": "shopify-section-ayla-product-showcase",
    "settings": [
      {
        "type": "header",
        "content": "Section Settings"
      }, {
        "type": "text",
        "id": "main_title",
        "label": "Main Title",
        "default": "Liderzy wśród kocich i psich smakoszy!"
      }
    ],
    "blocks": [
      {
        "type": "product_showcase",
        "name": "Product Showcase",
        "limit": 3,
        "settings": [
          {
            "type": "header",
            "content": "Product Information"
          },
          {
            "type": "text",
            "id": "product_title",
            "label": "Product Title",
            "default": "Liofilizowane przysmaki dla psa z filetu z piersi kurczaka"
          },
          {
            "type": "text",
            "id": "product_description",
            "label": "Product Description",
            "default": "Naturalne liofilizowane przysmaki"
          },
          {
            "type": "text",
            "id": "product_price",
            "label": "Product Price",
            "default": "23,90 zł"
          }, {
            "type": "url",
            "id": "product_url",
            "label": "Product URL"
          }, {
            "type": "header",
            "content": "Background Image"
          }, {
            "type": "image_picker",
            "id": "background_image",
            "label": "Complete Background Image",
            "info": "Upload the complete background with product and decorative elements (recommended: 400x400px)"
          }, {
            "type": "header",
            "content": "Badges & Labels"
          }, {
            "type": "checkbox",
            "id": "show_bestseller",
            "label": "Show Bestseller Badge",
            "default": true
          }, {
            "type": "text",
            "id": "badge_text",
            "label": "Badge Text",
            "default": "BESTSELLER"
          }, {
            "type": "select",
            "id": "animal_type",
            "label": "Animal Type",
            "options": [
              {
                "value": "dla kota",
                "label": "Dla Kota"
              }, {
                "value": "dla psa",
                "label": "Dla Psa"
              }
            ],
            "default": "dla kota"
          }, {
            "type": "image_picker",
            "id": "animal_icon",
            "label": "Animal Icon",
            "info": "Custom animal icon (optional - will use default if not set)"
          }, {
            "type": "header",
            "content": "Button"
          }, {
            "type": "text",
            "id": "buy_text",
            "label": "Buy Button Text",
            "default": "Dodaj do koszyka"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "AYLA Product Showcase",
        "category": "Product",
        "blocks": [
          {
            "type": "product_showcase",
            "settings": {
              "product_title": "Liofilizowane przysmaki dla psa z filetu z piersi kurczaka",
              "product_description": "Naturalne liofilizowane przysmaki",
              "product_price": "23,90 zł",
              "animal_type": "dla kota",
              "show_bestseller": true
            }
          }, {
            "type": "product_showcase",
            "settings": {
              "product_title": "Liofilizowane przysmaki dla psa z filetu z piersi kurczaka",
              "product_description": "Naturalne liofilizowane przysmaki",
              "product_price": "23,90 zł",
              "animal_type": "dla psa",
              "show_bestseller": true
            }
          }, {
            "type": "product_showcase",
            "settings": {
              "product_title": "Liofilizowane przysmaki dla psa z filetu z piersi kurczaka",
              "product_description": "Naturalne liofilizowane przysmaki",
              "product_price": "23,90 zł",
              "animal_type": "dla kota",
              "show_bestseller": true
            }
          }
        ]
      }
    ]
  }
{% endschema %}