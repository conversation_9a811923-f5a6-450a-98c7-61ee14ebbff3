{% comment %}
  Ayla Product Lines Section - Figma Design Implementation
  Three product lines in cards: DELIGHT, HELP, and RESCUE
  Matches Figma design exactly with proper colors, typography, and layout
{% endcomment %}

<ayla-product-lines class="ayla-product-lines">
  <div class="section-container">
    {% comment %} Main heading with cat and dog icons - Responsive layout {% endcomment %}
    <div class="section-header">
      <h2 class="section-title">
        <span class="title-part-1">{{ section.settings.title_line_1 | default: 'Produkty dopasowane do' }}</span>
        <span class="title-part-2">potrzeb twojego</span>
        <span class="title-part-3">
          <span class="title-text">kota</span>
          {% if section.settings.cat_icon %}
            <img
              src="{{ section.settings.cat_icon | image_url: width: 44 }}"
              alt="Cat icon"
              class="pet-icon cat-icon"
              width="29"
              height="44">
          {% else %}
            <svg
              class="pet-icon cat-icon"
              width="29"
              height="44"
              viewBox="0 0 29 44"
              fill="none">
              <path d="M14.5 0C6.5 0 0 6.5 0 14.5V29C0 36.5 6.5 43 14.5 43S29 36.5 29 29V14.5C29 6.5 22.5 0 14.5 0Z" fill="#2d4f40" />
            </svg>
          {% endif %}
          <span class="title-text">i</span>
          <span class="title-text">psa</span>
          {% if section.settings.dog_icon %}
            <img
              src="{{ section.settings.dog_icon | image_url: width: 44 }}"
              alt="Dog icon"
              class="pet-icon dog-icon"
              width="27"
              height="44">
          {% else %}
            <svg
              class="pet-icon dog-icon"
              width="27"
              height="44"
              viewBox="0 0 27 44"
              fill="none">
              <path d="M13.5 0C6 0 0 6 0 13.5V29C0 36.5 6 42.5 13.5 42.5S27 36.5 27 29V13.5C27 6 21 0 13.5 0Z" fill="#2d4f40" />
            </svg>
          {% endif %}
        </span>
      </h2>
    </div>

    {% comment %} Product cards container {% endcomment %}
    <div class="product-cards-container">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'delight_card' %}
            <div
              class="product-card delight-card"
              {{ block.shopify_attributes }}
              data-desktop-bg="{% if block.settings.desktop_image %}{{ block.settings.desktop_image | image_url: width: 600 }}{% endif %}"
              data-tablet-bg="{% if block.settings.tablet_image %}{{ block.settings.tablet_image | image_url: width: 600 }}{% endif %}"
              data-mobile-bg="{% if block.settings.mobile_image %}{{ block.settings.mobile_image | image_url: width: 400 }}{% endif %}">
              <div class="card-content">
                <div class="card-text">
                  <div class="card-header">
                    <span class="card-label">{{ block.settings.label | default: 'AYLA DELIGHT' }}</span>
                  </div>
                  <h3 class="card-title">{{ block.settings.title | default: 'Zdrowe, pełnowartościowe przysmaki' }}</h3>
                </div>
                <a href="{{ block.settings.link | default: '#' }}" class="card-button">
                  <span class="button-text">{{ block.settings.button_text | default: 'ODKRYJ LINIĘ DELIGHT' }}</span>
                  <div class="button-icon">
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none">
                      <path
                        d="M4 3l4 3-4 3"
                        stroke="currentColor"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                </a>
              </div>
              {% if block.settings.product_image %}
                <img
                  src="{{ block.settings.product_image | image_url: width: 400 }}"
                  alt="{{ block.settings.product_image.alt | default: block.settings.title }}"
                  class="product-image"
                  width="400"
                  height="300"
                  loading="lazy" />
              {% endif %}
            </div>

          {% when 'help_card' %}
            <div
              class="product-card help-card"
              {{ block.shopify_attributes }}
              data-desktop-bg="{% if block.settings.desktop_image %}{{ block.settings.desktop_image | image_url: width: 600 }}{% endif %}"
              data-tablet-bg="{% if block.settings.tablet_image %}{{ block.settings.tablet_image | image_url: width: 600 }}{% endif %}"
              data-mobile-bg="{% if block.settings.mobile_image %}{{ block.settings.mobile_image | image_url: width: 400 }}{% endif %}">
              <div class="card-content">
                <div class="card-text">
                  <div class="card-header">
                    <span class="card-label">{{ block.settings.label | default: 'AYLA HELP' }}</span>
                  </div>
                  <h3 class="card-title">{{ block.settings.title | default: 'Wspierające dobrostan i rekonwalsecencję' }}</h3>
                </div>
                <a href="{{ block.settings.link | default: '#' }}" class="card-button">
                  <span class="button-text">{{ block.settings.button_text | default: 'ODKRYJ LINIĘ HELP' }}</span>
                  <div class="button-icon">
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none">
                      <path
                        d="M4 3l4 3-4 3"
                        stroke="currentColor"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                </a>
              </div>
              {% if block.settings.product_image %}
                <img
                  src="{{ block.settings.product_image | image_url: width: 400 }}"
                  alt="{{ block.settings.product_image.alt | default: block.settings.title }}"
                  class="product-image"
                  width="400"
                  height="300"
                  loading="lazy" />
              {% endif %}
            </div>

          {% when 'rescue_card' %}
            <div
              class="product-card rescue-card"
              {{ block.shopify_attributes }}
              data-desktop-bg="{% if block.settings.desktop_image %}{{ block.settings.desktop_image | image_url: width: 600 }}{% endif %}"
              data-tablet-bg="{% if block.settings.tablet_image %}{{ block.settings.tablet_image | image_url: width: 600 }}{% endif %}"
              data-mobile-bg="{% if block.settings.mobile_image %}{{ block.settings.mobile_image | image_url: width: 400 }}{% endif %}">
              <div class="card-content">
                <div class="card-text">
                  <div class="card-header">
                    <span class="card-label">{{ block.settings.label | default: 'AYLA RESCUE' }}</span>
                  </div>
                  <h3 class="card-title">{{ block.settings.title | default: 'Wsparcie w terapii żywieniowej i rekonwalescencji' }}</h3>
                </div>
                <a href="{{ block.settings.link | default: '#' }}" class="card-button">
                  <span class="button-text">{{ block.settings.button_text | default: 'ODKRYJ LINIĘ RESCUE' }}</span>
                  <div class="button-icon">
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none">
                      <path
                        d="M4 3l4 3-4 3"
                        stroke="currentColor"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                </a>
              </div>
              {% if block.settings.product_image %}
                <img
                  src="{{ block.settings.product_image | image_url: width: 400 }}"
                  alt="{{ block.settings.product_image.alt | default: block.settings.title }}"
                  class="product-image"
                  width="400"
                  height="300"
                  loading="lazy" />
              {% endif %}
            </div>
        {% endcase %}
      {% endfor %}
    </div>
  </div>
</ayla-product-lines>

<script>
  // Product Lines - New image-based layout (no background switching needed)
  ('[AylaProductLines] Initialized with new image-based layout');
</script>

{% javascript %}
  class AylaProductLines extends HTMLElement {
    constructor() {
      super();
      ('[AylaProductLines] New image-based layout initialized');
    }

    connectedCallback() {

// New image-based layout - no background switching needed
      ('[AylaProductLines] Connected with new image layout');
    }
  }

  customElements.define('ayla-product-lines', AylaProductLines);
{% endjavascript %}

{% stylesheet %}
  .ayla-product-lines {
    --primary-p700: #2d4f40;
    --primary-p0: #ffffff;
    --bezowe-b50: #f9f9f4;
    --bezowe-b100: #f0f0e4;
    --bezowe-b200: #e0e0c8;
    --primary-p400: #6a9981;
    --special-duck: #8d5280;
    --special-indyk: #5f7fbc;

    background: var(--bezowe-b50);
    padding: 56px 120px;
    box-sizing: border-box;
  }

  /* New Product Image Layout - Based on Figma Analysis */
  .product-image {
    position: absolute !important;
    z-index: 1;
    pointer-events: none;
    background: transparent !important;
    display: block !important;
    object-fit: cover !important;
    /* Specific positioning will be handled per card type */
  }

  /* Card content positioning - matches Figma structure */
  .card-content {
    position: absolute;
    top: 24px;
    left: 24px;
    width: 189px;
    height: 240px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 2;
  }

  /* Remove any background image properties from cards */
  .product-card {
    position: relative;
    overflow: visible;
    /* Remove background-image related properties */
  }

  /* Card-specific image positioning - scaled to match Figma proportions */
  .delight-card .product-image {
    top: 15px !important;
    left: 159px !important;
    /* Scaled dimensions to match Figma visual proportions */
    width: 235px !important;
    height: 250px !important;
    max-width: none !important;
    /* Override media query max-width constraint */
    /* Aspect ratio: 235/250 = 0.94 */
    transform: none !important;
  }

  .help-card .product-image {
    top: 25px !important;
    left: 310px !important;
    width: 170px !important;
    height: 226px !important;
    transform: translateX(-50%) !important;
  }

  .rescue-card .product-image {
    top: 10px !important;
    left: 135px !important;
    width: 280px !important;
    height: 250px !important;
    transform: none !important;
  }

  .section-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 32px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  /* Section Header */
  .section-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 6px;
    width: 676px;
  }

  .ayla-product-lines .section-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 32px;
    line-height: 1.1;
    letter-spacing: -0.96px;
    color: var(--primary-p700);
    text-align: center;
    margin: 0;
    text-transform: none;
  }

  /* Base title styles - Desktop default */
  .title-part-1,
  .title-part-2,
  .title-part-3 {
    display: block;
    margin-bottom: 6px;
  }

  .title-part-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .pet-icon {
    width: 28.769px;
    height: 44px;
    flex-shrink: 0;
  }

  .cat-icon {
    width: 28.769px;
  }

  .dog-icon {
    width: 27.077px;
  }

  /* Desktop layout: produkty on line 1, potrzeb + kota-i-psa on line 2 */
  .title-part-1 {
    font-size: 40px;
    letter-spacing: -1.2px;
    line-height: 1.1;
  }

  .title-part-2 {
    font-size: 40px;
    letter-spacing: -1.2px;
    line-height: 1.1;
    display: inline;
  }

  .title-part-3 {
    font-size: 40px;
    letter-spacing: -1.2px;
    line-height: 1.1;
    display: inline-flex;
    margin-left: 12px;
  }

  /* Product Cards Container */
  .product-cards-container {
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: flex-start;
    width: 100%;
    flex-wrap: nowrap;
    justify-content: center;
  }

  /* Base Product Card Styles */
  .product-card {
    flex: 1 1 0;
    min-width: 0;
    max-width: none;
    height: 288px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    /* Initial background will be set by data attributes */
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
  }


  .card-content {
    position: absolute;
    top: 24px;
    left: 24px;
    right: 24px;
    bottom: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 10;
  }

  .card-text {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 189px;
  }

  .card-label {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: 1.4px;
    text-transform: uppercase;
  }

  .card-title {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 24px;
    line-height: 1.1;
    letter-spacing: -0.72px;
    margin: 0;
  }

  /* Card Button */
  .card-button {
    display: inline-flex;
    align-items: center;
    padding: 4px 4px 4px 12px;
    /* 4px right padding to match Figma pr-1 */
    border-radius: 33px;
    text-decoration: none;
    border: 1px solid;
    transition: all 0.2s ease;
    width: fit-content;
    gap: 9px;
    /* 9px gap between text and icon to match Figma */
  }

  .button-text {
    font-family: 'Jost'
    , sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    letter-spacing: 0.48px;
    text-transform: uppercase;
    white-space: nowrap;
    /* Prevent text wrapping */
  }

  .button-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-p0);
    border-radius: 32px;
    padding: 6.857px;
    box-sizing: border-box;
  }

  .button-icon svg {
    width: 12px;
    height: 12px;
  }

  /* DELIGHT Card (Purple) */
  .delight-card {
    /* Default background color if no image is set */
    background-color: var(--special-duck);
  }

  .delight-card .card-label {
    color: var(--primary-p0);
    opacity: 0.7;
  }

  .delight-card .card-title {
    color: var(--primary-p0);
  }

  .delight-card .card-button {
    border-color: rgba(255, 255, 255, 0.5);
  }

  .delight-card .button-text {
    color: var(--primary-p0);
  }

  .delight-card:hover .card-button {
    transform: scale(1.05);
  }

  /* HELP Card (Light) */
  .help-card {
    /* Default background color if no image is set */
    background-color: var(--bezowe-b100);
    border: 1px solid var(--bezowe-b100);
    overflow: hidden;
  }

  .help-card .card-label {
    color: var(--special-indyk);
  }

  .help-card .card-title {
    color: var(--primary-p700);
  }

  .help-card .card-button {
    border-color: var(--bezowe-b200);
  }

  .help-card .button-text {
    color: var(--primary-p700);
  }

  .help-card:hover .card-button {
    background: var(--bezowe-b200);
  }

  /* RESCUE Card (Green) */
  .rescue-card {
    /* Default background color if no image is set */
    background-color: var(--primary-p400);
    border: 1px solid var(--bezowe-b100);
  }

  .rescue-card .card-label {
    color: var(--primary-p0);
    opacity: 0.7;
  }

  .rescue-card .card-title {
    color: var(--primary-p0);
  }

  .rescue-card .card-button {
    border-color: rgba(255, 255, 255, 0.5);
  }

  .rescue-card .button-text {
    color: var(--primary-p0);
  }

  .rescue-card:hover .card-button {
    transform: scale(1.05);
  }

  /* Responsive Design */

  /* Large Desktop (1280px + ) */@media (min-width: 1281px) {
    .product-cards-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      /* Add padding to prevent edge overflow */
      box-sizing: border-box;
    }
  }

  /* Desktop Transition (1251px-1439px) - Responsive cards */
  @media (min-width: 1251px) and (max-width: 1439px) {
    .ayla-product-lines {
      padding: 48px 60px;
    }

    .section-header {
      width: 100%;
      max-width: 620px;
    }

    .ayla-product-lines .section-title {
      font-size: 32px;
      text-transform: none;
    }

    .product-cards-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box;
      flex-wrap: nowrap;
    }

    .product-card {
      flex: 1 1 0;
      min-width: 0;
      max-width: none;
      height: 288px;
    }

    .card-text {
      width: 100%;
      max-width: 180px;
      margin-bottom: 40px;
      /* Increased spacing to prevent overlap with product image */
    }

    .card-title {
      font-size: 22px;
    }

    .product-image {
      height: 95%;
      /* Larger for desktop */
      max-width: 70%;
      /* More space for image on desktop */
    }
  }

  /* Desktop Transition (1440px-1599px) - Responsive cards */
  @media (min-width: 1440px) and (max-width: 1599px) {
    .ayla-product-lines {
      padding: 52px 80px;
    }

    .section-header {
      width: 100%;
      max-width: 650px;
    }

    .ayla-product-lines .section-title {
      font-size: 30px;
      text-transform: none;
    }

    .product-cards-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box;
      flex-wrap: nowrap;
    }

    .product-card {
      flex: 1 1 0;
      min-width: 0;
      max-width: none;
      height: 288px;
    }

    .card-text {
      width: 100%;
      max-width: 185px;
      margin-bottom: 40px;
      /* Increased spacing to prevent overlap with product image */
    }

    .card-title {
      font-size: 23px;
    }

    .product-image {
      height: 95%;
      /* Larger for desktop */
      max-width: 70%;
      /* More space for image on desktop */
    }
  }

  /* Full Desktop (1600px+) - Original design with proper spacing */
  @media (min-width: 1600px) {
    .ayla-product-lines {
      padding: 56px 120px;
    }

    .section-header {
      width: 676px;
    }

    .ayla-product-lines .section-title {
      font-size: 28px;
      text-transform: none;
    }

    .product-cards-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box;
      flex-wrap: nowrap;
    }

    .product-card {
      flex: 1 1 0;
      min-width: 0;
      max-width: none;
      height: 288px;
    }

    .card-text {
      width: 189px;
      margin-bottom: 40px;
      /* Increased spacing to prevent overlap with product image */
      /* Original text width */
    }

    .card-title {
      font-size: 24px;
      /* Original font size */
    }

    .product-image {
      height: 100%;
      /* Full height for large desktop */
      max-width: 75%;
      /* Maximum space for image on large desktop */
    }
  }

  /* Old background image rules removed - using new image-based layout */

  /* Tablet View (481px-1250px) - Based on Figma design with 420px height */@media (max-width: 1250px) and (min-width: 801px) {
    .ayla-product-lines {
      padding: 16px 0;
      width: 100vw;
      margin-left: calc(-50vw + 50%);
      margin-right: calc(-50vw + 50%);
    }

    .ayla-product-lines .section-container {
      gap: 28px;
      padding: 0 16px !important;
      /* Force 16px padding on tablet view */
    }

    .section-header {
      width: 100%;
      max-width: 580px;
    }

    .ayla-product-lines .section-title {
      font-size: 26px;
      letter-spacing: -0.78px;
      text-transform: none;
    }

    /* Tablet layout: produkty + potrzeb on line 1, kota-i-psa on line 2 */
    .title-part-1 {
      font-size: 32px;
      letter-spacing: -0.96px;
      line-height: 1.1;
      display: inline;
    }

    .title-part-2 {
      font-size: 32px;
      letter-spacing: -0.96px;
      line-height: 1.1;
      display: inline;
    }

    .title-part-3 {
      font-size: 32px;
      letter-spacing: -0.96px;
      line-height: 1.1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      flex-wrap: wrap;
      margin-left: 0;
      margin-top: 6px;
      margin-bottom: 0;
      width: auto;
      max-width: none;
    }

    .product-cards-container {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: flex-start;
      flex-wrap: nowrap;
      gap: 8px;
      width: 100%;
      max-width: 100%;
      padding: 0 0 20px;
      /* No horizontal padding - handled by section-container */
      box-sizing: border-box;
    }

    .product-card {
      flex: 1 1 0;
      min-width: 0;
      max-width: none;
      height: 420px;
      /* Figma design height */
      overflow: visible;
      /* Allow images to extend beyond card boundaries */
    }


    .card-content {
      position: absolute;
      top: 24px;
      /* left-6 = 24px in Figma */
      left: 24px;
      width: 250px;
      /* Increased width to prevent text wrapping */
      height: 240px;
      /* h-60 = 240px in Figma */
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 24px;
      /* gap-6 = 24px in Figma */
      z-index: 2;
    }

    .card-text {
      width: 100%;
      max-width: 220px;
      /* Increased width to prevent text wrapping */
      gap: 12px;
      /* gap-3 = 12px in Figma */
      margin-bottom: 0;
      /* No bottom margin - gap handles spacing */
    }

    .card-label {
      font-size: 14px;
      letter-spacing: 1.4px;
    }

    .card-title {
      font-size: 24px;
      /* text-[24px] in Figma */
      line-height: 1.1;
      /* leading-[1.1] in Figma */
      letter-spacing: -0.72px;
      /* tracking-[-0.72px] in Figma */
    }

    /* Force specific text wrapping for HELP card at tablet view */
    .help-card .card-title {
      max-width: 180px;
      /* Increased width to prevent text wrapping */
    }

    .card-button {
      padding: 4px 4px 4px 12px;
      /* pl-3 pr-1 py-1 in Figma */
      width: fit-content;
      gap: 9px;
      /* gap-[9px] in Figma */
    }

    .button-text {
      font-size: 12px;
      /* text-[12px] in Figma */
      letter-spacing: 0.48px;
      /* tracking-[0.48px] in Figma */
    }

    .button-icon {
      width: 32px;
      height: 32px;
      /* size-8 = 32px in Figma */
    }

    /* Product image positioning for tablet view - bottom-right corner like Figma */
    .delight-card .product-image {
      position: absolute !important;
      right: 0 !important;
      bottom: -15px !important;
      width: 220px !important;
      height: 220px !important;
      max-width: none !important;
      left: auto !important;
      top: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .help-card .product-image {
      position: absolute !important;
      right: 0 !important;
      bottom: 0 !important;
      width: 150px !important;
      height: 200px !important;
      max-width: none !important;
      left: auto !important;
      top: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .rescue-card .product-image {
      position: absolute !important;
      right: 0 !important;
      bottom: 0 !important;
      width: 150px !important;
      height: 200px !important;
      max-width: none !important;
      left: auto !important;
      top: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }
  }

  /* Mobile View (<=800px) - Based on Figma design */
  @media (max-width: 800px) {
    .ayla-product-lines {
      padding: 56px 0;
      /* py-14 = 56px vertical padding */
      width: 100vw;
      margin-left: calc(-50vw + 50%);
      margin-right: calc(-50vw + 50%);
    }

    .section-container {
      gap: 32px;
      /* gap-8 = 32px gap */
      padding: 0 16px;
    }

    .section-header {
      width: 100%;
    }

    .ayla-product-lines .section-title {
      font-size: 24px;
      /* text-[24px] responsive */
      letter-spacing: -0.72px;
      text-transform: none;
      /* tracking-[-0.96px] in Figma */
      line-height: 1.1;
      /* leading-[1.1] in Figma */
    }

    /* Mobile layout: produkty on line 1, potrzeb on line 2, kota-i-psa on line 3 */
    .title-part-1 {
      font-size: 32px;
      letter-spacing: -0.96px;
      line-height: 1.1;
      display: block;
      margin-bottom: 6px;
    }

    .title-part-2 {
      font-size: 32px;
      letter-spacing: -0.96px;
      line-height: 1.1;
      display: block;
      margin-bottom: 6px;
      margin-left: 0;
    }

    .title-part-3 {
      font-size: 32px;
      letter-spacing: -0.96px;
      line-height: 1.1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      flex-wrap: wrap;
      margin-left: 0;
      margin-top: 0;
      width: auto;
      max-width: none;
    }

    .pet-icon {
      width: 28.769px;
      height: 44px;
      /* Match Figma dimensions */
    }

    .product-cards-container {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      /* gap-3 = 12px gap between cards */
      width: 100%;
      max-width: 100%;
      padding: 0 16px;
      /* 16px padding on both sides */
      box-sizing: border-box;
    }

    .product-card {
      width: 100%;
      height: 288px;
      /* h-72 = 288px in Figma */
      flex: 0 0 auto;
      position: relative;
      overflow: hidden;
    }

    .delight-card .product-image {
      position: absolute !important;
      right: 5px !important;
      bottom: -15px !important;
      width: 260px !important;
      height: 320px !important;
      max-width: none !important;
      left: auto !important;
      top: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .help-card .product-image {
      position: absolute !important;
      right: -10px !important;
      top: 0 !important;
      width: 200px !important;
      height: 280px !important;
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .rescue-card .product-image {
      position: absolute !important;
      right: 0 !important;
      top: 0 !important;
      width: 240px !important;
      /* Larger width to fill empty space */
      height: 288px !important;
      /* h-72 = 288px */
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .card-content {
      position: absolute !important;
      top: 20px !important;
      /* Slightly reduced top spacing */
      left: 20px !important;
      /* Slightly reduced left spacing */
      width: calc(100% - 220px) !important;
      /* Reduced gap - text gets more space */
      max-width: none !important;
      /* Remove max-width constraint */
      height: calc(100% - 40px) !important;
      /* Use full available height with padding */
      display: flex !important;
      flex-direction: column !important;
      justify-content: space-between !important;
      /* justify-between */
      align-items: flex-start !important;
      z-index: 2 !important;
    }

    .card-text {
      width: 100% !important;
      max-width: 100% !important;
      /* Use full available width */
      gap: 16px !important;
      /* Increased gap for better spacing */
      flex: 1 !important;
      /* Take available space */
      display: flex !important;
      flex-direction: column !important;
      justify-content: flex-start !important;
    }

    .card-label {
      font-size: 13px !important;
      /* Slightly smaller for better mobile fit */
      letter-spacing: 1.3px !important;
      /* Adjusted letter spacing */
      line-height: 1.4 !important;
      /* leading-[1.4] */
    }

    .card-title {
      font-size: 22px !important;
      /* Slightly smaller for better mobile fit */
      line-height: 1.15 !important;
      /* Slightly increased line height */
      letter-spacing: -0.66px !important;
      /* Adjusted letter spacing */
    }

    .card-button {
      padding: 6px 6px 6px 14px !important;
      /* Slightly increased padding */
      width: fit-content !important;
      gap: 8px !important;
      /* Slightly reduced gap */
      margin-top: auto !important;
      /* Push to bottom */
    }

    .button-text {
      font-size: 12px;
      /* text-[12px] in Figma */
      letter-spacing: 0.48px;
      /* tracking-[0.48px] in Figma */
      line-height: 1.25;
    }

    .button-icon {
      width: 32px;
      height: 32px;
      /* size-8 = 32px in Figma */
    }
  }

  /* Very Small Mobile View (<=500px) - Based on Figma design - MOVED TO END FOR HIGHER PRIORITY */
  @media (max-width: 500px) {
    .ayla-product-lines {
      padding: 56px 0;
      /* py-14 = 56px vertical padding */
      width: 100vw;
      margin-left: calc(-50vw + 50%);
      margin-right: calc(-50vw + 50%);
    }

    .section-container {
      gap: 32px;
      /* gap-8 = 32px gap */
      padding: 0 16px;
    }

    .ayla-product-lines .section-title {
      font-size: 22px;
      /* text-[22px] responsive mobile */
      letter-spacing: -0.66px;
      text-transform: none;
      /* tracking-[-0.96px] in Figma */
      line-height: 1.1;
      /* leading-[1.1] in Figma */
    }

    .pet-icon {
      width: 28.769px;
      height: 44px;
      /* Match Figma dimensions */
    }

    .product-cards-container {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      /* gap-3 = 12px gap between cards */
      width: 100%;
      max-width: 100%;
      padding: 0 16px;
      /* 16px padding on both sides */
      box-sizing: border-box;
    }

    .product-card {
      width: 100%;
      height: 288px;
      /* h-72 = 288px in Figma */
      flex: 0 0 auto;
      position: relative;
      overflow: hidden;
    }

    /* Product image positioning for very small mobile - individual card styling */
    .delight-card .product-image {
      position: absolute !important;
      right: 0 !important;
      top: 0 !important;
      width: 225px !important;
      height: 280px !important;
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .help-card .product-image {
      position: absolute !important;
      right: 0 !important;
      top: 20px !important;
      width: 170px !important;
      height: 230px !important;
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .rescue-card .product-image {
      position: absolute !important;
      right: 0 !important;
      top: 20px !important;
      width: 170px !important;
      height: 230px !important;
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .card-content {
      position: absolute !important;
      top: 24px !important;
      /* top-6 = 24px in Figma */
      left: 24px !important;
      /* left-6 = 24px in Figma */
      width: calc(100% - 180px) !important;
      /* More generous width for better text flow */
      max-width: 200px !important;
      /* Larger max-width to prevent cramping */
      height: 240px !important;
      /* h-60 = 240px in Figma */
      display: flex !important;
      flex-direction: column !important;
      justify-content: space-between !important;
      /* justify-between in Figma */
      align-items: flex-start !important;
      z-index: 2 !important;
    }

    .card-text {
      width: 100% !important;
      max-width: 200px !important;
      /* Match the card-content max-width */
      gap: 12px !important;
      /* gap-3 = 12px in Figma */
    }

    .card-label {
      font-size: 14px;
      /* text-[14px] in Figma */
      letter-spacing: 1.4px;
      /* tracking-[1.4px] in Figma */
      line-height: 1.4;
      /* leading-[1.4] in Figma */
    }

    .card-title {
      font-size: 24px;
      /* text-[24px] in Figma */
      line-height: 1.1;
      /* leading-[1.1] in Figma */
      letter-spacing: -0.72px;
      /* tracking-[-0.72px] in Figma */
    }

    .card-button {
      padding: 4px 4px 4px 12px;
      /* pl-3 pr-1 py-1 in Figma */
      width: fit-content;
      gap: 9px;
      /* gap-[9px] in Figma */
    }

    .button-text {
      font-size: 12px;
      /* text-[12px] in Figma */
      letter-spacing: 0.48px;
      /* tracking-[0.48px] in Figma */
      line-height: 1.25;
    }

    .button-icon {
      width: 32px;
      height: 32px;
      /* size-8 = 32px in Figma */
    }
  }

  /* Extra Small Mobile View (<=430px) - Even smaller images for very small screens */
  @media (max-width: 430px) {
    .delight-card .product-image {
      position: absolute !important;
      right: 0 !important;
      top: 0 !important;
      width: 150px !important;
      height: 260px !important;
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .help-card .product-image {
      position: absolute !important;
      right: -30px !important;
      top: 30px !important;
      width: 140px !important;
      height: 200px !important;
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
      object-position: center;
    }

    .rescue-card .product-image {
      position: absolute !important;
      right: 0 !important;
      top: 20px !important;
      width: 130px !important;
      height: 220px !important;
      max-width: none !important;
      left: auto !important;
      bottom: auto !important;
      transform: none !important;
      z-index: 1;
      object-fit: cover;
    }

    .card-content {
      width: calc(100% - 140px) !important;
      /* Push text even closer to the 150px images */
      max-width: 180px !important;
      /* Wider max-width for more text space */
    }

    .delight-card .card-text .card-label,
    .help-card .card-text .card-label,
    .rescue-card .card-text .card-label {
      font-size: 12px !important;
      /* Reduced from 13px */
    }

    .delight-card .card-text .card-title,
    .help-card .card-text .card-title,
    .rescue-card .card-text .card-title {
      font-size: 22px !important;
      /* Reduced from 23px */
    }
  }

{% endstylesheet %}

<script>
  (function() {
    'use strict';
    
    // Check if Web Component is supported and working
    function isWebComponentWorking() {
      return customElements.get('ayla-product-lines') && 
             document.querySelector('ayla-product-lines');
    }
    
    // Function to update responsive backgrounds
    function updateProductLineBackgrounds() {
      // Skip if Web Component is handling this
      if (isWebComponentWorking()) {
  ('[Product Lines Fallback] Web Component is active, skipping fallback');
        return;
      }
      
      const productCards = document.querySelectorAll('.product-card');
      const viewportWidth = window.innerWidth;
      
      // Log for debugging
  (`[Product Lines Fallback] Viewport width: ${viewportWidth}px`);
      
      productCards.forEach((card, index) => {
        const desktopBg = card.getAttribute('data-desktop-bg');
        const tabletBg = card.getAttribute('data-tablet-bg');
        const mobileBg = card.getAttribute('data-mobile-bg');
        
        // Skip if no backgrounds are defined
        if (!desktopBg && !tabletBg && !mobileBg) {
  (`[Product Lines Fallback] Card ${index + 1}: No backgrounds defined`);
          return;
        }
        
        // Fallback to product image if no responsive images are set
        const productImage = card.querySelector('.product-image');
        if (productImage && productImage.src && !desktopBg && !tabletBg && !mobileBg) {
  (`[Product Lines Fallback] Card ${index + 1}: Using product image as fallback`);
          const fallbackBg = productImage.src;
          card.style.setProperty('--current-bg-image', `url('${fallbackBg}')`);
          card.setAttribute('style', 
            `background-image: url('${fallbackBg}') !important; ` +
            `background-size: cover !important; ` +
            `background-position: center !important; ` +
            `background-repeat: no-repeat !important; ` +
            `--current-bg-image: url('${fallbackBg}');`
          );
          return;
        }
        
        let selectedBg = '';
        let mode = '';
        
        // STRICT BREAKPOINTS - Updated to match CSS and Web Component logic
        if (viewportWidth <= 800) {
          // Mobile for 0-800px range
          selectedBg = mobileBg || tabletBg || desktopBg;
          mode = 'mobile';
        } else if (viewportWidth >= 801 && viewportWidth <= 1024) {
          // Tablet for 801-1024px range
          selectedBg = tabletBg || desktopBg;
          mode = 'tablet';
        } else if (viewportWidth > 1024) {
          // Desktop above 1024px
          selectedBg = desktopBg;
          mode = 'desktop';
        }
        
        // Apply the selected background with important flag to override inline styles
        if (selectedBg) {
          // Set CSS variables for additional CSS control
          const tabletImage = tabletBg || desktopBg;
          card.style.setProperty('--tablet-bg-image', `url('${tabletImage}')`);
          card.style.setProperty('--current-bg-image', `url('${selectedBg}')`);
          
          // Clear all background styles first
          card.style.background = '';
          card.style.backgroundImage = '';
          
          // Apply new background with force
          card.setAttribute('style', 
            `background-image: url('${selectedBg}') !important; ` +
            `background-size: cover !important; ` +
            `background-position: center !important; ` +
            `background-repeat: no-repeat !important; ` +
            `--tablet-bg-image: url('${tabletImage}'); ` +
            `--current-bg-image: url('${selectedBg}');`
          );
          
          // Add data attribute to track current mode
          card.setAttribute('data-current-mode', mode);
          
  (`[Product Lines Fallback] Card ${index + 1}: ${mode} mode - background: ${selectedBg}`);
        }
      });
      
  (`[Product Lines Fallback] Updated ${productCards.length} cards for ${viewportWidth <= 480 ? 'mobile' : viewportWidth <= 1024 ? 'tablet' : 'desktop'} view`);
    }
    
    // Initialize on DOM ready
    function initProductLines() {
      // Wait a bit for any Shopify scripts to finish
      setTimeout(function() {
        updateProductLineBackgrounds();
        
        // Force update after images might have loaded
        window.addEventListener('load', function() {
          setTimeout(updateProductLineBackgrounds, 100);
        });
      }, 100);
      
      // Update on resize with debouncing
      let resizeTimeout;
      window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(updateProductLineBackgrounds, 100);
      });
      
      // Update on orientation change
      window.addEventListener('orientationchange', function() {
        setTimeout(updateProductLineBackgrounds, 200);
      });
      
      // Also listen for Shopify theme editor events
      document.addEventListener('shopify:section:load', function(event) {
        if (event.detail.sectionId && event.target.classList.contains('shopify-section-ayla-product-lines')) {
          setTimeout(updateProductLineBackgrounds, 200);
        }
      });
      
      document.addEventListener('shopify:section:reorder', updateProductLineBackgrounds);
    }
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initProductLines);
    } else {
      initProductLines();
    }
  })();
</script>

{% schema %}
  {
    "name": "Ayla Product Lines",
    "tag": "section",
    "class": "shopify-section-ayla-product-lines",
    "settings": [
      {
        "type": "header",
        "content": "Section Heading"
      },
      {
        "type": "text",
        "id": "title_line_1",
        "label": "Title Line 1",
        "default": "Produkty dopasowane do"
      },
      {
        "type": "text",
        "id": "title_line_2",
        "label": "Title Line 2 (before cat)",
        "default": "potrzeb twojego kota"
      },
      {
        "type": "text",
        "id": "title_connector",
        "label": "Connector word",
        "default": "i"
      }, {
        "type": "text",
        "id": "title_line_3",
        "label": "Title Line 3 (kota for tablet/mobile)",
        "default": "kota"
      }, {
        "type": "text",
        "id": "title_line_4",
        "label": "Title Line 4 (psa for tablet/mobile)",
        "default": "psa"
      }, {
        "type": "image_picker",
        "id": "cat_icon",
        "label": "Cat Icon",
        "info": "SVG icon for cat (recommended: 29x44px)"
      }, {
        "type": "image_picker",
        "id": "dog_icon",
        "label": "Dog Icon",
        "info": "SVG icon for dog (recommended: 27x44px)"
      }
    ],
    "blocks": [
      {
        "type": "delight_card",
        "name": "🟣 DELIGHT Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "label",
            "label": "Card Label",
            "default": "AYLA DELIGHT"
          },
          {
            "type": "textarea",
            "id": "title",
            "label": "Card Title",
            "default": "Zdrowe, pełnowartościowe przysmaki"
          },
          {
            "type": "text",
            "id": "button_text",
            "label": "Button Text",
            "default": "ODKRYJ LINIĘ DELIGHT"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Card Link"
          }, {
            "type": "header",
            "content": "🖼️ Product Image"
          }, {
            "type": "image_picker",
            "id": "product_image",
            "label": "Product Image",
            "info": "The product image that will be positioned on the right side of the card. Recommended: 400x300px or larger"
          }, {
            "type": "header",
            "content": "📱 Responsive Background Images"
          }, {
            "type": "image_picker",
            "id": "desktop_image",
            "label": "Desktop Background Image",
            "info": "Background image for desktop view (recommended: 600x400px). If not set, uses product image."
          }, {
            "type": "image_picker",
            "id": "tablet_image",
            "label": "Tablet Background Image",
            "info": "Background image for tablet view (recommended: 600x400px). If not set, uses desktop image."
          }, {
            "type": "image_picker",
            "id": "mobile_image",
            "label": "Mobile Background Image",
            "info": "Background image for mobile view (recommended: 400x300px). If not set, uses tablet image."
          }
        ]
      }, {
        "type": "help_card",
        "name": "🔵 HELP Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "label",
            "label": "Card Label",
            "default": "AYLA HELP"
          },
          {
            "type": "textarea",
            "id": "title",
            "label": "Card Title",
            "default": "Wspierające dobrostan i rekonwalsecencję"
          },
          {
            "type": "text",
            "id": "button_text",
            "label": "Button Text",
            "default": "ODKRYJ LINIĘ HELP"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Card Link"
          }, {
            "type": "header",
            "content": "🖼️ Product Image"
          }, {
            "type": "image_picker",
            "id": "product_image",
            "label": "Product Image",
            "info": "The product image that will be positioned on the right side of the card. Recommended: 400x300px or larger"
          }, {
            "type": "header",
            "content": "📱 Responsive Background Images"
          }, {
            "type": "image_picker",
            "id": "desktop_image",
            "label": "Desktop Background Image",
            "info": "Background image for desktop view (recommended: 600x400px). If not set, uses product image."
          }, {
            "type": "image_picker",
            "id": "tablet_image",
            "label": "Tablet Background Image",
            "info": "Background image for tablet view (recommended: 600x400px). If not set, uses desktop image."
          }, {
            "type": "image_picker",
            "id": "mobile_image",
            "label": "Mobile Background Image",
            "info": "Background image for mobile view (recommended: 400x300px). If not set, uses tablet image."
          }
        ]
      }, {
        "type": "rescue_card",
        "name": "🟢 RESCUE Card",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "label",
            "label": "Card Label",
            "default": "AYLA RESCUE"
          },
          {
            "type": "textarea",
            "id": "title",
            "label": "Card Title",
            "default": "Wsparcie w terapii żywieniowej i rekonwalescencji"
          },
          {
            "type": "text",
            "id": "button_text",
            "label": "Button Text",
            "default": "ODKRYJ LINIĘ RESCUE"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Card Link"
          }, {
            "type": "header",
            "content": "🖼️ Product Image"
          }, {
            "type": "image_picker",
            "id": "product_image",
            "label": "Product Image",
            "info": "The product image that will be positioned on the right side of the card. Recommended: 400x300px or larger"
          }, {
            "type": "header",
            "content": "📱 Responsive Background Images"
          }, {
            "type": "image_picker",
            "id": "desktop_image",
            "label": "Desktop Background Image",
            "info": "Background image for desktop view (recommended: 600x400px). If not set, uses product image."
          }, {
            "type": "image_picker",
            "id": "tablet_image",
            "label": "Tablet Background Image",
            "info": "Background image for tablet view (recommended: 600x400px). If not set, uses desktop image."
          }, {
            "type": "image_picker",
            "id": "mobile_image",
            "label": "Mobile Background Image",
            "info": "Background image for mobile view (recommended: 400x300px). If not set, uses tablet image."
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Ayla Product Lines",
        "category": "Product",
        "blocks": [
          {
            "type": "delight_card",
            "settings": {
              "label": "AYLA DELIGHT",
              "title": "Zdrowe, pełnowartościowe przysmaki",
              "button_text": "ODKRYJ LINIĘ DELIGHT"
            }
          }, {
            "type": "help_card",
            "settings": {
              "label": "AYLA HELP",
              "title": "Wspierające dobrostan i rekonwalsecencję",
              "button_text": "ODKRYJ LINIĘ HELP"
            }
          }, {
            "type": "rescue_card",
            "settings": {
              "label": "AYLA RESCUE",
              "title": "Wsparcie w terapii żywieniowej i rekonwalescencji",
              "button_text": "ODKRYJ LINIĘ RESCUE"
            }
          }
        ]
      }
    ]
  }
{% endschema %}