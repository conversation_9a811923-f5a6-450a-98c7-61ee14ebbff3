{% comment %}
  Main Collection Section - Product listing with filters and grid
  Includes conditional hero banner for AYLA Delight collection
{% endcomment %}

<div class="main-collection collection-ayla-delight" data-collection-handle="{{ collection.handle }}">
  <div class="collection-container">

    <!-- Filters Sidebar -->
    <aside class="collection-filters">
      <div class="filter-header">
        <h2 class="filter-title">Filtry</h2>
      </div>

      <div class="filter-divider"></div>

      <!-- Category Pills -->
      <div class="filter-options">
        <a href="/collections/all" class="filter-pill {% if collection.handle == 'all' or collection.handle == blank %}selected{% endif %}">
          <span class="filter-pill-text">Wszystkie produkty</span>
        </a>
        <a href="/collections/bestseller" class="filter-pill {% if collection.handle == 'bestseller' %}selected{% endif %}">
          <span class="filter-pill-text">Bestsellery</span>
        </a>
        <a href="/collections/new" class="filter-pill {% if collection.handle == 'new' %}selected{% endif %}">
          <span class="filter-pill-text">Now<PERSON><PERSON>ci</span>
        </a>
        <div class="filter-pill" data-filter="promotions">
          <span class="filter-pill-text">Promocje</span>
        </div>
      </div>

      <div class="filter-divider"></div>

      <!-- For Whom Accordion -->
      <div class="filter-section">
        <div class="filter-accordion open" data-accordion="for-whom">
          <span class="accordion-text">Dla kogo</span>
          <svg
            class="accordion-icon"
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none">
            <path
              d="M26 20L14 20"
              stroke="#2D4F40"
              stroke-linecap="round" />
          </svg>
        </div>
        <div class="filter-accordion-content">
          <div class="filter-pill checkbox-pill" data-filter="for-dog">
            <div class="checkbox"></div>
            <span class="filter-pill-text">Dla Psa</span>
            <svg
              class="category-icon"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none">
              <path d="M7.4581 18.2617C7.30092 18.1334 7.18474 18.0009 7.18884 17.7994C7.18884 17.7856 7.17927 17.7718 7.17244 17.7594C7.06446 17.5634 7.08633 17.4406 7.26811 17.3012C7.47997 17.1397 7.71643 17.0293 7.97613 16.9755C8.08821 16.952 8.13605 16.8913 8.16202 16.7837C8.25633 16.4 8.25223 16.0191 8.15792 15.6354C8.01577 15.0586 7.87635 14.4817 7.64536 13.9324C7.4745 13.5253 7.25171 13.143 7.03029 12.7621C6.75555 12.2901 6.53003 11.7919 6.25256 11.3227C6.03114 10.9501 5.78921 10.5899 5.58965 10.2049C5.47484 9.98404 5.36413 9.76047 5.25615 9.53552C5.05932 9.12426 4.63561 8.28793 4.48252 7.90565C4.22693 6.89406 4.65475 6.05221 4.70805 5.83278C4.83927 5.485 5.02379 5.07236 5.12083 4.89985C5.3737 4.44857 5.55822 3.96968 5.62792 3.45492C5.67166 3.1306 5.64023 2.8187 5.44477 2.53165C5.29989 2.31636 5.1714 2.09279 4.99508 1.89819C4.84063 1.72845 4.74085 1.35583 4.73539 1.30476C4.72992 1.24266 4.76136 1.10879 4.76819 1.09085C4.8297 0.951465 4.78323 0.933524 4.80373 0.788616C4.81056 0.740313 4.83927 0.701671 4.86797 0.664409C4.92128 0.595406 4.95955 0.518122 4.98962 0.435317C5.06069 0.233827 5.16184 0.175864 5.3778 0.206225C5.58419 0.235207 5.78921 0.265568 5.9956 0.29179C6.11998 0.306971 6.23889 0.261428 6.36738 0.275229C6.78289 0.319391 7.16287 0.453258 7.51824 0.67269C7.74514 0.813457 8.07317 0.743074 8.22899 0.526402C8.2864 0.447738 8.34654 0.370454 8.40394 0.29179C8.45862 0.217266 8.51056 0.228306 8.55703 0.29731C8.58846 0.345613 8.6158 0.396675 8.64451 0.446358C8.70875 0.556764 8.71831 0.555384 8.79622 0.458779C8.90283 0.327672 9.00535 0.191044 9.1434 0.0902991C9.15843 0.0792585 9.17073 0.0654578 9.18714 0.0585574C9.26231 0.0240556 9.34569 -0.0270071 9.42086 0.0171552C9.50014 0.0626976 9.4564 0.156543 9.44683 0.226926C9.39489 0.647849 9.31698 1.06187 9.21037 1.47451C9.14203 1.73673 9.09829 2.00446 9.03542 2.26943C9.01491 2.35914 9.02175 2.45022 9.04362 2.53855C9.15843 3.01605 9.17757 3.4977 9.08326 3.98072C9.06959 4.04835 9.05866 4.11735 9.03405 4.18084C8.896 4.54241 8.8755 4.92193 8.8591 5.30145C8.8427 5.66303 8.96024 5.96251 9.25958 6.20126C9.4933 6.38757 9.7489 6.51316 10.0154 6.62771C10.2587 6.73397 10.5061 6.83334 10.7563 6.92442C11.2565 7.10383 11.6652 7.42953 12.0766 7.75108C12.3295 7.94843 12.5673 8.16235 12.8065 8.37764C13.1769 8.71162 13.4544 9.12288 13.74 9.52586C14.4685 10.5499 15.1725 11.5904 15.8736 12.6338C16.1402 13.0312 16.3589 13.4549 16.5106 13.9104C17.0013 15.1386 16.9179 16.1723 16.9562 16.5587C16.9958 16.9631 16.9534 17.3605 16.9124 17.7608C16.9042 17.8422 16.907 17.9195 16.9589 17.9885C16.9876 18.0271 17.0013 18.0726 16.9999 18.121C16.9931 18.3721 16.9534 18.6067 16.7416 18.7806C16.3247 19.1229 15.849 19.2995 15.3173 19.3616C14.5738 19.4472 13.8302 19.5424 13.084 19.6142C12.6302 19.6584 12.1764 19.6818 11.7212 19.7053C11.0597 19.7398 10.3968 19.7757 9.73523 19.8102C9.41676 19.8267 9.09829 19.8419 8.77982 19.8571C7.93512 19.8985 7.09043 19.9399 6.24573 19.9841C5.6539 20.0144 5.06343 20.0075 4.47706 19.9095C4.12715 19.8516 4.05061 19.7494 4.0055 19.3906C3.96723 19.0884 4.13398 18.9076 4.33764 18.742C4.62877 18.506 4.96775 18.3928 5.33543 18.3487C5.77554 18.2962 6.21566 18.2921 6.65578 18.3252C6.92231 18.3445 7.182 18.3155 7.45947 18.2659L7.4581 18.2617ZM9.36892 17.7856C9.59992 17.6904 9.81177 17.6448 10.0264 17.6103C10.1972 17.5827 10.3571 17.5206 10.513 17.4433C10.6223 17.3895 10.6373 17.3357 10.5813 17.2267C10.5403 17.1466 10.4761 17.0914 10.4118 17.0335C10.2464 16.883 10.04 16.8057 9.84595 16.705C9.71473 16.6374 9.68603 16.6512 9.6956 16.8002C9.71746 17.1107 9.61632 17.3826 9.45503 17.6379C9.43043 17.6766 9.40856 17.7152 9.36756 17.7842L9.36892 17.7856Z" fill="#2D4F40" />
            </svg>
          </div>
          <div class="filter-pill checkbox-pill" data-filter="for-cat">
            <div class="checkbox"></div>
            <span class="filter-pill-text">Dla Kota</span>
            <svg
              class="category-icon"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none">
              <path d="M7.4581 18.2617C7.30092 18.1334 7.18474 18.0009 7.18884 17.7994C7.18884 17.7856 7.17927 17.7718 7.17244 17.7594C7.06446 17.5634 7.08633 17.4406 7.26811 17.3012C7.47997 17.1397 7.71643 17.0293 7.97613 16.9755C8.08821 16.952 8.13605 16.8913 8.16202 16.7837C8.25633 16.4 8.25223 16.0191 8.15792 15.6354C8.01577 15.0586 7.87635 14.4817 7.64536 13.9324C7.4745 13.5253 7.25171 13.143 7.03029 12.7621C6.75555 12.2901 6.53003 11.7919 6.25256 11.3227C6.03114 10.9501 5.78921 10.5899 5.58965 10.2049C5.47484 9.98404 5.36413 9.76047 5.25615 9.53552C5.05932 9.12426 4.63561 8.28793 4.48252 7.90565C4.22693 6.89406 4.65475 6.05221 4.70805 5.83278C4.83927 5.485 5.02379 5.07236 5.12083 4.89985C5.3737 4.44857 5.55822 3.96968 5.62792 3.45492C5.67166 3.1306 5.64023 2.8187 5.44477 2.53165C5.29989 2.31636 5.1714 2.09279 4.99508 1.89819C4.84063 1.72845 4.74085 1.35583 4.73539 1.30476C4.72992 1.24266 4.76136 1.10879 4.76819 1.09085C4.8297 0.951465 4.78323 0.933524 4.80373 0.788616C4.81056 0.740313 4.83927 0.701671 4.86797 0.664409C4.92128 0.595406 4.95955 0.518122 4.98962 0.435317C5.06069 0.233827 5.16184 0.175864 5.3778 0.206225C5.58419 0.235207 5.78921 0.265568 5.9956 0.29179C6.11998 0.306971 6.23889 0.261428 6.36738 0.275229C6.78289 0.319391 7.16287 0.453258 7.51824 0.67269C7.74514 0.813457 8.07317 0.743074 8.22899 0.526402C8.2864 0.447738 8.34654 0.370454 8.40394 0.29179C8.45862 0.217266 8.51056 0.228306 8.55703 0.29731C8.58846 0.345613 8.6158 0.396675 8.64451 0.446358C8.70875 0.556764 8.71831 0.555384 8.79622 0.458779C8.90283 0.327672 9.00535 0.191044 9.1434 0.0902991C9.15843 0.0792585 9.17073 0.0654578 9.18714 0.0585574C9.26231 0.0240556 9.34569 -0.0270071 9.42086 0.0171552C9.50014 0.0626976 9.4564 0.156543 9.44683 0.226926C9.39489 0.647849 9.31698 1.06187 9.21037 1.47451C9.14203 1.73673 9.09829 2.00446 9.03542 2.26943C9.01491 2.35914 9.02175 2.45022 9.04362 2.53855C9.15843 3.01605 9.17757 3.4977 9.08326 3.98072C9.06959 4.04835 9.05866 4.11735 9.03405 4.18084C8.896 4.54241 8.8755 4.92193 8.8591 5.30145C8.8427 5.66303 8.96024 5.96251 9.25958 6.20126C9.4933 6.38757 9.7489 6.51316 10.0154 6.62771C10.2587 6.73397 10.5061 6.83334 10.7563 6.92442C11.2565 7.10383 11.6652 7.42953 12.0766 7.75108C12.3295 7.94843 12.5673 8.16235 12.8065 8.37764C13.1769 8.71162 13.4544 9.12288 13.74 9.52586C14.4685 10.5499 15.1725 11.5904 15.8736 12.6338C16.1402 13.0312 16.3589 13.4549 16.5106 13.9104C17.0013 15.1386 16.9179 16.1723 16.9562 16.5587C16.9958 16.9631 16.9534 17.3605 16.9124 17.7608C16.9042 17.8422 16.907 17.9195 16.9589 17.9885C16.9876 18.0271 17.0013 18.0726 16.9999 18.121C16.9931 18.3721 16.9534 18.6067 16.7416 18.7806C16.3247 19.1229 15.849 19.2995 15.3173 19.3616C14.5738 19.4472 13.8302 19.5424 13.084 19.6142C12.6302 19.6584 12.1764 19.6818 11.7212 19.7053C11.0597 19.7398 10.3968 19.7757 9.73523 19.8102C9.41676 19.8267 9.09829 19.8419 8.77982 19.8571C7.93512 19.8985 7.09043 19.9399 6.24573 19.9841C5.6539 20.0144 5.06343 20.0075 4.47706 19.9095C4.12715 19.8516 4.05061 19.7494 4.0055 19.3906C3.96723 19.0884 4.13398 18.9076 4.33764 18.742C4.62877 18.506 4.96775 18.3928 5.33543 18.3487C5.77554 18.2962 6.21566 18.2921 6.65578 18.3252C6.92231 18.3445 7.182 18.3155 7.45947 18.2659L7.4581 18.2617ZM9.36892 17.7856C9.59992 17.6904 9.81177 17.6448 10.0264 17.6103C10.1972 17.5827 10.3571 17.5206 10.513 17.4433C10.6223 17.3895 10.6373 17.3357 10.5813 17.2267C10.5403 17.1466 10.4761 17.0914 10.4118 17.0335C10.2464 16.883 10.04 16.8057 9.84595 16.705C9.71473 16.6374 9.68603 16.6512 9.6956 16.8002C9.71746 17.1107 9.61632 17.3826 9.45503 17.6379C9.43043 17.6766 9.40856 17.7152 9.36756 17.7842L9.36892 17.7856Z" fill="#2D4F40" />
            </svg>
          </div>
        </div>
      </div>

      <div class="filter-divider"></div>

      <!-- Product Lines Accordion -->
      <div class="filter-section">
        <div class="filter-accordion open" data-accordion="product-lines">
          <span class="accordion-text">Linia Produktów</span>
          <svg
            class="accordion-icon"
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none">
            <path
              d="M26 20L14 20"
              stroke="#2D4F40"
              stroke-linecap="round" />
          </svg>
        </div>
        <div class="filter-accordion-content">
          <a href="/collections/ayla-delight" class="filter-pill product-line-filter {% if collection.handle == 'ayla-delight' %}selected{% endif %}">
            <span class="filter-pill-text">AYLA Delight</span>
          </a>
          <a href="/collections/ayla-help" class="filter-pill product-line-filter {% if collection.handle == 'ayla-help' %}selected{% endif %}">
            <span class="filter-pill-text">AYLA Help</span>
          </a>
          <a href="/collections/ayla-rescue" class="filter-pill product-line-filter {% if collection.handle == 'ayla-rescue' %}selected{% endif %}">
            <span class="filter-pill-text">AYLA Rescue</span>
          </a>
        </div>
      </div>

      <div class="filter-divider"></div>

      <!-- Meat Type Accordion -->
      <div class="filter-section">
        <div class="filter-accordion open" data-accordion="meat">
          <span class="accordion-text">Mięso</span>
          <svg
            class="accordion-icon"
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none">
            <path
              d="M26 20L14 20"
              stroke="#2D4F40"
              stroke-linecap="round" />
          </svg>
        </div>
        <div class="filter-accordion-content">
          <div class="filter-pill" data-filter="duck">
            <span class="filter-pill-text">Z kaczki</span>
          </div>
          <div class="filter-pill" data-filter="chicken">
            <span class="filter-pill-text">Z kurczaka</span>
          </div>
          <div class="filter-pill" data-filter="turkey">
            <span class="filter-pill-text">Z indyka</span>
          </div>
          <div class="filter-pill" data-filter="offal">
            <span class="filter-pill-text">Z podrobów</span>
          </div>
          <div class="filter-pill" data-filter="blend">
            <span class="filter-pill-text">Blend</span>
          </div>
        </div>
      </div>
    </aside>

    <!-- Product Grid Section -->
    <main class="collection-products">
      <div class="product-grid">
        {% comment %} Adjust pagination for promo tile on first page of "all" collection {% endcomment %}
        {% if collection.handle == 'all' or collection.handle == blank %}
          {% assign products_per_page_first = 11 %}
        {% else %}
          {% assign products_per_page_first = 12 %}
        {% endif %}

        {% paginate collection.products by 12 %}
          {% assign product_limit = 12 %}
          {% if paginate.current_page == 1 %}
            {% if collection.handle == 'all' or collection.handle == blank %}
              {% assign product_limit = 11 %}
            {% endif %}
          {% endif %}

          {% for product in collection.products limit: product_limit %}
            <div
              class="product-tile"
              data-bestseller="{% if product.tags contains 'bestseller' %}true{% else %}false{% endif %}"
              data-new="{% if product.tags contains 'new' %}true{% else %}false{% endif %}"
              data-promotion="{% if product.tags contains 'promotion' %}true{% else %}false{% endif %}"
              data-fordog="{% if product.tags contains 'for-dog' %}true{% else %}false{% endif %}"
              data-forcat="{% if product.tags contains 'for-cat' %}true{% else %}false{% endif %}"
              data-line="{% if product.tags contains 'ayla-delight' %}ayla-delight{% elsif product.tags contains 'ayla-help' %}ayla-help{% elsif product.tags contains 'ayla-rescue' %}ayla-rescue{% else %}unknown{% endif %}"
              data-meat="{% if product.tags contains 'duck' %}duck{% elsif product.tags contains 'chicken' %}chicken{% elsif product.tags contains 'turkey' %}turkey{% elsif product.tags contains 'offal' %}offal{% elsif product.tags contains 'blend' %}blend{% else %}unknown{% endif %}">

              <!-- Product Image -->
              <div class="product-image">
                <a href="{{ product.url }}" class="product-image-link">
                  {% if product.featured_image %}
                    <img
                      src="{{ product.featured_image | img_url: '286x286' }}"
                      srcset="{{ product.featured_image | img_url: '286x286' }} 1x,
                                 {{ product.featured_image | img_url: '572x572' }} 2x"
                      alt="{{ product.featured_image.alt | default: product.title | escape }}"
                      width="286"
                      height="286"
                      loading="lazy">
                  {% else %}
                    <div class="product-image-placeholder"></div>
                  {% endif %}
                </a>

                <!-- Product Badges -->
                {% if product.tags contains 'bestseller' %}
                  <div class="product-badge bestseller-badge">
                    <span>Bestseller</span>
                  </div>
                {% endif %}

                {% if product.tags contains 'new' %}
                  <div class="product-badge new-badge">
                    <span>Nowość!</span>
                  </div>
                {% endif %}

                <!-- For Whom Badge -->
                <div class="product-badge for-whom-badge">
                  {% if product.tags contains 'for-dog' and product.tags contains 'for-cat' %}
                    <span>DLA PSA I KOTA</span>
                    <svg
                      width="15"
                      height="24"
                      viewBox="0 0 15 24"
                      fill="none">
                      <path d="M8.13375 23.9995C8.69052 24.0078 9.30198 23.915 9.90846 23.7825C10.1322 23.7345 10.3559 23.7179 10.5812 23.7229C11.1645 23.7378 11.7445 23.6947 12.3261 23.6699C12.7917 23.65 13.2541 23.5639 13.7114 23.4711C13.8042 23.4529 13.8639 23.39 13.8854 23.3055C13.95 23.0687 14.1174 22.9279 14.3162 22.8119C14.5068 22.701 14.9194 22.161 14.999 21.5399C14.999 21.5399 15.0122 20.9718 14.9443 20.6869C14.8498 20.2861 14.2218 17.5482 14.1157 17.2071C13.96 16.7135 13.8075 16.2182 13.612 15.7363C13.3485 15.0837 13.0271 14.4576 12.7338 13.8199C12.6956 13.7371 12.2184 12.798 12.0129 12.4485C11.6997 11.9152 11.2639 11.478 10.8928 10.9844C10.6608 10.6747 8.70544 7.80762 8.57453 7.61549C8.46682 7.45649 8.35414 7.30411 8.20169 7.1832C8.12878 7.12689 8.07244 7.0507 8.03102 6.96788C7.86531 6.63828 7.80068 5.70413 7.79902 5.59813C7.79074 5.141 8.00781 3.36546 7.90508 2.92985C7.88685 2.85201 7.83879 2.7311 7.80731 2.65822C7.65652 2.30709 7.60515 1.93442 7.59189 1.55348C7.57532 1.05493 7.32676 0.736928 6.85947 0.551424C6.21156 0.293043 5.54377 0.11582 4.85609 0.0164427C4.61084 -0.0199957 4.35234 0.0131301 4.10212 0.029693C3.99441 0.0363181 3.88836 0.130727 3.83202 0.223479C3.68123 0.528236 3.27194 0.828024 2.8063 0.847899C2.43015 0.862806 2.05731 0.803179 1.68281 0.786616C1.39614 0.773366 1.10781 0.765085 0.821141 0.783304C0.496357 0.803179 0.383677 0.975433 0.526184 1.41766C0.56264 1.53195 0.552698 1.65617 0.584182 1.77211C0.599096 1.82842 0.618979 1.88474 0.65212 1.93277C0.73663 2.05368 0.743258 2.35512 0.970275 2.55222C1.2818 2.85532 2.31249 3.44496 2.43015 3.57084C2.64059 3.7315 2.75493 3.79775 2.95047 3.84578C3.12611 3.8905 3.29679 3.9435 3.45918 4.02135C3.65637 4.11575 3.78231 4.41223 3.72763 4.62424C3.71768 4.66564 3.59506 5.38116 3.4741 5.68426C3.34816 6.00061 3.21394 6.31365 3.08635 6.62834C2.95875 6.94635 2.95378 7.28589 2.89578 7.61715C2.85932 7.82418 2.85933 8.03619 2.7831 8.2366C2.71848 8.40554 2.62899 8.5132 2.4782 8.65895C2.27438 8.84777 2.07057 9.09621 1.75407 9.81173C1.57179 10.2225 1.51545 10.9132 1.51545 11.2262C1.51545 11.2262 1.47568 11.9434 1.64304 12.4071C1.76401 12.74 1.81206 12.8477 1.89657 13.0696C2.01422 13.3827 2.37381 14.1048 2.56105 14.3582C2.85767 14.8253 2.90241 15.2195 2.85601 15.9715C2.83281 16.3276 2.78973 16.6837 2.72842 17.0348C2.65219 17.4787 2.57597 17.9226 2.41192 18.3416C2.22799 18.807 2.23461 19.289 2.27107 19.7677C2.3009 20.1685 2.24622 20.0509 2.13354 20.4318C2.09211 20.5726 2.06891 20.7184 2.04074 20.8641C1.99268 21.1159 1.68281 21.4753 1.48894 21.6343C1.37957 21.7237 1.25032 21.7668 1.10284 21.7436C0.894051 21.7088 0.690233 21.7552 0.489729 21.7999C0.386991 21.8231 0.307452 21.9043 0.264368 22.0053C0.247798 22.0467 0.219627 22.0815 0.186486 22.1097C-0.0819577 22.3366 -0.0140172 22.5039 0.101977 22.7755C0.161631 22.9179 0.275969 23.0306 0.386992 23.1316C0.494701 23.2293 0.647148 23.2492 0.786341 23.2508C1.3282 23.2525 1.87006 23.3055 2.41523 23.2343C2.75162 23.1896 2.95047 23.0007 3.10623 22.7308C3.13937 22.6728 3.13937 22.6032 3.14103 22.5403C3.146 22.0517 3.33656 21.6343 3.62157 21.2517C3.77071 21.0513 4.04744 20.6621 4.18994 20.4583C4.4153 20.1354 4.70198 20.3656 4.98368 20.094C5.04002 20.0393 5.03505 19.9449 4.97208 19.8985C4.80803 19.7793 4.78152 19.7064 4.83288 19.5093C4.86768 19.3751 4.89917 19.2426 4.93562 19.1085C4.9787 18.9511 5.0831 18.8302 5.19413 18.7209C5.53217 18.3913 5.80392 18.017 6.02597 17.6012C6.09391 17.4737 6.18836 17.3594 6.26956 17.2402C6.3027 17.1921 6.33584 17.169 6.39052 17.2137C6.61091 17.3926 6.86113 17.53 7.09477 17.689C7.1246 17.7089 7.18425 17.7271 7.12626 17.7801C6.92409 17.9706 6.76999 18.2141 6.72028 18.4807C6.63743 19.1963 6.74016 19.289 7.00861 19.7544C7.01192 19.7594 7.01358 19.7627 7.01689 19.7677C7.35659 20.2066 7.72611 20.9453 8.07409 21.2385C8.44031 21.5465 8.80818 21.8529 9.11308 22.2239C9.13627 22.2521 9.18433 22.2769 9.16942 22.3167C9.1545 22.3564 9.10148 22.3465 9.06668 22.3498C8.66236 22.3813 8.28123 22.5138 7.89016 22.6049C7.72225 22.6435 7.57311 22.7374 7.44276 22.8865C7.32013 23.0289 7.27871 23.2906 7.25551 23.4115C7.21905 23.6003 7.22237 23.7229 7.32013 23.8703C7.37316 23.9498 7.54384 23.9945 7.63166 23.9962C7.77748 23.9995 7.92331 23.9962 8.13541 23.9962L8.13375 23.9995Z" fill="#2D4F40" />
                    </svg>
                    <svg
                      width="16"
                      height="24"
                      viewBox="0 0 16 24"
                      fill="none">
                      <path d="M4.25613 21.9141C4.06267 21.76 3.91968 21.6011 3.92472 21.3593C3.92472 21.3427 3.91295 21.3262 3.90454 21.3112C3.77164 21.0761 3.79855 20.9287 4.02229 20.7614C4.28304 20.5677 4.57407 20.4352 4.89369 20.3706C5.03164 20.3424 5.09052 20.2696 5.12248 20.1404C5.23856 19.68 5.23351 19.2229 5.11744 18.7625C4.94248 18.0703 4.77089 17.378 4.48659 16.7189C4.27631 16.2304 4.00211 15.7716 3.72958 15.3146C3.39145 14.7482 3.11388 14.1503 2.77239 13.5873C2.49986 13.1401 2.2021 12.7079 1.9565 12.2458C1.81519 11.9809 1.67892 11.7126 1.54603 11.4426C1.30378 10.9491 0.782287 9.94552 0.593875 9.48678C0.279295 8.27287 0.805842 7.26266 0.87145 6.99934C1.03295 6.58201 1.26005 6.08683 1.37949 5.87982C1.6907 5.33828 1.9178 4.76362 2.0036 4.1459C2.05743 3.75672 2.01874 3.38244 1.77818 3.03798C1.59986 2.77963 1.44173 2.51134 1.22472 2.27783C1.03462 2.07413 0.911821 1.62699 0.905092 1.56572C0.898363 1.49119 0.937053 1.33055 0.945464 1.30902C1.02117 1.14176 0.96397 1.12023 0.989204 0.946339C0.997615 0.888376 1.03294 0.842006 1.06827 0.797291C1.13388 0.714487 1.18098 0.621746 1.21799 0.522381C1.30547 0.280592 1.42995 0.211036 1.69575 0.24747C1.94977 0.282248 2.2021 0.318682 2.45612 0.350148C2.60921 0.368365 2.75556 0.313714 2.91369 0.330275C3.4251 0.383269 3.89276 0.54391 4.33015 0.807228C4.6094 0.976149 5.01314 0.891688 5.20491 0.631683C5.27557 0.537286 5.34958 0.444545 5.42024 0.350148C5.48753 0.260719 5.55145 0.273968 5.60865 0.356772C5.64734 0.414735 5.68099 0.47601 5.71632 0.53563C5.79538 0.668117 5.80715 0.66646 5.90304 0.550534C6.03426 0.393206 6.16043 0.229253 6.33033 0.108359C6.34884 0.0951102 6.36398 0.0785493 6.38417 0.0702689C6.47669 0.0288667 6.57931 -0.0324085 6.67183 0.0205863C6.7694 0.0752372 6.71557 0.187851 6.70379 0.272312C6.63987 0.777418 6.54398 1.27424 6.41276 1.76941C6.32865 2.08407 6.27482 2.40535 6.19744 2.72332C6.1722 2.83097 6.18062 2.94027 6.20753 3.04626C6.34884 3.61926 6.37239 4.19724 6.25632 4.77687C6.23949 4.85802 6.22604 4.94082 6.19576 5.017C6.02585 5.4509 6.00062 5.90632 5.98043 6.36175C5.96024 6.79564 6.10491 7.15501 6.47333 7.44151C6.76099 7.66509 7.07557 7.81579 7.40361 7.95325C7.70305 8.08076 8.00754 8.2 8.31539 8.3093C8.93109 8.5246 9.43408 8.91543 9.94043 9.3013C10.2516 9.53812 10.5444 9.79482 10.8388 10.0532C11.2946 10.4539 11.6361 10.9475 11.9877 11.431C12.8844 12.6598 13.7507 13.9085 14.6137 15.1605C14.9417 15.6375 15.2109 16.1459 15.3976 16.6924C16.0016 18.1663 15.8989 19.4067 15.9461 19.8705C15.9948 20.3557 15.9427 20.8326 15.8922 21.3129C15.8821 21.4106 15.8855 21.5034 15.9494 21.5862C15.9847 21.6325 16.0016 21.6872 15.9999 21.7451C15.9915 22.0465 15.9427 22.3281 15.6819 22.5368C15.1689 22.9475 14.5834 23.1594 13.929 23.234C13.0139 23.3366 12.0988 23.4509 11.1803 23.537C10.6217 23.59 10.0632 23.6182 9.50305 23.6463C8.68885 23.6877 7.87296 23.7308 7.05875 23.7722C6.66679 23.7921 6.27482 23.8103 5.88286 23.8285C4.84323 23.8782 3.8036 23.9279 2.76398 23.9809C2.03556 24.0173 1.30883 24.009 0.58715 23.8914C0.156495 23.8219 0.0622872 23.6993 0.00677314 23.2687C-0.0403297 22.9061 0.164903 22.6891 0.415558 22.4904C0.773876 22.2072 1.19108 22.0714 1.6436 22.0184C2.18528 21.9555 2.72697 21.9505 3.26865 21.9902C3.59669 22.0134 3.91631 21.9787 4.25781 21.919L4.25613 21.9141ZM6.6079 21.3427C6.8922 21.2284 7.15295 21.1738 7.41706 21.1324C7.62734 21.0993 7.82417 21.0247 8.01595 20.932C8.15053 20.8674 8.16903 20.8028 8.10006 20.672C8.04959 20.5759 7.97052 20.5097 7.89146 20.4401C7.68791 20.2596 7.43389 20.1669 7.19501 20.046C7.03352 19.9648 6.99819 19.9814 7.00996 20.1603C7.03688 20.5329 6.91239 20.8591 6.71389 21.1655C6.68361 21.2119 6.65669 21.2583 6.60623 21.3411L6.6079 21.3427Z" fill="#2D4F40" />
                    </svg>
                  {% elsif product.tags contains 'for-dog' %}
                    <span>Dla PSA</span>
                    <svg
                      width="15"
                      height="24"
                      viewBox="0 0 15 24"
                      fill="none">
                      <path d="M8.13375 23.9995C8.69052 24.0078 9.30198 23.915 9.90846 23.7825C10.1322 23.7345 10.3559 23.7179 10.5812 23.7229C11.1645 23.7378 11.7445 23.6947 12.3261 23.6699C12.7917 23.65 13.2541 23.5639 13.7114 23.4711C13.8042 23.4529 13.8639 23.39 13.8854 23.3055C13.95 23.0687 14.1174 22.9279 14.3162 22.8119C14.5068 22.701 14.9194 22.161 14.999 21.5399C14.999 21.5399 15.0122 20.9718 14.9443 20.6869C14.8498 20.2861 14.2218 17.5482 14.1157 17.2071C13.96 16.7135 13.8075 16.2182 13.612 15.7363C13.3485 15.0837 13.0271 14.4576 12.7338 13.8199C12.6956 13.7371 12.2184 12.798 12.0129 12.4485C11.6997 11.9152 11.2639 11.478 10.8928 10.9844C10.6608 10.6747 8.70544 7.80762 8.57453 7.61549C8.46682 7.45649 8.35414 7.30411 8.20169 7.1832C8.12878 7.12689 8.07244 7.0507 8.03102 6.96788C7.86531 6.63828 7.80068 5.70413 7.79902 5.59813C7.79074 5.141 8.00781 3.36546 7.90508 2.92985C7.88685 2.85201 7.83879 2.7311 7.80731 2.65822C7.65652 2.30709 7.60515 1.93442 7.59189 1.55348C7.57532 1.05493 7.32676 0.736928 6.85947 0.551424C6.21156 0.293043 5.54377 0.11582 4.85609 0.0164427C4.61084 -0.0199957 4.35234 0.0131301 4.10212 0.029693C3.99441 0.0363181 3.88836 0.130727 3.83202 0.223479C3.68123 0.528236 3.27194 0.828024 2.8063 0.847899C2.43015 0.862806 2.05731 0.803179 1.68281 0.786616C1.39614 0.773366 1.10781 0.765085 0.821141 0.783304C0.496357 0.803179 0.383677 0.975433 0.526184 1.41766C0.56264 1.53195 0.552698 1.65617 0.584182 1.77211C0.599096 1.82842 0.618979 1.88474 0.65212 1.93277C0.73663 2.05368 0.743258 2.35512 0.970275 2.55222C1.2818 2.85532 2.31249 3.44496 2.43015 3.57084C2.64059 3.7315 2.75493 3.79775 2.95047 3.84578C3.12611 3.8905 3.29679 3.9435 3.45918 4.02135C3.65637 4.11575 3.78231 4.41223 3.72763 4.62424C3.71768 4.66564 3.59506 5.38116 3.4741 5.68426C3.34816 6.00061 3.21394 6.31365 3.08635 6.62834C2.95875 6.94635 2.95378 7.28589 2.89578 7.61715C2.85932 7.82418 2.85933 8.03619 2.7831 8.2366C2.71848 8.40554 2.62899 8.5132 2.4782 8.65895C2.27438 8.84777 2.07057 9.09621 1.75407 9.81173C1.57179 10.2225 1.51545 10.9132 1.51545 11.2262C1.51545 11.2262 1.47568 11.9434 1.64304 12.4071C1.76401 12.74 1.81206 12.8477 1.89657 13.0696C2.01422 13.3827 2.37381 14.1048 2.56105 14.3582C2.85767 14.8253 2.90241 15.2195 2.85601 15.9715C2.83281 16.3276 2.78973 16.6837 2.72842 17.0348C2.65219 17.4787 2.57597 17.9226 2.41192 18.3416C2.22799 18.807 2.23461 19.289 2.27107 19.7677C2.3009 20.1685 2.24622 20.0509 2.13354 20.4318C2.09211 20.5726 2.06891 20.7184 2.04074 20.8641C1.99268 21.1159 1.68281 21.4753 1.48894 21.6343C1.37957 21.7237 1.25032 21.7668 1.10284 21.7436C0.894051 21.7088 0.690233 21.7552 0.489729 21.7999C0.386991 21.8231 0.307452 21.9043 0.264368 22.0053C0.247798 22.0467 0.219627 22.0815 0.186486 22.1097C-0.0819577 22.3366 -0.0140172 22.5039 0.101977 22.7755C0.161631 22.9179 0.275969 23.0306 0.386992 23.1316C0.494701 23.2293 0.647148 23.2492 0.786341 23.2508C1.3282 23.2525 1.87006 23.3055 2.41523 23.2343C2.75162 23.1896 2.95047 23.0007 3.10623 22.7308C3.13937 22.6728 3.13937 22.6032 3.14103 22.5403C3.146 22.0517 3.33656 21.6343 3.62157 21.2517C3.77071 21.0513 4.04744 20.6621 4.18994 20.4583C4.4153 20.1354 4.70198 20.3656 4.98368 20.094C5.04002 20.0393 5.03505 19.9449 4.97208 19.8985C4.80803 19.7793 4.78152 19.7064 4.83288 19.5093C4.86768 19.3751 4.89917 19.2426 4.93562 19.1085C4.9787 18.9511 5.0831 18.8302 5.19413 18.7209C5.53217 18.3913 5.80392 18.017 6.02597 17.6012C6.09391 17.4737 6.18836 17.3594 6.26956 17.2402C6.3027 17.1921 6.33584 17.169 6.39052 17.2137C6.61091 17.3926 6.86113 17.53 7.09477 17.689C7.1246 17.7089 7.18425 17.7271 7.12626 17.7801C6.92409 17.9706 6.76999 18.2141 6.72028 18.4807C6.63743 19.1963 6.74016 19.289 7.00861 19.7544C7.01192 19.7594 7.01358 19.7627 7.01689 19.7677C7.35659 20.2066 7.72611 20.9453 8.07409 21.2385C8.44031 21.5465 8.80818 21.8529 9.11308 22.2239C9.13627 22.2521 9.18433 22.2769 9.16942 22.3167C9.1545 22.3564 9.10148 22.3465 9.06668 22.3498C8.66236 22.3813 8.28123 22.5138 7.89016 22.6049C7.72225 22.6435 7.57311 22.7374 7.44276 22.8865C7.32013 23.0289 7.27871 23.2906 7.25551 23.4115C7.21905 23.6003 7.22237 23.7229 7.32013 23.8703C7.37316 23.9498 7.54384 23.9945 7.63166 23.9962C7.77748 23.9995 7.92331 23.9962 8.13541 23.9962L8.13375 23.9995Z" fill="#2D4F40" />
                    </svg>
                  {% elsif product.tags contains 'for-cat' %}
                    <span>Dla kota</span>
                    <svg
                      width="16"
                      height="24"
                      viewBox="0 0 16 24"
                      fill="none">
                      <path d="M4.25613 21.9141C4.06267 21.76 3.91968 21.6011 3.92472 21.3593C3.92472 21.3427 3.91295 21.3262 3.90454 21.3112C3.77164 21.0761 3.79855 20.9287 4.02229 20.7614C4.28304 20.5677 4.57407 20.4352 4.89369 20.3706C5.03164 20.3424 5.09052 20.2696 5.12248 20.1404C5.23856 19.68 5.23351 19.2229 5.11744 18.7625C4.94248 18.0703 4.77089 17.378 4.48659 16.7189C4.27631 16.2304 4.00211 15.7716 3.72958 15.3146C3.39145 14.7482 3.11388 14.1503 2.77239 13.5873C2.49986 13.1401 2.2021 12.7079 1.9565 12.2458C1.81519 11.9809 1.67892 11.7126 1.54603 11.4426C1.30378 10.9491 0.782287 9.94552 0.593875 9.48678C0.279295 8.27287 0.805842 7.26266 0.87145 6.99934C1.03295 6.58201 1.26005 6.08683 1.37949 5.87982C1.6907 5.33828 1.9178 4.76362 2.0036 4.1459C2.05743 3.75672 2.01874 3.38244 1.77818 3.03798C1.59986 2.77963 1.44173 2.51134 1.22472 2.27783C1.03462 2.07413 0.911821 1.62699 0.905092 1.56572C0.898363 1.49119 0.937053 1.33055 0.945464 1.30902C1.02117 1.14176 0.96397 1.12023 0.989204 0.946339C0.997615 0.888376 1.03294 0.842006 1.06827 0.797291C1.13388 0.714487 1.18098 0.621746 1.21799 0.522381C1.30547 0.280592 1.42995 0.211036 1.69575 0.24747C1.94977 0.282248 2.2021 0.318682 2.45612 0.350148C2.60921 0.368365 2.75556 0.313714 2.91369 0.330275C3.4251 0.383269 3.89276 0.54391 4.33015 0.807228C4.6094 0.976149 5.01314 0.891688 5.20491 0.631683C5.27557 0.537286 5.34958 0.444545 5.42024 0.350148C5.48753 0.260719 5.55145 0.273968 5.60865 0.356772C5.64734 0.414735 5.68099 0.47601 5.71632 0.53563C5.79538 0.668117 5.80715 0.66646 5.90304 0.550534C6.03426 0.393206 6.16043 0.229253 6.33033 0.108359C6.34884 0.0951102 6.36398 0.0785493 6.38417 0.0702689C6.47669 0.0288667 6.57931 -0.0324085 6.67183 0.0205863C6.7694 0.0752372 6.71557 0.187851 6.70379 0.272312C6.63987 0.777418 6.54398 1.27424 6.41276 1.76941C6.32865 2.08407 6.27482 2.40535 6.19744 2.72332C6.1722 2.83097 6.18062 2.94027 6.20753 3.04626C6.34884 3.61926 6.37239 4.19724 6.25632 4.77687C6.23949 4.85802 6.22604 4.94082 6.19576 5.017C6.02585 5.4509 6.00062 5.90632 5.98043 6.36175C5.96024 6.79564 6.10491 7.15501 6.47333 7.44151C6.76099 7.66509 7.07557 7.81579 7.40361 7.95325C7.70305 8.08076 8.00754 8.2 8.31539 8.3093C8.93109 8.5246 9.43408 8.91543 9.94043 9.3013C10.2516 9.53812 10.5444 9.79482 10.8388 10.0532C11.2946 10.4539 11.6361 10.9475 11.9877 11.431C12.8844 12.6598 13.7507 13.9085 14.6137 15.1605C14.9417 15.6375 15.2109 16.1459 15.3976 16.6924C16.0016 18.1663 15.8989 19.4067 15.9461 19.8705C15.9948 20.3557 15.9427 20.8326 15.8922 21.3129C15.8821 21.4106 15.8855 21.5034 15.9494 21.5862C15.9847 21.6325 16.0016 21.6872 15.9999 21.7451C15.9915 22.0465 15.9427 22.3281 15.6819 22.5368C15.1689 22.9475 14.5834 23.1594 13.929 23.234C13.0139 23.3366 12.0988 23.4509 11.1803 23.537C10.6217 23.59 10.0632 23.6182 9.50305 23.6463C8.68885 23.6877 7.87296 23.7308 7.05875 23.7722C6.66679 23.7921 6.27482 23.8103 5.88286 23.8285C4.84323 23.8782 3.8036 23.9279 2.76398 23.9809C2.03556 24.0173 1.30883 24.009 0.58715 23.8914C0.156495 23.8219 0.0622872 23.6993 0.00677314 23.2687C-0.0403297 22.9061 0.164903 22.6891 0.415558 22.4904C0.773876 22.2072 1.19108 22.0714 1.6436 22.0184C2.18528 21.9555 2.72697 21.9505 3.26865 21.9902C3.59669 22.0134 3.91631 21.9787 4.25781 21.919L4.25613 21.9141ZM6.6079 21.3427C6.8922 21.2284 7.15295 21.1738 7.41706 21.1324C7.62734 21.0993 7.82417 21.0247 8.01595 20.932C8.15053 20.8674 8.16903 20.8028 8.10006 20.672C8.04959 20.5759 7.97052 20.5097 7.89146 20.4401C7.68791 20.2596 7.43389 20.1669 7.19501 20.046C7.03352 19.9648 6.99819 19.9814 7.00996 20.1603C7.03688 20.5329 6.91239 20.8591 6.71389 21.1655C6.68361 21.2119 6.65669 21.2583 6.60623 21.3411L6.6079 21.3427Z" fill="#2D4F40" />
                    </svg>
                  {% endif %}
                </div>
              </div>

              <!-- Product Details -->
              <div class="product-details">
                <div class="product-info">
                  <h3 class="product-name">
                    <a href="{{ product.url }}">{{ product.title | escape }}</a>
                  </h3>
                  <p class="product-description">
                    {{ product.metafields.custom.short_description | default: product.description | strip_html | truncate: 60 }}
                  </p>
              </div>

                <!-- Add to Cart Button (full pill clickable) -->
                {%- assign card_variant = product.selected_or_first_available_variant -%}
                <form
                  action="/cart/add"
                  method="post"
                  enctype="multipart/form-data">
                  <input
                    type="hidden"
                    name="id"
                    value="{{ card_variant.id }}">
                  <button
                    type="submit"
                    class="product-add-to-cart"
                    data-success-icon="{{ '3589a8fde2a91501906cf41d590682ce1538c36e.svg' | asset_url }}">
                    <span class="add-to-cart-button">
                      <svg
                        width="28"
                        height="28"
                        viewBox="0 0 28 28"
                        fill="none">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M20.7269 24.0784H7.28688C6.04928 24.0784 5.04688 23.076 5.04688 21.8384V10.0784C5.04688 9.15108 5.79952 8.39844 6.72688 8.39844H21.2869C22.2142 8.39844 22.9669 9.15108 22.9669 10.0784V21.8384C22.9669 23.076 21.9645 24.0784 20.7269 24.0784Z"
                          stroke="#2D4F40"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                        <path
                          d="M18.4808 12.0847C18.3262 12.0847 18.2008 12.2101 18.2019 12.3647C18.2019 12.5192 18.3273 12.6447 18.4819 12.6447C18.6364 12.6447 18.7619 12.5192 18.7619 12.3647C18.7619 12.2101 18.6364 12.0847 18.4808 12.0847"
                          stroke="#2D4F40"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                        <path
                          d="M9.52763 12.0847C9.37307 12.0847 9.24763 12.2101 9.24875 12.3647C9.24875 12.5192 9.37419 12.6447 9.52875 12.6447C9.68331 12.6447 9.80875 12.5192 9.80875 12.3647C9.80875 12.2101 9.68331 12.0847 9.52763 12.0847"
                          stroke="#2D4F40"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                        <path
                          d="M9.5312 8.39406V8.11406V8.11406C9.5312 5.79454 11.4117 3.91406 13.7312 3.91406H14.2912C16.6108 3.91406 18.4912 5.79454 18.4912 8.11406V8.11406V8.39406"
                          stroke="#2D4F40"
                          stroke-linecap="round"
                          stroke-linejoin="round" />
                      </svg>
                      <span class="add-to-cart-text">Dodaj do koszyka</span>
                    </span>
                    <span class="product-price">{{ product.price | money }}</span>
                  </button>
                </form>
              </div>
            </div>
            <!-- Insert promotion tile after 8th product on first page only for "all" collection -->
            {% if forloop.index == 8 and paginate.current_page == 1 %}
              {% if collection.handle == 'all' or collection.handle == blank %}
                <div class="promo-tile">
                  <img
                    class="promo-image"
                    src="{{ 'Promotion Tile.png' | asset_url }}"
                    alt="Promocja - Kupiłeś już dla mnie smaczki?"
                    width="286"
                    height="461"
                    loading="lazy">
                </div>
              {% endif %}
            {% endif %}
          {% endfor %}

        {% endpaginate %}
      </div>

      <!-- Pagination - outside grid but inside main -->
      {% paginate collection.products by 12 %}
        {% if paginate.pages > 1 %}
          <div class="collection-pagination">
            <div class="pagination-container">
              {% if paginate.previous %}
                <a href="{{ paginate.previous.url }}" class="page-prev">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none">
                    <path
                      d="M9.57153 2.5L3.94653 7.5625L9.57153 12.625"
                      stroke="#F9F9F4"
                      stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </a>
              {% endif %}

              {% for part in paginate.parts %}
                {% if part.is_link %}
                  <a href="{{ part.url }}" class="page-number">{{ part.title }}</a>
                {% else %}
                  {% if part.title == paginate.current_page %}
                    <div class="page-number active">{{ part.title }}</div>
                  {% elsif part.title == '&hellip;' %}
                    <div class="page-ellipsis">...</div>
                  {% else %}
                    <div class="page-number">{{ part.title }}</div>
                  {% endif %}
                {% endif %}
              {% endfor %}

              {% if paginate.next %}
                <a href="{{ paginate.next.url }}" class="page-next">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none">
                    <path
                      d="M4.42847 2.5L10.0535 7.5625L4.42847 12.625"
                      stroke="#F9F9F4"
                      stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </a>
              {% endif %}
            </div>
          </div>
        {% endif %}
      {% endpaginate %}
    </main>
  </div>
</div>

{% stylesheet %}
  .collection-ayla-delight {
    --primary-p0: #ffffff;
    --primary-p50: #f2f7f4;
    --primary-p300: #99bca9;
    --primary-p500: #4c7d67;
    --primary-p700: #2d4f40;
    --bezowe-b50: #f9f9f4;
    --bezowe-b100: #f0f0e4;
    --special-duck: #824386;

    display: flex;
    flex-direction: column;
    width: 100%;
    background: var(--primary-p0);
    font-family: 'Jost'
    , -apple-system
    , 'Roboto'
    , 'Helvetica'
    , sans-serif;
  }

  /* Collection container adjustment */
  .collection-container {
    display: flex;
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 32px 120px;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
    border-top: 1px solid var(--bezowe-b100);
  }

  /* Filters Sidebar */
  .collection-filters {
    display: flex;
    width: 276px;
    max-height: fit-content;
    flex-direction: column;
    align-items: flex-start;
    flex-shrink: 0;
    border-radius: 12px;
    border: 1px solid var(--bezowe-b100);
    background: var(--primary-p0);
    position: sticky;
    top: 24px;
    align-self: flex-start;
  }

  .filter-header {
    display: flex;
    padding: 16px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    background: var(--bezowe-b50);
  }

  .filter-title {
    flex: 1 0 0;
    color: var(--primary-p700);
    font-size: 24px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.72px;
    margin: 0;
  }

  .filter-divider {
    height: 1px;
    align-self: stretch;
    border-radius: 1px;
    background: var(--bezowe-b100);
  }

  .filter-options {
    display: flex;
    padding: 8px 12px;
    align-items: flex-start;
    align-content: flex-start;
    gap: 4px;
    align-self: stretch;
    flex-wrap: wrap;
  }

  .filter-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
  }

  .filter-accordion {
    display: flex;
    height: 48px;
    padding: 4px 16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 4px;
    cursor: pointer;
  }

  .accordion-text {
    color: var(--primary-p500);
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 1.4px;
    text-transform: uppercase;
  }

  .accordion-icon {
    width: 40px;
    height: 40px;
    transition: transform 0.3s ease;
  }

  .filter-accordion.open .accordion-icon {
    transform: rotate(0deg);
  }

  .filter-accordion:not(.open) .accordion-icon {
    transform: rotate(90deg);
  }

  .filter-accordion-content {
    display: flex;
    padding: 0 12px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .filter-accordion:not(.open) + .filter-accordion-content {
    display: none;
  }

  .filter-pill {
    display: flex;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 244px;
    border: 1px solid var(--bezowe-b100);
    background: var(--primary-p0);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .filter-pill:hover:not(.selected):not(.has-checked) {
    background: var(--bezowe-b50);
  }

  a.filter-pill {
    text-decoration: none;
    color: inherit;
  }

  a.filter-pill:hover:not(.selected) {
    background: var(--bezowe-b50);
  }

  .filter-pill.selected {
    background: var(--primary-p700);
  }

  /* Hover state for selected pills */
  .filter-pill.selected:hover {
    background: var(--primary-p500);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .filter-pill.selected .filter-pill-text {
    color: var(--primary-p0);
  }

  .filter-pill-text {
    color: var(--primary-p700);
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.28px;
  }

  .checkbox-pill {
    justify-content: flex-start;
  }

  .checkbox {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid var(--bezowe-b100);
    background: var(--primary-p0);
    position: relative;
    transition: all 0.3s ease;
  }

  /* Checked state for the entire pill */
  .checkbox-pill.has-checked {
    background: var(--primary-p700);
    border-color: var(--primary-p700);
    color: white;
  }

  /* Hover state for checked checkbox pills */
  .checkbox-pill.has-checked:hover {
    background: var(--primary-p500);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .checkbox-pill.has-checked .filter-pill-text {
    color: white;
  }

  .checkbox-pill.has-checked .category-icon path {
    fill: white;
  }

  /* Checkbox when checked */
  .checkbox.checked {
    background: white;
    border-color: white;
  }

  /* Checkmark inside checkbox */
  .checkbox.checked::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid var(--primary-p700);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  .category-icon {
    width: 20px;
    height: 20px;
  }

  /* Product Grid */
  .collection-products {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 36px;
    flex: 1;
  }

  .product-grid {
    display: flex;
    width: 892px;
    align-items: flex-start;
    align-content: flex-start;
    gap: 40px 16px;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .collection-ayla-delight .product-tile,
  .product-grid .product-tile {
    display: flex !important;
    width: 286px !important;
    min-width: 286px !important;
    max-width: 286px !important;
    height: 461px !important;
    min-height: 461px !important;
    max-height: 461px !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 16px !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }

  .product-image {
    width: 286px;
    height: 286px;
    align-self: stretch;
    border-radius: 8px;
    background: var(--bezowe-b50);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .product-image-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
  }

  .product-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }

  .product-image-placeholder {
    width: 100%;
    height: 100%;
    background: var(--bezowe-b50);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-p500);
  }

  .product-badge {
    position: absolute;
    display: inline-flex;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 24px;
    z-index: 1;
  }

  .bestseller-badge {
    background: var(--primary-p500);
    top: 8px;
    left: 8px;
  }

  .bestseller-badge span {
    color: var(--primary-p0);
    font-size: 12px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 1.2px;
    text-transform: uppercase;
  }

  .new-badge {
    background: var(--special-duck);
    top: 8px;
    left: 8px;
  }

  .new-badge span {
    color: var(--primary-p0);
    font-size: 12px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: 1.2px;
    text-transform: uppercase;
  }

  .for-whom-badge {
    position: absolute;
    display: inline-flex;
    width: auto;
    min-width: 113px;
    height: 40px;
    padding: 8px 16px;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-radius: 0 16px 0 0;
    background: var(--bezowe-b100);
    bottom: 0;
    left: 0;
    box-sizing: border-box;
  }

  .for-whom-badge span {
    color: var(--primary-p700);
    font-size: 12px;
    font-weight: 600;
    line-height: 140%;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    white-space: nowrap;
  }

  .for-whom-badge svg {
    width: 15px;
    height: 24px;
    fill: var(--primary-p700);
    flex-shrink: 0;
  }

  .product-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    flex: 1;
  }

  .product-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    flex: 1 0 0;
    align-self: stretch;
  }

  .product-name {
    align-self: stretch;
    margin: 0;
  }

  .product-name a {
    color: var(--primary-p700);
    font-size: 20px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.6px;
    text-decoration: none;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-name a:hover {
    opacity: 0.8;
  }

  .product-description {
    color: #212121;
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.28px;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-add-to-cart {
    display: flex;
    padding: 12px 20px;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    width: 286px;
    /* Set fixed width as requested */
    height: 52px;
    /* lock height to prevent visual jump */
    border-radius: 44px;
    border: 1px solid var(--bezowe-b100);
    background: var(--primary-p0);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: auto;
    appearance: none;
    -webkit-appearance: none;
  }

  .product-add-to-cart:hover {
    background: var(--bezowe-b50);
  }

  .add-to-cart-button {
    display: flex;
    align-items: center;
    gap: 4px;
    /* Reset native button styles to keep original look */
    border: none;
    background: transparent;
    padding: 0;
    margin: 0;
    appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
    color: inherit;
  }

  .add-to-cart-button svg {
    width: 28px;
    height: 28px;
  }

  .add-to-cart-text {
    color: var(--primary-p700);
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: -0.28px;
  }
  /* Success state styled per Figma */
  .product-add-to-cart.add-to-cart--success {
    background: var(--primary-p700);
    border-color: var(--primary-p700);
    justify-content: center;
    /* center content during success state */
  }
  .product-add-to-cart.add-to-cart--success .add-to-cart-text {
    color: var(--primary-p0);
  }
  .product-add-to-cart.add-to-cart--success .product-price {
    /* Completely remove price from layout flow during success state */
    display: none;
  }
  .product-add-to-cart.add-to-cart--success .add-to-cart-success-icon {
    width: 28px;
    height: 28px;
    filter: brightness(0) invert(1);
  }

  /* Disabled state mirrors original non-clickable design */
  .add-to-cart-button[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .product-price {
    color: var(--primary-p700);
    font-size: 18px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.54px;
  }

  /* Promotional Tile */
  .promo-tile {
    width: 286px !important;
    height: 461px !important;
    min-width: 286px !important;
    max-width: 286px !important;
    min-height: 461px !important;
    max-height: 461px !important;
    flex-shrink: 0;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .promo-background {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .promo-circles {
    position: absolute;
    left: -86px;
    top: 67px;
    width: 562px;
    height: 562px;
  }

  .promo-tile .promo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    display: block;
  }

  .promo-content {
    position: absolute;
    left: 24px;
    top: 24px;
    width: 232px;
    height: 139px;
    display: flex;
    flex-direction: column;
    gap: 11px;
  }

  .promo-title {
    color: var(--primary-p0);
    font-size: 24px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.72px;
    margin: 0;
  }

  .promo-subtitle {
    color: var(--primary-p50);
    font-size: 18px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.36px;
    margin: 0;
  }

  .promo-description {
    color: var(--primary-p300);
    font-size: 14px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.28px;
    margin: 0;
  }

  /* Pagination */
  .collection-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .pagination-container {
    display: flex;
    padding: 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 24px;
    border: 1px solid var(--bezowe-b100);
    background: var(--primary-p0);
    backdrop-filter: blur(2px);
  }

  .page-number {
    display: flex;
    width: 36px;
    height: 36px;
    padding: 6px;
    justify-content: center;
    align-items: center;
    gap: 6px;
    border-radius: 36px;
    background: var(--bezowe-b50);
    color: var(--primary-p700);
    font-size: 18px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.54px;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    font-family: inherit;
  }

  button.page-number {
    border: none;
    font-family: inherit;
  }

  a.page-number:hover,
  button.page-number:hover {
    background: var(--primary-p500);
    color: var(--primary-p0);
  }

  .page-number.active {
    background: var(--primary-p700);
    color: var(--primary-p0);
  }

  .page-ellipsis {
    display: flex;
    width: 36px;
    height: 36px;
    justify-content: center;
    align-items: center;
    color: var(--primary-p700);
    font-size: 18px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.54px;
    cursor: default;
  }

  .page-prev,
  .page-next {
    display: flex;
    width: 36px;
    height: 36px;
    padding: 6px;
    justify-content: center;
    align-items: center;
    gap: 6px;
    border-radius: 36px;
    background: var(--primary-p700);
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    font-family: inherit;
  }

  button.page-prev,
  button.page-next {
    border: none;
    font-family: inherit;
  }

  a.page-prev:hover,
  a.page-next:hover,
  button.page-prev:hover,
  button.page-next:hover {
    background: var(--primary-p500);
  }

  .page-prev svg,
  .page-next svg {
    width: 13.5px;
    height: 13.5px;
  }

  /* Responsive Design */
  @media (max-width: 1440px) {
    .collection-container {
      padding: 24px 40px;
      gap: 12px;
    }

    .collection-filters {
      width: 240px;
      flex-shrink: 0;
    }

    .product-grid {
      width: 100%;
      gap: 20px 12px;
      justify-content: flex-start;
    }

    .collection-ayla-delight .product-tile,
    .product-grid .product-tile {
      width: calc((100% - 36px) / 3) !important;
      min-width: 240px !important;
      max-width: 280px !important;
      height: 461px !important;
    }
  }

  @media (max-width: 1200px) {
    .collection-container {
      padding: 24px 20px;
      gap: 10px;
    }

    .collection-filters {
      width: 220px;
    }

    .collection-ayla-delight .product-tile,
    .product-grid .product-tile {
      width: calc((100% - 24px) / 2) !important;
      min-width: 220px !important;
      max-width: 300px !important;
    }
  }

  @media (max-width: 1024px) {
    .collection-container {
      flex-direction: column;
      padding: 20px;
      gap: 24px;
    }

    .collection-filters {
      width: 100%;
      max-width: none;
      position: static;
    }

    .product-grid {
      width: 100%;
      gap: 20px 10px;
    }

    .collection-ayla-delight .product-tile,
    .product-grid .product-tile {
      width: calc(50% - 5px) !important;
      height: auto !important;
      min-width: 200px !important;
      min-height: auto !important;
      max-height: none !important;
    }

    .product-image {
      height: 200px;
    }

    .promo-tile {
      width: calc(50% - 5px) !important;
      min-width: 200px !important;
      height: 320px !important;
      max-width: none !important;
      min-height: 320px !important;
      max-height: 320px !important;
    }

    .promo-tile .promo-image {
      width: 100%;
      height: 100%;
    }

    .promo-content {
      left: 16px;
      top: 16px;
      width: calc(100% - 32px);
    }

    .promo-title {
      font-size: 20px;
    }

    .promo-subtitle {
      font-size: 16px;
    }

    .promo-description {
      font-size: 12px;
    }
  }

  @media (max-width: 768px) {
    .collection-ayla-delight {
      padding: 0;
    }

    .collection-container {
      padding: 32px 16px 16px;
    }

    .product-grid {
      gap: 16px 8px;
    }

    .collection-ayla-delight .product-tile,
    .product-grid .product-tile {
      width: 100% !important;
      height: auto !important;
      min-height: auto !important;
      max-height: none !important;
    }

    .product-image {
      height: 250px;
    }

    .promo-tile {
      width: 100% !important;
      height: 280px !important;
      min-width: 100% !important;
      max-width: 100% !important;
      min-height: 280px !important;
      max-height: 280px !important;
    }

    .promo-tile .promo-image {
      width: 100%;
      height: 100%;
    }

    .promo-content {
      left: 12px;
      top: 12px;
      width: calc(100% - 24px);
    }

    .promo-title {
      font-size: 18px;
    }

    .promo-subtitle {
      font-size: 14px;
    }

    .promo-description {
      font-size: 11px;
    }
  }

  /* Hidden state for filtering */
  .collection-ayla-delight .product-tile.hidden,
  .product-grid .product-tile.hidden {
    display: none !important;
  }
{% endstylesheet %}

{% javascript %}
  class CollectionManager {
      constructor(element) {
        this.element = element;
        this.filters = {
          category: 'all',
          forWhom: [],
          productLine: [],
          meatType: []
        };
        this.init();
      }

      init() {
        this.setupFilterListeners();
        this.setupAccordions();
        this.setupBannerListener();
      }

      setupBannerListener() {

  // Listen for filter changes from the banner
        document.addEventListener('banner-filter-change', (e) => {
          const filter = e.detail.filter;

  // Find and click the corresponding filter pill
          const pill = this.element.querySelector(`.product-line-filter[data-filter="${filter}"]`);
          if (pill) {
            this.handleProductLineFilter(pill, filter);
          }
        });
      }

      setupFilterListeners() {

  // Category pills (excluding links)
        const filterPills = this.element.querySelectorAll('.filter-pill:not(a)');

        filterPills.forEach(pill => {
          pill.addEventListener('click', (e) => {
            e.stopPropagation();
            const filter = e.currentTarget.dataset.filter;

  // Check if this is a product line filter
            if (pill.classList.contains('product-line-filter')) {
              this.handleProductLineFilter(e.currentTarget, filter);
            } else if (filter) {
              this.handleFilterClick(e.currentTarget, filter);
            }
          });
        });

  // Checkbox pills
        this.element.querySelectorAll('.checkbox-pill').forEach(pill => {
          pill.addEventListener('click', (e) => {
            e.stopPropagation();
            const checkbox = e.currentTarget.querySelector('.checkbox');
            checkbox.classList.toggle('checked');

  // Toggle the has-checked class on the pill
            if (checkbox.classList.contains('checked')) {
              e.currentTarget.classList.add('has-checked');
            } else {
              e.currentTarget.classList.remove('has-checked');
            }

            const filter = e.currentTarget.dataset.filter;
            this.handleCheckboxFilter(filter, checkbox.classList.contains('checked'));
          });
        });
      }

      setupAccordions() {
        this.element.querySelectorAll('.filter-accordion').forEach(accordion => {
          accordion.addEventListener('click', () => {
            accordion.classList.toggle('open');
          });
        });
      }

      handleProductLineFilter(pill, filter) {

  // Product line filters are now handled via direct links to collections
  // This function is no longer needed for product line filters
        console.log('Product line filter clicked:', filter);
      }

      handleFilterClick(pill, filter) {

  // Handle category filters
        if (['all', 'bestsellers', 'new', 'promotions'].includes(filter)) { // Remove selected from all category filter pills (including the "all" link)
          this.element.querySelectorAll('.filter-options .filter-pill').forEach(p => {
            p.classList.remove('selected');
          });

  // Add selected to the clicked pill
          pill.classList.add('selected');
          this.filters.category = filter;
        }

  // Handle product line filters else if (['ayla-delight', 'ayla-help', 'ayla-rescue'].includes(filter)) {
        this.toggleArrayFilter('productLine', filter);
        pill.classList.toggle('selected');
      }

  // Handle meat type filters else if ([
      'duck',
      'chicken',
      'turkey',
      'offal',
      'blend'
    ].includes(filter)
  ) {this.toggleArrayFilter('meatType', filter);
    pill.classList.toggle('selected');
  }

  this.applyFilters();
}

handleCheckboxFilter(filter, isChecked) {
  if (['for-dog', 'for-cat'].includes(filter)) {
    if (isChecked) {
      if (!this.filters.forWhom.includes(filter)) {
        this.filters.forWhom.push(filter);
      }
    } else {
      this.filters.forWhom = this.filters.forWhom.filter(f => f !== filter);
    }
  }

  this.applyFilters();
}

toggleArrayFilter(filterType, value) {
  if (this.filters[filterType].includes(value)) {
    this.filters[filterType] = this.filters[filterType].filter(v => v !== value);
  } else {
    this.filters[filterType].push(value);
  }
}

applyFilters() {
  const productTiles = this.element.querySelectorAll('.product-tile');
  let visibleCount = 0;

// Reset to page 1 when filters change
  this.currentPage = 1;

  productTiles.forEach(tile => {
    let shouldShow = true;

// Category filter
    if (this.filters.category !== 'all') {
      const hasCategory = tile.dataset[this.filters.category] === 'true';
      if (! hasCategory) 
        shouldShow = false;
      


    }

// For whom filter
    if (this.filters.forWhom.length > 0) {
      const hasMatch = this.filters.forWhom.some(filter => tile.dataset[filter.replace('-', '')] === 'true');
      if (! hasMatch) 
        shouldShow = false;
      


    }

// Product line filter
    if (this.filters.productLine.length > 0) {
      const hasMatch = this.filters.productLine.includes(tile.dataset.line);
      if (! hasMatch) 
        shouldShow = false;
      


    }

// Meat type filter
    if (this.filters.meatType.length > 0) {
      const hasMatch = this.filters.meatType.includes(tile.dataset.meat);
      if (! hasMatch) 
        shouldShow = false;
      


    }

// Hide/show tile - don't set display here, let pagination handle it
    tile.classList.toggle('hidden', ! shouldShow);
    if (shouldShow) {
      visibleCount++;
    }
  });

// Handle promo tile visibility
  const promoTile = this.element.querySelector('.promo-tile');
  if (promoTile) { // Only show promo tile if we have at least 8 visible products and we're on page 1
    if (visibleCount >= 8 && (this.currentPage === 1 || !this.currentPage)) {
      promoTile.style.display = '';
    } else {
      promoTile.style.display = 'none';
    }
  }

// Update pagination based on visible products
  this.updatePagination(visibleCount);
}

updatePagination(visibleCount) {
  const itemsPerPage = 12; // Show 12 products per page
  const totalPages = Math.ceil(visibleCount / itemsPerPage);
  const paginationContainer = document.querySelector('.pagination-container');
  const collectionPagination = document.querySelector('.collection-pagination');

  console.log('updatePagination called:', {visibleCount, totalPages, itemsPerPage});

  if (! paginationContainer || ! collectionPagination) {
    console.error('Pagination elements not found');
    return;
  }

// Store current page (default to 1 for client-side filtering)
  this.currentPage = this.currentPage || 1;

// Always paginate products, even if only 1 page
  if (totalPages <= 1) {
    collectionPagination.style.display = 'none';

// Still paginate to show only 12 products even if there's only 1 page
    this.paginateProducts();
    return;
  }

  collectionPagination.style.display = 'flex';

// Rebuild pagination for filtered results
  let paginationHTML = '';

// Previous button (disabled on page 1)
  if (this.currentPage > 1) {
    paginationHTML += `
          <button class="page-prev" data-page="${
      this.currentPage - 1
    }">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
              <path d="M9.57153 2.5L3.94653 7.5625L9.57153 12.625" stroke="#F9F9F4" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>`;
  }

// Page numbers
  for (let i = 1; i <= totalPages; i++) {
    if (i === this.currentPage) {
      paginationHTML += `<div class="page-number active">${i}</div>`;
    } else { // Show page if it's close to current page or at the boundaries
      if (i === 1 || i === totalPages || Math.abs(i - this.currentPage) <= 2) {
        paginationHTML += `<button class="page-number" data-page="${i}">${i}</button>`;
      } else if (i === this.currentPage - 3 || i === this.currentPage + 3) { // Add ellipsis
        if (! paginationHTML.includes('page-ellipsis')) {
          paginationHTML += `<div class="page-ellipsis">...</div>`;
        }
      }
    }
  }

// Next button (disabled on last page)
  if (this.currentPage<totalPages) {
        paginationHTML += `
          <button class="page-next" data-page="${this.currentPage + 1}">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
              <path d="M4.42847 2.5L10.0535 7.5625L4.42847 12.625" stroke="#F9F9F4" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>`;
      }
      
      // Update pagination container
      paginationContainer.innerHTML = paginationHTML;
      
      // Add click handlers for pagination
      paginationContainer.querySelectorAll('[data-page]').forEach(button => {
    button.addEventListener('click', (e) => {
      this.currentPage = parseInt(e.currentTarget.dataset.page);
      this.paginateProducts();
      this.updatePagination(visibleCount);
    });
  }) 


// Paginate the products
    this.paginateProducts();
  


}

paginateProducts() {
  const itemsPerPage = 12; // Show 12 products per page
  const productTiles = this.element.querySelectorAll('.product-tile:not(.hidden)');
  const promoTile = this.element.querySelector('.promo-tile');
  const startIndex = (this.currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  console.log('paginateProducts called:', {
    currentPage: this.currentPage,
    totalVisible: productTiles.length,
    startIndex,
    endIndex
  });

  let visibleOnCurrentPage = 0;

// First hide ALL products (including hidden ones to reset display)
  this.element.querySelectorAll('.product-tile').forEach(tile => {
    tile.style.display = 'none';
  });

// Then show only the visible ones for current page
  productTiles.forEach((tile, index) => {
    if (index >= startIndex && index < endIndex) {
      tile.style.display = '';
      visibleOnCurrentPage++;
    }
  });

// Handle promo tile visibility for paginated results
  if (promoTile) { // Show promo tile only on page 1 when there are at least 8 products visible on that page
    if (this.currentPage === 1 && visibleOnCurrentPage >= 8) {
      promoTile.style.display = '';
    } else {
      promoTile.style.display = 'none';
    }
  }

// Scroll to top of collection
  const collectionContainer = document.querySelector('.collection-container');
  if (collectionContainer) {
    collectionContainer.scrollIntoView({behavior: 'smooth', block: 'start'});
  }
}}

// Initialize collection filtering when DOM is readyfunction initializeCollection () {
const collectionElement = document.querySelector('.main-collection');
if (collectionElement) {
  const manager = new CollectionManager(collectionElement);

// Collection is now handled by Shopify's built-in system
  console.log('Collection initialized with Shopify pagination');

// Handle navbar filter links - they should now be direct links to collections
  document.querySelectorAll('.navbar-filter-link').forEach(link => {
    link.addEventListener('click', (e) => {

// Let the default link behavior handle navigation
// Just ensure smooth scrolling if needed
      const collectionSection = document.querySelector('.main-collection');
      if (collectionSection) {
        setTimeout(() => {
          collectionSection.scrollIntoView({behavior: 'smooth', block: 'start'});
        }, 100);
      }
    });
  });

// Check if there's a hash in the URL and activate the corresponding filter
  if (window.location.hash) {
    const filter = window.location.hash.replace('#', '');
    const filterPill = document.querySelector(`.product-line-filter[data-filter="${filter}"]`);
    if (filterPill) {
      setTimeout(() => filterPill.click(), 100);
    }
  }
}}if (document.readyState === 'loading') {
document.addEventListener('DOMContentLoaded', initializeCollection);} else {
initializeCollection();}
{% endjavascript %}

{% schema %}
  {
    "name": "Collection Ayla Delight",
    "tag": "section",
    "class": "shopify-section-collection-ayla-delight",
    "settings": [
      {
        "type": "header",
        "content": "Collection Settings"
      }, {
        "type": "collection",
        "id": "collection",
        "label": "Collection"
      }
    ]
  }
{% endschema %}