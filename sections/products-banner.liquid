{% comment %}
  Products banner section with three product tiles
{% endcomment %}

{% comment %} Determine which banner to show based on collection {% endcomment %}
{% assign initial_banner = 'all' %}
{% if collection %}
  {% if collection.handle == 'ayla-delight' %}
    {% assign initial_banner = 'ayla-delight' %}
  {% elsif collection.handle == 'ayla-help' %}
    {% assign initial_banner = 'ayla-help' %}
  {% elsif collection.handle == 'ayla-rescue' %}
    {% assign initial_banner = 'ayla-rescue' %}
  {% elsif collection.handle == 'all' or collection.handle == 'wszystkie' %}
    {% assign initial_banner = 'all' %}
  {% endif %}
{% endif %}

<link rel="preconnect" href="https://fonts.googleapis.com">
<link
  rel="preconnect"
  href="https://fonts.gstatic.com"
  crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Jost:wght@400;500&display=swap" rel="stylesheet">

<!-- Debug: collection.handle = {{ collection.handle }}, initial_banner = {{ initial_banner }} -->
<div
  class="products-banner"
  data-dynamic-banner
  data-initial-banner="{{ initial_banner }}">
  <!-- Default All Products Banner -->
  <div
    class="banner-container default-banner"
    data-banner-type="all"
    style="display: {% if initial_banner == 'all' or initial_banner == blank %}flex{% else %}none{% endif %};">
    <div class="banner-title">
      {{ section.settings.banner_title | default: 'Wszystkie produkty' }}
    </div>
    <div class="banner-content">
      <div class="product-tile tile-delight" data-filter-trigger="ayla-delight">
        <a href="#" class="tile-link">
          <img
            class="tile-background-image"
            src="{{ section.settings.delight_tile_image | default: 'Tiles-Top-left.png' | asset_url }}"
            alt="AYLA Delight" />
          <div class="tile-hover-button">
            <span>{{ section.settings.delight_button_text | default: 'PRODUKTY AYLA DELIGHT' }}</span>
            <div class="button-arrow">
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M4.5 1.5L8.5 6L4.5 10.5"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </div>
          </div>
        </a>
      </div>

      <div class="product-tile tile-help" data-filter-trigger="ayla-help">
        <a href="#" class="tile-link">
          <img
            class="tile-background-image"
            src="{{ section.settings.help_tile_image | default: 'Tiles-Top-middle.png' | asset_url }}"
            alt="AYLA Help" />
          <div class="tile-hover-button">
            <span>{{ section.settings.help_button_text | default: 'PRODUKTY AYLA HELP' }}</span>
            <div class="button-arrow">
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M4.5 1.5L8.5 6L4.5 10.5"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </div>
          </div>
        </a>
      </div>

      <div class="product-tile tile-rescue" data-filter-trigger="ayla-rescue">
        <a href="#" class="tile-link">
          <img
            class="tile-background-image"
            src="{{ section.settings.rescue_tile_image | default: 'Tiles-Top-right.png' | asset_url }}"
            alt="AYLA Rescue" />
          <div class="tile-hover-button">
            <span>{{ section.settings.rescue_button_text | default: 'PRODUKTY AYLA RESCUE' }}</span>
            <div class="button-arrow">
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M4.5 1.5L8.5 6L4.5 10.5"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>

  <!-- AYLA Delight Banner -->
  <div
    class="dynamic-banner ayla-delight-banner"
    data-banner-type="ayla-delight"
    style="display: {% if initial_banner == 'ayla-delight' %}flex{% else %}none{% endif %};">
    <div class="hero-banner" style="background-image: url('{{ 'ayla-delight-top-banner.png' | asset_url }}');">
      <div class="hero-text-content">
        <div class="hero-label">AYLA DELIGHT • NATURALLY HEALTHY TREATS</div>
        <h1 class="hero-title">Naturalne, smaczne zdrowe i smakowite przysmaki dla psów i kotów</h1>
        <p class="hero-description">W 100% z mięsa najwyższej klasy, jakości human grade, bez żadnych dodatków i konserwantów. Dostępne w trzech wariantach mięsnych: Kurczak, Kaczka i Indyk.</p>
      </div>
    </div>
  </div>

  <!-- AYLA Help Banner -->
  <div
    class="dynamic-banner ayla-help-banner"
    data-banner-type="ayla-help"
    style="display: {% if initial_banner == 'ayla-help' %}flex{% else %}none{% endif %};">
    <div class="hero-banner" style="background-image: url('{{ 'ayla-help-top-banner.png' | asset_url }}');">
      <div class="hero-text-content">
        <div class="hero-label">AYLA HELP • RECOVERY FOOD</div>
        <h1 class="hero-title">Dla psów i kotów wspierające rekonwalescencję i dobrostań</h1>
        <p class="hero-description">Innowacyjny produkt wspierający żywienie w okresie rekonwalescencji, zaburzeń odżywiania dla psów i kotów, poprawiający ich ogólny dobrostań.</p>
      </div>
    </div>
  </div>

  <!-- AYLA Rescue Banner -->
  <div
    class="dynamic-banner ayla-rescue-banner"
    data-banner-type="ayla-rescue"
    style="display: {% if initial_banner == 'ayla-rescue' %}flex{% else %}none{% endif %};">
    <div class="hero-banner" style="background-image: url('{{ 'Ayla-rescue-top-banner.png' | asset_url }}');">
      <div class="hero-text-content">
        <div class="hero-label">AYLA RESCUE • ESSENTIAL CARE FOOD</div>
        <h1 class="hero-title">Idealne wsparcie w terapii żywieniowej i rekonwalescencji</h1>
        <p class="hero-description">Innowacyjny produkt wspierający terapię żywieniową dla pacjentów weterynaryjnych w stanach pooperacyjnych, realimentacji i rekonwalescencji.</p>
      </div>
    </div>
  </div>
</div>

{% stylesheet %}
  :root  {
    --Primary-Color-P700---Main: #2D4F40;
    --Primary-Color-P0: #FFF;
    --Bezowe-B50: #F9F9F4;
    --Specjalne-Specjalny---Indyk: #5F7FBC;
    --Bezowe-B100: #F0F0E4;
    --Primary-Color-P400: #6A9981;
  }

  .products-banner {
    display: flex;
    width: 100%;
    max-width: 1216px;
    /* 1200px + 16px for padding */
    height: auto;
    min-height: 296px;
    /* 280px + 16px for padding */
    justify-content: center;
    align-items: center;
    position: relative;
    margin: 0 auto;
    padding: 8px;
    box-sizing: border-box;
  }

  .banner-container {
    display: flex;
    width: 100%;
    height: 280px;
    padding: 32px 0;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
    gap: 24px;
    flex-shrink: 0;
    box-sizing: border-box;
  }

  .banner-title {
    align-self: stretch;
    color: var(--Primary-Color-P700---Main);
    font-family: 'Jost'
    , -apple-system
    , Roboto
    , Helvetica
    , sans-serif;
    font-size: 40px;
    font-style: normal;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -1.2px;
    position: relative;
  }

  .banner-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    position: relative;
  }

  .product-tile {
    width: 392px;
    height: 167px;
    border-radius: 12px;
    position: relative;
    flex: 1;
    overflow: hidden;
    cursor: pointer !important;
  }

  .product-tile:hover {
    cursor: pointer !important;
  }

  .product-tile * {
    cursor: pointer !important;
  }

  .tile-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    pointer-events: none;
  }

  .tile-delight {
  ;
  /* Remove solid background as we're using image */
}

.tile-help {
;
/* Remove solid background as we're using image */
}

.tile-rescue {
;
/* Remove solid background as we're using image */
}

.tile-link {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  text-decoration: none;
  color: inherit;
  cursor: pointer !important;
}

.tile-link:hover {
  cursor: pointer !important;
}

a.tile-link {
  cursor: pointer !important;
}

.tile-hover-button {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 214px !important;
  height: 40px !important;
  min-width: 214px;
  min-height: 40px;
  max-width: 214px;
  max-height: 40px;
  background: white;
  color: #2D4F40;
  padding: 0 0 0 12px;
  border-radius: 12px 0 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease
  , transform 0.3s ease;
  z-index: 2;
  box-shadow: -2px -2px 8px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  overflow: hidden;
}

.tile-hover-button span {
  font-family: 'Jost'
  , -apple-system
  , Roboto
  , Helvetica
  , sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 15px;
  letter-spacing: 0.48px;
  /* 4% of 12px */
  text-transform: uppercase;
  width: 157px;
  height: 15px;
  display: flex;
  align-items: center;
  margin-right: 9px;
}

.button-arrow {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2D4F40;
  border-radius: 50%;
  flex-shrink: 0;
  margin-right: 4px;
  transition: transform 0.3s ease;
}

.button-arrow svg {
  width: 12px;
  height: 12px;
  color: white;
}

.product-tile:hover .tile-background-image {
  opacity: 0.85;
  transition: opacity 0.3s ease;
}

.product-tile:hover .tile-hover-button {
  opacity: 1;
  transform: translateY(0);
}

.product-tile:hover .button-arrow {
  transform: translateX(3px);
}

.tile-background-image {
  transition: opacity 0.3s ease;
}

/* Dynamic Banner Styles */
.dynamic-banner {
  width: 100%;
  display: flex;
  justify-content: center;
}

.hero-banner {
  width: 100%;
  height: 280px;
  border-radius: 16px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  padding: 0 60px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.hero-text-content {
  max-width: 556px;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.hero-label {
  color: hsla(296, 33%, 39%, 1);
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.12em;
  text-transform: uppercase;
  margin: 0 0 16px;
  font-family: 'Poppins'
  , sans-serif;
}

.ayla-help-banner .hero-label {
  color: hsla(219, 41%, 55%, 1);
}

.ayla-rescue-banner .hero-label {
  color: hsla(9, 60%, 58%, 1);
}

.hero-title {
  color: #2D4F40;
  font-size: 32px;
  font-weight: 500;
  line-height: 35px;
  margin: 0 0 16px;
  font-family: 'Poppins'
  , sans-serif;
  width: 600px;
  min-height: 70px;
  display: block;
}

.hero-description {
  color: #2D4F40;
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
  margin: 0;
  opacity: 0.9;
  width: 600px;
  min-height: 50px;
  display: block;
}

/* Mobile responsiveness */
@media (max-width: 1200px) {
  .products-banner {
    padding: 0 20px;
  }

  .banner-content {
    flex-direction: column;
    gap: 16px;
  }

  .product-tile {
    width: 100%;
    height: auto;
    min-height: 167px;
  }
}

@media (max-width: 768px) {
  .banner-title {
    font-size: 32px;
  }

  .products-banner {
    height: auto;
    padding: 0 16px;
  }

  .banner-container {
    height: auto;
    padding: 24px 0;
  }
}
{% endstylesheet %}

{% javascript %}
  document.addEventListener('DOMContentLoaded', function() {
    const banner = document.querySelector('.products-banner');
    if (! banner) return;
    

// Function to switch banners
    function switchBanner(type) {

// Hide all banners
      const allBanners = banner.querySelectorAll('.default-banner, .dynamic-banner');
      allBanners.forEach(b => b.style.display = 'none');

// Show the appropriate banner
      if (type === 'all' || ! type) {
        const defaultBanner = banner.querySelector('.default-banner');
        if (defaultBanner) 
          defaultBanner.style.display = 'flex';
        
      } else {
        const dynamicBanner = banner.querySelector(`[data-banner-type="${type}"]`);
        if (dynamicBanner) 
          dynamicBanner.style.display = 'flex';
        
      }
    }

// Handle clicks on tiles in the default banner - navigate to collections
    const tiles = banner.querySelectorAll('.product-tile[data-filter-trigger]');
    tiles.forEach(tile => {
      tile.addEventListener('click', function(e) {
        e.preventDefault();
        const filterType = this.dataset.filterTrigger;

// Navigate to the appropriate collection
        if (filterType === 'ayla-delight') {
          window.location.href = '/collections/ayla-delight';
        } else if (filterType === 'ayla-help') {
          window.location.href = '/collections/ayla-help';
        } else if (filterType === 'ayla-rescue') {
          window.location.href = '/collections/ayla-rescue';
        }
      });
    });

// Listen for filter changes from the collection
    document.addEventListener('collection-filter-change', function(e) {
      switchBanner(e.detail.filter);
    });

// Set initial banner based on data attribute
    const initialBanner = banner.dataset.initialBanner || 'all';

// Only switch if not already set by Liquid
    if (initialBanner && initialBanner !== 'all') {
      switchBanner(initialBanner);
    }
  });
{% endjavascript %}

{% schema %}
  {
    "name": "Products Banner",
    "settings": [
      {
        "type": "text",
        "id": "banner_title",
        "label": "Banner Title",
        "default": "Wszystkie produkty"
      },
      {
        "type": "image_picker",
        "id": "delight_tile_image",
        "label": "Delight Tile Image"
      },
      {
        "type": "url",
        "id": "delight_link",
        "label": "Delight Link"
      },
      {
        "type": "text",
        "id": "delight_button_text",
        "label": "Delight Button Text",
        "default": "PRODUKTY AYLA DELIGHT"
      }, {
        "type": "image_picker",
        "id": "help_tile_image",
        "label": "Help Tile Image"
      }, {
        "type": "url",
        "id": "help_link",
        "label": "Help Link"
      }, {
        "type": "text",
        "id": "help_button_text",
        "label": "Help Button Text",
        "default": "PRODUKTY AYLA HELP"
      }, {
        "type": "image_picker",
        "id": "rescue_tile_image",
        "label": "Rescue Tile Image"
      }, {
        "type": "url",
        "id": "rescue_link",
        "label": "Rescue Link"
      }, {
        "type": "text",
        "id": "rescue_button_text",
        "label": "Rescue Button Text",
        "default": "PRODUKTY AYLA RESCUE"
      }
    ],
    "presets": [
      {
        "name": "Products Banner"
      }
    ]
  }
{% endschema %}