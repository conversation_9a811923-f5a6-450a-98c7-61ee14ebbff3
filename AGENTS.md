# Repository Guidelines

## Project Structure & Module Organization
- `assets/`: Theme CSS, JS, and media (e.g., `enhanced-cart.js`, `critical.css`).
- `sections/`: Page-level Liquid sections (kebab-case, one concern per file).
- `snippets/`: Reusable Liquid fragments included by sections/templates.
- `templates/`: Page templates and JSON templates.
- `layout/`: Global wrappers (e.g., `theme.liquid`).
- `blocks/`, `config/`, `locales/`: Theme schema, settings, and translations.
- Tooling: `.theme-check.yml` (lint), `shopify.theme.toml` (CLI env), `deploy.sh` (push theme).

## Build, Test, and Development Commands
- `shopify theme serve`: Run local dev server with live reload.
- `shopify theme check`: Static analysis for Liquid/JSON schema issues.
- `./deploy.sh`: Upload theme to the configured store as an unpublished theme. Review and publish in Admin.
- `shopify theme push --live`: Push and publish directly (use with caution).

## Coding Style & Naming Conventions
- Indentation: 2 spaces for Liquid, JSON, JS, and CSS.
- Naming: Use kebab-case for files (e.g., `enhanced-cart.js`, `product-grid.liquid`).
- Liquid: Keep logic minimal; prefer snippets for reuse and readability.
- JS: Place scripts in `assets/`; scope via data attributes (e.g., `data-cart-count`), avoid globals.
- CSS: Prefer component/section-scoped styles; avoid leaking to Admin/editor UI.

## Testing Guidelines
- Lint: Run `shopify theme check` before committing. Address all errors; warnings should be intentional.
- Manual QA: Use `shopify theme serve` or a preview link to verify UX, translations, and dynamic states (e.g., cart drawer, accordions).
- No unit test framework is configured; visual and functional checks are expected.

## Commit & Pull Request Guidelines
- Commits: Imperative mood and focused scope (e.g., "Improve cart drawer animation").
- Reference area: Prefix if helpful (e.g., `sections:`, `snippets:`, `assets:`).
- PRs: Include a clear summary, screenshots/GIFs, affected templates/sections, and a preview URL. Link issues when applicable.
- CI/Lint: Confirm `shopify theme check` passes and that `deploy.sh` can push without errors.

## Security & Configuration Tips
- Do not commit store credentials or tokens. Use Shopify CLI auth locally.
- Keep store-specific values out of Liquid where possible; use settings/schema in `config/`.
- Respect `.shopifyignore` to avoid uploading local-only files.

