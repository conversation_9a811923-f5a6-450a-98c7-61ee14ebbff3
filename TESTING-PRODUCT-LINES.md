# Testing Product Lines Background Switching

## Fixed Issues
- Removed inline styles that were overriding JavaScript updates
- Added Web Component implementation for reliable background switching
- Added immediate initialization script for correct initial backgrounds
- Enhanced JavaScript with force override using !important flags
- Breakpoint aligned with hero section at 800px

## Testing Instructions

### 1. Manual Browser Testing
1. Open your Shopify store preview
2. Navigate to the product lines section
3. Resize browser window to test breakpoints:
   - **Desktop**: >800px - Should show desktop background images
   - **Tablet**: 481-800px - Should show tablet background images (if set)
   - **Mobile**: ≤480px - Should show mobile background images (if set)

### 2. Debug Console Testing
Add this to browser console to load debug panel:
```javascript
const script = document.createElement('script');
script.src = '/assets/product-lines-debug.js';
document.head.appendChild(script);
```

The debug panel will show:
- Current viewport width
- Expected mode (desktop/tablet/mobile)
- Each card's current background status
- Test buttons for specific breakpoints

### 3. Force Update Testing
In browser console, run:
```javascript
// Force update backgrounds
document.querySelector('ayla-product-lines').updateBackgrounds();
```

### 4. Verify Data Attributes
Check that cards have correct data attributes:
```javascript
document.querySelectorAll('.product-card').forEach(card => {
  console.log({
    desktop: card.getAttribute('data-desktop-bg'),
    tablet: card.getAttribute('data-tablet-bg'),
    mobile: card.getAttribute('data-mobile-bg'),
    currentMode: card.getAttribute('data-active-mode')
  });
});
```

## Breakpoints
- **Mobile**: ≤480px
- **Tablet**: 481-800px (matches hero section)
- **Desktop**: >800px

## Expected Behavior
1. On page load, correct background should be set immediately
2. On resize, backgrounds should switch at the exact breakpoints
3. Tablet images should appear at 800px and below
4. If tablet/mobile images aren't set, desktop image is used as fallback

## Shopify Admin Setup
1. Go to Theme Customizer
2. Find "Ayla Product Lines" section
3. For each card (DELIGHT, HELP, RESCUE):
   - Upload Desktop Background Image (required)
   - Upload Tablet Background Image (optional)
   - Upload Mobile Background Image (optional)

## Troubleshooting
If backgrounds aren't switching:
1. Check browser console for errors
2. Verify images are uploaded in Shopify admin
3. Clear browser cache and reload
4. Use debug panel to see actual vs expected states
5. Check that Web Component is initialized:
   ```javascript
   console.log(customElements.get('ayla-product-lines'));
   ```