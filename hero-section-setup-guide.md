# Hero Section Setup Guide - Ayla Theme

## Overview
The hero section has been completely redesigned to match the Figma design specifications. It now features a complex layout with text overlay, product images, and decorative elements, providing a professional and engaging hero experience.

## Key Changes Made

### 1. **Complete Figma Design Implementation**
- Exact recreation of the Figma design with proper positioning
- Complex layout with text overlay on the left side
- Multiple product images positioned at specific angles
- Decorative kibble background elements

### 2. **Enhanced Features**
- **Text Content**: Badge, heading, description, and CTA button
- **Product Showcase**: Up to 3 product images per slide at different angles
- **Visual Effects**: Gradient overlay and decorative elements
- **Multiple Slides**: Support for up to 4 hero slides
- **Autoplay**: Configurable auto-rotation with customizable delay
- **Keyboard Navigation**: Arrow keys for slide navigation
- **Touch Support**: Mobile-friendly slide navigation
- **Accessibility**: Proper alt text support and ARIA labels
- **SEO Optimization**: Semantic HTML and proper image attributes

## How to Configure in Theme Customizer

### Step 1: Access the Hero Section
1. Go to your Shopify Admin
2. Navigate to **Online Store > Themes**
3. Click **Customize** on your active theme
4. Find the **Hero Ayla Delight** section on your homepage

### Step 2: Add Hero Slides
1. Click **Add block** and select **Hero Slide**
2. For each slide, configure:

   **Text Content:**
   - **Badge Text**: Small badge above heading (e.g., "BESTSELLER")
   - **Heading**: Main hero heading text
   - **Description**: Supporting description text
   - **Button Text**: CTA button text
   - **Button Link**: URL for the CTA button
   
   **Product Images:**
   - **Center Product Image**: Main product package (300x300px PNG with transparency)
   - **Left Product Image**: Secondary product package (300x300px PNG with transparency)
   - **Right Product Image**: Third product package (300x300px PNG with transparency)

### Step 3: Configure Slideshow Settings
- **Auto-rotate slides**: Enable/disable automatic slide rotation
- **Auto-rotate delay**: Set delay between slides (3-10 seconds)

### Step 4: Save Your Changes
Click **Save** to apply your configuration

## Image Requirements

### Product Images (Required)
- **Dimensions**: 300x300 pixels
- **Format**: PNG with transparent background
- **File Size**: Under 200KB per image
- **Quality**: High-quality product shots
- **Note**: Images will be automatically rotated and positioned

### Decorative Kibble Pattern (Optional)
- **File Name**: hero-kibble-pattern.png
- **Location**: Upload to theme assets
- **Format**: PNG with transparency
- **Purpose**: Background decorative element

### Design Specifications
- Background color: #f2f7f4 (light green)
- Text color: #2d4f40 (dark green)
- Badge color: #4c7d67 (medium green)
- Button color: #2d4f40 (dark green)

## Fallback Behavior

If no blocks are configured, the section displays:
1. Default text content with Polish pet treat messaging
2. A message prompting configuration in theme customizer
3. The standard navigation controls (if multiple slides)

The section maintains its professional appearance even without product images.

## Technical Details

### Files Modified
- `/sections/hero-ayla-delight.liquid` - Main section file with all updates

### Key Technical Improvements
1. **Figma-accurate design**: Pixel-perfect implementation of the design
2. **Complex positioning**: Product images with rotation transforms
3. **Layered layout**: Text overlay, products, and decorative elements
4. **Custom element**: JavaScript functionality using Web Components
5. **Responsive design**: Graceful degradation on mobile devices
6. **Performance optimized**: Lazy loading for non-visible slides

## Troubleshooting

### Images Not Showing
1. Ensure images are uploaded in the theme customizer
2. Check that image files are not corrupted
3. Verify image URLs are accessible

### Slideshow Not Working
1. Ensure you have more than one slide added
2. Check that autoplay is enabled in settings
3. Verify JavaScript is not blocked in browser

### Performance Issues
1. Optimize image file sizes (use image compression)
2. Ensure images are the recommended dimensions
3. Consider reducing the number of slides

## Browser Support
- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support
- Mobile browsers: Full support with touch navigation

## Need Help?
If you encounter any issues with the hero section, please check:
1. Theme customizer settings are saved
2. Browser console for any JavaScript errors
3. Network tab to ensure images are loading

For additional support, contact your theme developer or Shopify support.