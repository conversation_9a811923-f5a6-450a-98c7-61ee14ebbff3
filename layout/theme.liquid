<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    {% # Inlined CSS Variables %}
    {% render 'css-variables' %}

    {% # Load and preload the critical CSS %}
    {{ 'critical.css' | asset_url | stylesheet_tag: preload: true }}

    {% # Social, title, etc. %}
    {% render 'meta-tags' %}

    {{ content_for_header }}
  </head>

  <body>
    {% sections 'header-group' %}

    {{ content_for_layout }}

    {% sections 'footer-group' %}
    
    {% # Cart Drawer %}
    {% render 'cart-drawer' %}

    <!-- VetApp integration temporarily disabled -->
    <!--
    <script>
    // Konfiguracja z Twoimi Variant ID
    const VETAPP_CONFIG = {
      vetHelp: {
        variantId: '55454972871000',  // Dostęp do VETHELP (30 dni)
        threshold: 50,
        sku: 'VETHELP'
      },
      vetHelpCare: {
        variantId: '55456871678296',  // Dostęp do VETHELPCARE (30 Dni)
        threshold: 100,
        sku: 'VETHELPCARE'
      }
    };

    // Flag to prevent concurrent execution
    let vetAppProcessing = false;

    // Funkcja sprawdzająca i dodająca bonusy
    async function checkAndAddVetAppBonus() {
      // Prevent concurrent executions
      if (vetAppProcessing) {
        console.log('VetApp bonus check already in progress, skipping...');
        return;
      }
      
      vetAppProcessing = true;
      
      try {
        // Pobierz aktualny koszyk
        const cart = await fetch('/cart.js').then(r => r.json());
        const cartTotal = cart.total_price / 100; // w złotówkach
        
        console.log('Wartość koszyka:', cartTotal, 'zł');
        
        // Sprawdź które produkty już są w koszyku
        const hasVetHelp = cart.items.some(item => 
          item.variant_id == VETAPP_CONFIG.vetHelp.variantId
        );
        const hasVetHelpCare = cart.items.some(item => 
          item.variant_id == VETAPP_CONFIG.vetHelpCare.variantId
        );
        
        // Lista produktów do dodania
        const toAdd = [];
        
        // Logika dodawania
        if (cartTotal >= VETAPP_CONFIG.vetHelpCare.threshold && !hasVetHelpCare) {
          // Powyżej 100 zł - dodaj VetHelp&Care
          toAdd.push({
            id: VETAPP_CONFIG.vetHelpCare.variantId,
            quantity: 1,
            properties: {
              '_is_bonus': 'true',
              '_threshold': '100zł'
            }
          });
          
          // Usuń VetHelp jeśli jest (zastąp lepszym)
          if (hasVetHelp) {
            await removeFromCart(VETAPP_CONFIG.vetHelp.variantId);
          }
        } 
        else if (cartTotal >= VETAPP_CONFIG.vetHelp.threshold && !hasVetHelp && !hasVetHelpCare) {
          // Między 50-99 zł - dodaj tylko VetHelp
          toAdd.push({
            id: VETAPP_CONFIG.vetHelp.variantId,
            quantity: 1,
            properties: {
              '_is_bonus': 'true',
              '_threshold': '50zł'
            }
          });
        }
        else if (cartTotal < VETAPP_CONFIG.vetHelp.threshold) {
          // Poniżej 50 zł - usuń bonusy jeśli są
          if (hasVetHelp) await removeFromCart(VETAPP_CONFIG.vetHelp.variantId);
          if (hasVetHelpCare) await removeFromCart(VETAPP_CONFIG.vetHelpCare.variantId);
        }
        
        // Dodaj produkty do koszyka
        if (toAdd.length > 0) {
          console.log('Dodaję bonusy VetApp:', toAdd);
          
          for (const item of toAdd) {
            await fetch('/cart/add.js', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(item)
            });
          }
          
          // Odśwież stronę lub zaktualizuj UI
          updateCartUI();
        }
        
      } catch (error) {
        console.error('Błąd przy dodawaniu bonusu VetApp:', error);
      } finally {
        // Always reset the flag to allow future checks
        vetAppProcessing = false;
      }
    }

    // Funkcja usuwająca z koszyka
    async function removeFromCart(variantId) {
      const cart = await fetch('/cart.js').then(r => r.json());
      const item = cart.items.find(i => i.variant_id == variantId);
      
      if (item) {
        await fetch('/cart/change.js', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            id: item.key,
            quantity: 0
          })
        });
      }
    }

    // Funkcja aktualizująca UI
    function updateCartUI() {
      // Zawsze używaj dynamicznych aktualizacji zamiast przeładowania
      // Removed reload to prevent hot reload loop
      fetch('/cart.js')
        .then(r => r.json())
        .then(cart => {
          // Aktualizuj liczniki koszyka
          document.querySelectorAll('.cart-count, [data-cart-count]').forEach(el => {
            el.textContent = cart.item_count;
          });
          
          // Aktualizuj total jeśli jesteśmy na stronie koszyka
          if (window.location.pathname.includes('/cart')) {
            const cartTotal = document.querySelector('[data-cart-total]');
            if (cartTotal) {
              cartTotal.textContent = (cart.total_price / 100).toFixed(2).replace('.', ',') + ' zł';
            }
          }
        });
    }

    // Nasłuchuj na zmiany w koszyku
    document.addEventListener('DOMContentLoaded', function() {
      // Sprawdź przy załadowaniu
      if (window.location.pathname.includes('/cart')) {
        checkAndAddVetAppBonus();
      }
      
      // Nasłuchuj na dodanie do koszyka (AJAX)
      document.addEventListener('cart:updated', checkAndAddVetAppBonus);
      document.addEventListener('ajaxCart:updated', checkAndAddVetAppBonus);
      
      // Dla formularzy dodawania do koszyka
      document.querySelectorAll('form[action="/cart/add"]').forEach(form => {
        form.addEventListener('submit', function() {
          setTimeout(checkAndAddVetAppBonus, 1000);
        });
      });
      
      // Dla przycisków AJAX - z debouncing
      let vetAppTimeout;
      document.addEventListener('click', function(e) {
        if (e.target.matches('[data-add-to-cart], .add-to-cart, .btn-addtocart')) {
          // Clear any existing timeout to prevent multiple triggers
          clearTimeout(vetAppTimeout);
          vetAppTimeout = setTimeout(checkAndAddVetAppBonus, 2000); // Increased delay to prevent conflicts
        }
      });
    });

    // Dodaj komunikat informacyjny
    function showVetAppMessage() {
      const messageHtml = `
        <div id="vetapp-bonus-message" style="
          background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
          padding: 15px 20px;
          margin: 20px 0;
          border-radius: 8px;
          border-left: 4px solid #4caf50;
          font-size: 14px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        ">
          <strong>🎁 Darmowe bonusy VetApp!</strong><br>
          • Zamówienie 50-99 zł = Darmowy VetApp VetHelp (30 dni)<br>
          • Zamówienie 100+ zł = Darmowy VetApp VetHelp&Care (30 dni)
        </div>
      `;
      
      // Wstaw komunikat w różnych miejscach
      const cartForm = document.querySelector('form[action="/cart"]');
      if (cartForm && !document.getElementById('vetapp-bonus-message')) {
        cartForm.insertAdjacentHTML('beforebegin', messageHtml);
      }
    }

    // Pokaż komunikat
    document.addEventListener('DOMContentLoaded', showVetAppMessage);
    </script>

    <style>
    /* Oznacz produkty bonus w koszyku */
    .cart-item[data-variant-id="55454972871000"] .price,
    .cart-item[data-variant-id="55456871678296"] .price {
      color: #4caf50;
      font-weight: bold;
    }

    .cart-item[data-variant-id="55454972871000"] .price::after,
    .cart-item[data-variant-id="55456871678296"] .price::after {
      content: " (BONUS)";
      color: #4caf50;
      font-size: 12px;
    }

    /* Ukryj przyciski usuwania dla bonusów */
    .cart-item[data-variant-id="55454972871000"] .remove-item,
    .cart-item[data-variant-id="55456871678296"] .remove-item {
      display: none;
    }
    </style>
    -->
    <!-- End of VetApp integration -->
  </body>
</html>
