#!/bin/bash
# Ayla Theme Deployment Script

echo "🚀 Starting Ayla Theme Deployment..."
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Store URL (update if needed)
STORE_URL="piolive.myshopify.com"

echo -e "${YELLOW}📝 Checking Shopify CLI installation...${NC}"
if ! command -v shopify &> /dev/null; then
    echo -e "${RED}❌ Shopify CLI is not installed${NC}"
    echo "Please install it first: npm install -g @shopify/cli @shopify/theme"
    exit 1
fi

echo -e "${GREEN}✓ Shopify CLI found${NC}"

echo -e "${YELLOW}🔐 Authenticating with Shopify...${NC}"
shopify whoami &> /dev/null || {
    echo "Please authenticate using the link provided:"
    shopify auth logout 2>/dev/null
    shopify theme dev --store $STORE_URL
}

echo -e "${YELLOW}📤 Pushing theme to store...${NC}"
echo "This may take a few minutes..."

# Push as unpublished theme first
shopify theme push --unpublished --theme "Ayla Theme" --store $STORE_URL

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Theme successfully uploaded!${NC}"
    echo ""
    echo "======================================"
    echo -e "${GREEN}🎉 DEPLOYMENT SUCCESSFUL!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Go to: https://$STORE_URL/admin/themes"
    echo "2. Find 'Ayla Theme' in your theme library"
    echo "3. Click 'Actions' > 'Publish' to make it live"
    echo ""
    echo "Or run: shopify theme push --live --theme 'Ayla Theme'"
    echo "======================================"
else
    echo -e "${RED}❌ Deployment failed. Please check the error messages above.${NC}"
    exit 1
fi