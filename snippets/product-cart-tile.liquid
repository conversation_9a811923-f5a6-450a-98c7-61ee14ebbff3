{% comment %}
  Reusable product tile for cart and recommendations
  Usage: {% render 'product-cart-tile', product: product, variant: 'small' %}
{% endcomment %}

{% assign variant = variant | default: 'small' %}
{% assign show_add_to_cart = show_add_to_cart | default: true %}

  <div class="product-cart-tile product-cart-tile--{{ variant }}">
  <!-- Product Image -->
  <div class="product-cart-tile__image">
    <a href="{{ product.url }}" class="product-cart-tile__image-link">
      {% if product.featured_image %}
        <img
          src="{{ product.featured_image | image_url: width: 286 }}"
          alt="{{ product.featured_image.alt | escape }}"
          width="286"
          height="236"
          loading="lazy">
      {% else %}
        <div class="product-cart-tile__placeholder"></div>
      {% endif %}
    </a>
  </div>

  <!-- Product Details -->
  <div class="product-cart-tile__details">
    <div class="product-cart-tile__info">
      <h3 class="product-cart-tile__title">
        <a href="{{ product.url }}">{{ product.title | truncate: 60 }}</a>
      </h3>
      {% if product.description %}
        <p class="product-cart-tile__description">{{ product.description | truncate: 100 }}</p>
      {% endif %}
    </div>
  </div>

  <!-- Add to Cart Button -->
  {% if show_add_to_cart %}
    <form
      action="/cart/add"
      method="post"
      enctype="multipart/form-data"
      class="product-cart-tile__form">
      <input
        type="hidden"
        name="id"
        value="{{ product.selected_or_first_available_variant.id }}">
      <button
        type="submit"
        class="product-cart-tile__add-btn"
        data-success-icon="{{ '3589a8fde2a91501906cf41d590682ce1538c36e.svg' | asset_url }}">
        <div class="product-cart-tile__add-content">
          <svg
            class="product-cart-tile__cart-icon"
            width="29"
            height="28"
            viewBox="0 0 29 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M21.2269 24.0784H7.78688C6.54928 24.0784 5.54688 23.076 5.54688 21.8384V10.0784C5.54688 9.15108 6.29952 8.39844 7.22688 8.39844H21.7869C22.7142 8.39844 23.4669 9.15108 23.4669 10.0784V21.8384C23.4669 23.076 22.4645 24.0784 21.2269 24.0784Z"
              stroke="#2D4F40"
              stroke-linecap="round"
              stroke-linejoin="round" />
            <path
              d="M18.9808 12.0847C18.8262 12.0847 18.7008 12.2101 18.7019 12.3647C18.7019 12.5192 18.8273 12.6447 18.9819 12.6447C19.1364 12.6447 19.2619 12.5192 19.2619 12.3647C19.2619 12.2101 19.1364 12.0847 18.9808 12.0847"
              stroke="#2D4F40"
              stroke-linecap="round"
              stroke-linejoin="round" />
            <path
              d="M10.0276 12.0847C9.87307 12.0847 9.74763 12.2101 9.74875 12.3647C9.74875 12.5192 9.87419 12.6447 10.0288 12.6447C10.1833 12.6447 10.3088 12.5192 10.3088 12.3647C10.3088 12.2101 10.1833 12.0847 10.0276 12.0847"
              stroke="#2D4F40"
              stroke-linecap="round"
              stroke-linejoin="round" />
            <path
              d="M10.0312 8.39406V8.11406V8.11406C10.0312 5.79454 11.9117 3.91406 14.2313 3.91406H14.7913C17.1108 3.91406 18.9913 5.79454 18.9913 8.11406V8.11406V8.39406"
              stroke="#2D4F40"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          <span class="product-cart-tile__add-text">Dodaj</span>
        </div>
        <span class="product-cart-tile__price">{{ product.price | money }}</span>
      </button>
    </form>
  {% endif %}
</div>

<style>
  .product-cart-tile {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    width: 100%;
  }

  .product-cart-tile--small {
    max-width: 236px;
  }

  .product-cart-tile__image {
    display: flex;
    height: 236px;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    background: var(--beige-50, #F9F9F4);
    overflow: hidden;
  }

  .product-cart-tile__image-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
  }

  .product-cart-tile__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .product-cart-tile__placeholder {
    width: 100%;
    height: 100%;
    background: var(--beige-50, #F9F9F4);
  }

  .product-cart-tile__details {
    display: flex;
    align-items: center;
    gap: 12px;
    align-self: stretch;
  }

  .product-cart-tile__info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 11px;
    flex: 1 0 0;
  }

  .product-cart-tile__title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.48px;
    color: var(--primary-color-700, #2D4F40);
  }

  .product-cart-tile__title a {
    text-decoration: none;
    color: inherit;
  }

  .product-cart-tile__description {
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 140%;
    letter-spacing: -0.24px;
    color: var(--primary-color-700, #2D4F40);
    opacity: 0.8;
  }

  .product-cart-tile__form {
    width: 100%;
  }

  .product-cart-tile__add-btn {
    display: flex;
    height: 44px;
    /* lock height */
    padding: 12px 32px;
    justify-content: center;
    align-items: center;
    gap: 12px;
    width: 100%;
    min-width: 200px;
    /* ensure consistent width regardless of content */
    border-radius: 44px;
    border: 1px solid var(--beige-100, #F0F0E4);
    background: var(--primary-color-0, #FFF);
    cursor: pointer;
    font-family: inherit;
  }

  .product-cart-tile__add-content {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .product-cart-tile__cart-icon {
    width: 28px;
    height: 28px;
  }

  .product-cart-tile__add-text {
    color: var(--primary-color-700, #2D4F40);
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
    letter-spacing: -0.28px;
  }
  /* Success state styled per Figma */
  .product-cart-tile__add-btn.add-to-cart--success {
    background: #2D4F40;
    border-color: #2D4F40;
    justify-content: center;
    /* center content during success state */
  }
  .product-cart-tile__add-btn.add-to-cart--success .product-cart-tile__add-text {
    color: #FFFFFF;
  }
  .product-cart-tile__add-btn.add-to-cart--success .product-cart-tile__price {
    /* Completely remove price from layout flow during success state */
    display: none;
  }
  .product-cart-tile__add-btn.add-to-cart--success .add-to-cart-success-icon {
    width: 28px;
    height: 28px;
    filter: brightness(0) invert(1);
  }

  .product-cart-tile__price {
    color: var(--primary-color-700, #2D4F40);
    font-size: 16px;
    font-weight: 500;
    line-height: 110%;
    letter-spacing: -0.48px;
  }

  @media (max-width: 768px) {
    .product-cart-tile--small {
      max-width: 100%;
    }
  }
</style>