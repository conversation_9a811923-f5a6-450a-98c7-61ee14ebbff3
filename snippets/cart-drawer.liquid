{% comment %}
  Cart Drawer - Slide-out cart panel
  Opens when products are added to cart
{% endcomment %}

<div
  id="cart-drawer"
  class="cart-drawer"
  aria-hidden="true">
  <div class="cart-drawer__overlay"></div>
  <div class="cart-drawer__container">
    <!-- Header -->
    <div class="cart-drawer__header">
      <h2 class="cart-drawer__title">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="cart-drawer__icon">
          <path
            d="M5 7H19C19.55 7 20 7.45 20 8V18C20 19.1 19.1 20 18 20H6C4.9 20 4 19.1 4 18V8C4 7.45 4.45 7 5 7Z"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            fill="none" />
          <path
            d="M8 7V6.5C8 4.5 9.5 3 11.5 3H12.5C14.5 3 16 4.5 16 6.5V7"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round" />
          <circle
            cx="8"
            cy="10.5"
            r="0.5"
            fill="currentColor" />
          <circle
            cx="16"
            cy="10.5"
            r="0.5"
            fill="currentColor" />
        </svg>
        Twój koszyk
      </h2>
      <button class="cart-drawer__close" aria-label="Zamknij koszyk">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path
            d="M18 6L6 18M6 6L18 18"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round" />
        </svg>
      </button>
    </div>

    <!-- Free Shipping Notice -->
    <div class="cart-drawer__shipping-notice">
      <span class="shipping-notice__text">Wydaj jeszcze
        <span class="shipping-notice__amount">33,00 zł</span>, aby uzyskać bezpłatną dostawę do paczkomatu</span>
    </div>

    <!-- Cart Items Count -->
    <div class="cart-drawer__count">
      <span class="cart-count__number">2</span>
      produkty w koszyku
    </div>

    <!-- Cart Items -->
    <div class="cart-drawer__items" data-cart-items>
      <!-- Cart items will be dynamically inserted here -->
    </div>

    <!-- Cart Footer -->
    <div class="cart-drawer__footer">
      <button class="cart-drawer__checkout-btn">
        <img
          src="{{ 'Delivery icon.png' | asset_url }}"
          alt="Delivery"
          class="checkout-btn__icon"
          width="20"
          height="20">
        <span class="checkout-btn__text">ZAMÓW</span>
        <span class="checkout-btn__separator">•</span>
        <span class="checkout-btn__total">103,80 ZŁ</span>
      </button>
    </div>
  </div>
</div>

<style>
  /* Cart Drawer Container */
  .cart-drawer {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    pointer-events: none;
    display: flex;
    justify-content: flex-end;
  }

  .cart-drawer[aria-hidden="false"] {
    pointer-events: all;
  }

  /* Overlay */
  .cart-drawer__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .cart-drawer[aria-hidden="false"] .cart-drawer__overlay {
    opacity: 1;
  }

  /* Drawer Container */
  .cart-drawer__container {
    position: relative;
    width: 497px;
    max-width: 90vw;
    height: 100%;
    background: #FFFFFF;
    box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .cart-drawer[aria-hidden="false"] .cart-drawer__container {
    transform: translateX(0);
  }

  /* Header */
  .cart-drawer__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px;
    border-bottom: 1px solid #F0F0E4;
    background: #FFFFFF;
  }

  .cart-drawer__title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    font-size: 20px;
    font-weight: 400;
    color: #2D4F40;
    letter-spacing: -0.4px;
  }

  .cart-drawer__icon {
    width: 24px;
    height: 24px;
    color: #2D4F40;
  }

  .cart-drawer__close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #2D4F40;
    transition: opacity 0.2s ease;
  }

  .cart-drawer__close:hover {
    opacity: 0.7;
  }

  /* Shipping Notice */
  .cart-drawer__shipping-notice {
    padding: 16px 32px;
    background: #F9F9F4;
    border-bottom: 1px solid #F0F0E4;
    transition: all 0.3s ease;
  }

  .cart-drawer__shipping-notice.free-shipping-achieved {
    background: #4C7D67;
    border-bottom: none;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .shipping-notice__text {
    font-size: 14px;
    color: #2D4F40;
    line-height: 140%;
  }

  .cart-drawer__shipping-notice.free-shipping-achieved .shipping-notice__text {
    color: #FFFFFF;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .shipping-notice__amount {
    font-weight: 600;
    color: #4C7D67;
  }

  .shipping-notice__icon {
    width: 24px;
    height: 24px;
    display: none;
  }

  .cart-drawer__shipping-notice.free-shipping-achieved .shipping-notice__icon {
    display: block;
    filter: brightness(0) invert(1);
  }

  /* Cart Count */
  .cart-drawer__count {
    padding: 20px 32px 16px;
    font-size: 14px;
    color: #2D4F40;
    opacity: 0.7;
  }

  .cart-count__number {
    font-weight: 600;
  }

  /* Cart Items Container */
  .cart-drawer__items {
    flex: 1;
    overflow-y: auto;
    padding: 0 32px 24px;
  }

  /* Cart Item */
  .cart-drawer__item {
    display: flex;
    gap: 16px;
    padding: 20px 0;
    border-bottom: 1px solid #F0F0E4;
  }

  .cart-drawer__item:last-child {
    border-bottom: none;
  }

  .cart-item__image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    background: #F9F9F4;
    overflow: hidden;
    flex-shrink: 0;
  }

  .cart-item__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .cart-item__details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .cart-item__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
  }

  .cart-item__title {
    font-size: 16px;
    font-weight: 500;
    color: #2D4F40;
    line-height: 110%;
    letter-spacing: -0.32px;
    text-decoration: none;
    flex: 1;
  }

  .cart-item__variant {
    font-size: 14px;
    color: #2D4F40;
    opacity: 0.6;
  }

  .cart-item__bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
  }

  /* Quantity Selector */
  .cart-item__quantity {
    display: flex;
    align-items: center;
    gap: 12px;
    background: #F9F9F4;
    border-radius: 24px;
    padding: 4px;
  }

  .quantity__btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #2D4F40;
    transition: opacity 0.2s ease;
  }

  .quantity__btn:hover:not(:disabled) {
    opacity: 0.7;
  }

  .quantity__btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .quantity__value {
    min-width: 20px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #2D4F40;
  }

  .cart-item__price {
    font-size: 18px;
    font-weight: 500;
    color: #2D4F40;
    letter-spacing: -0.32px;
  }

  .cart-item__remove {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 0;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #4C7D67;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
  }

  .cart-item__remove:hover {
    opacity: 0.7;
  }

  .cart-item__remove-icon {
    width: 20px;
    height: 20px;
  }

  .cart-item__remove-text {
    font-size: 14px;
    font-weight: 400;
    color: #4C7D67;
    letter-spacing: -0.28px;
  }

  /* Footer */
  .cart-drawer__footer {
    padding: 24px 32px 32px;
    background: #FFFFFF;
    border-top: 1px solid #F0F0E4;
  }

  .cart-drawer__checkout-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 32px;
    background: #2D4F40;
    border: none;
    border-radius: 44px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .cart-drawer__checkout-btn:hover {
    background: #4C7D67;
  }

  .checkout-btn__icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    filter: brightness(0) invert(1);
    /* Makes the icon white */
  }

  .checkout-btn__text,
  .checkout-btn__separator,
  .checkout-btn__total {
    color: #FFFFFF;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }

  .checkout-btn__separator {
    opacity: 0.5;
  }

  /* Mobile Styles */
  @media (max-width: 640px) {
    .cart-drawer__container {
      width: 100%;
      max-width: 100%;
    }

    .cart-drawer__header,
    .cart-drawer__shipping-notice,
    .cart-drawer__count,
    .cart-drawer__items,
    .cart-drawer__footer {
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  /* Empty State */
  .cart-drawer__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 32px;
    text-align: center;
    height: 100%;
  }

  .cart-drawer__empty-image {
    width: 240px;
    height: auto;
    margin-bottom: 40px;
  }

  .cart-drawer__empty-title {
    font-size: 24px;
    font-weight: 500;
    color: #2D4F40;
    margin: 0 0 16px;
  }

  .cart-drawer__empty-text {
    font-size: 14px;
    line-height: 1.6;
    color: #2D4F40;
    opacity: 0.7;
    margin: 0 0 40px;
    max-width: 320px;
  }

  .cart-drawer__products-link {
    display: inline-block;
    padding: 14px 40px;
    background: transparent;
    color: #2D4F40;
    border: 1px solid #2D4F40;
    border-radius: 44px;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .cart-drawer__products-link:hover {
    background: #2D4F40;
    color: #FFFFFF;
  }
</style>

<script>
  class CartDrawer {
    constructor() {
      this.drawer = document.getElementById('cart-drawer');
      this.overlay = this.drawer.querySelector('.cart-drawer__overlay');
      this.closeBtn = this.drawer.querySelector('.cart-drawer__close');
      this.checkoutBtn = this.drawer.querySelector('.cart-drawer__checkout-btn');
      this.itemsContainer = this.drawer.querySelector('[data-cart-items]');
      this.isOpen = false;
      
      this.init();
    }
    
    init() {
      // Close button
      this.closeBtn?.addEventListener('click', () => this.close());
      
      // Overlay click to close
      this.overlay?.addEventListener('click', () => this.close());
      
      // Checkout button
      this.checkoutBtn?.addEventListener('click', () => {
        window.location.href = '/cart';
      });
      
      // Header cart icon click - open drawer instead of navigating
      const headerCartLink = document.querySelector('.navbar-cart .navbar-icon-link');
      if (headerCartLink) {
        headerCartLink.addEventListener('click', (e) => {
          e.preventDefault();
          this.open();
          this.updateCart();
        });
      }
      
      // Listen for add to cart events
      document.addEventListener('cart:added', (event) => {
        this.open();
        this.updateCart();
      });
      
      // Listen for cart updates
      document.addEventListener('cart:updated', () => {
        this.updateCart();
      });
      
      // Intercept add to cart forms
      this.interceptAddToCartForms();
      
      // Initial cart update
      this.updateCart();
    }
    
    open() {
      this.drawer.setAttribute('aria-hidden', 'false');
      document.body.style.overflow = 'hidden';
      this.isOpen = true;
    }
    
    close() {
      this.drawer.setAttribute('aria-hidden', 'true');
      document.body.style.overflow = '';
      this.isOpen = false;
    }
    
    async updateCart() {
      try {
        const response = await fetch('/cart.js');
        const cart = await response.json();
        
        this.renderCartItems(cart);
        this.updateCartCount(cart);
        this.updateShippingNotice(cart);
        this.updateCheckoutButton(cart);
      } catch (error) {
        console.error('Error updating cart:', error);
      }
    }
    
    renderCartItems(cart) {
      if (!this.itemsContainer) return;
      
      if (cart.item_count === 0) {
        this.itemsContainer.innerHTML = `
          <div class="cart-drawer__empty">
            <img src="{{ 'empty-card-image.png' | asset_url }}" alt="Empty cart" class="cart-drawer__empty-image">
            <h3 class="cart-drawer__empty-title">Twój koszyk jest pusty</h3>
            <p class="cart-drawer__empty-text">Zaloguj się, aby zobaczyć produkty w swoim koszyku zamówienia lub dodaj nasze produkty AYLA.</p>
            <a href="/collections/all" class="cart-drawer__products-link">NASZE PRODUKTY</a>
          </div>
        `;
        return;
      }
      
      const itemsHTML = cart.items.map(item => this.renderCartItem(item)).join('');
      this.itemsContainer.innerHTML = itemsHTML;
      
      // Bind quantity controls
      this.bindQuantityControls();
    }
    
    renderCartItem(item) {
      const itemKey = item.key || item.id;
      return `
        <div class="cart-drawer__item" data-item-id="${itemKey}">
          <div class="cart-item__image">
            ${item.image ? `<img src="${item.image}" alt="${item.title}" loading="lazy">` : ''}
          </div>
          <div class="cart-item__details">
            <div class="cart-item__header">
              <a href="${item.url}" class="cart-item__title">${item.product_title}</a>
              <button class="cart-item__remove" data-item-id="${itemKey}" aria-label="Usuń">
                <img src="{{ 'trashbinicon.svg' | asset_url }}" alt="" class="cart-item__remove-icon">
                <span class="cart-item__remove-text">Usuń</span>
              </button>
            </div>
            ${item.variant_title && item.variant_title !== 'Default Title' ? 
              `<span class="cart-item__variant">${item.variant_title}</span>` : ''}
            <div class="cart-item__bottom">
              <div class="cart-item__quantity">
                <button class="quantity__btn quantity__btn--minus" data-action="decrease" data-item-id="${itemKey}" ${item.quantity <= 1 ? 'disabled' : ''}>
                  <svg width="12" height="2" viewBox="0 0 12 2" fill="none">
                    <path d="M1 1H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  </svg>
                </button>
                <span class="quantity__value">${item.quantity}</span>
                <button class="quantity__btn quantity__btn--plus" data-action="increase" data-item-id="${itemKey}">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <path d="M6 1V11M1 6H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  </svg>
                </button>
              </div>
              <span class="cart-item__price">${this.formatMoney(item.final_line_price)}</span>
            </div>
          </div>
        </div>
      `;
    }
    
    bindQuantityControls() {
      // Quantity buttons
      this.itemsContainer.querySelectorAll('.quantity__btn').forEach(btn => {
        btn.addEventListener('click', async (e) => {
          const itemId = btn.dataset.itemId;
          const action = btn.dataset.action;
          const currentItem = this.drawer.querySelector(`[data-item-id="${itemId}"]`);
          const quantityEl = currentItem.querySelector('.quantity__value');
          let quantity = parseInt(quantityEl.textContent);
          
          if (action === 'increase') {
            quantity++;
          } else if (action === 'decrease' && quantity > 1) {
            quantity--;
          }
          
          await this.updateItemQuantity(itemId, quantity);
        });
      });
      
      // Remove buttons
      this.itemsContainer.querySelectorAll('.cart-item__remove').forEach(btn => {
        btn.addEventListener('click', async (e) => {
          const itemId = btn.dataset.itemId;
          await this.updateItemQuantity(itemId, 0);
        });
      });
    }
    
    async updateItemQuantity(itemId, quantity) {
      try {
        // First, update the UI optimistically
        const itemElement = this.drawer.querySelector(`[data-item-id="${itemId}"]`);
        if (itemElement && quantity > 0) {
          const quantityEl = itemElement.querySelector('.quantity__value');
          if (quantityEl) {
            quantityEl.textContent = quantity;
          }
          const minusBtn = itemElement.querySelector('.quantity__btn--minus');
          if (minusBtn) {
            minusBtn.disabled = quantity <= 1;
          }
        }
        
        const response = await fetch('/cart/change.js', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: itemId,
            quantity: quantity
          })
        });
        
        const cart = await response.json();
        
        // Only re-render if item was removed
        if (quantity === 0) {
          this.renderCartItems(cart);
        } else {
          // Update the price after we get the response
          const updatedItem = cart.items.find(item => {
            const key = item.key || item.id;
            return String(key) === String(itemId);
          });
          if (updatedItem && itemElement) {
            const priceEl = itemElement.querySelector('.cart-item__price');
            if (priceEl) {
              priceEl.textContent = this.formatMoney(updatedItem.final_line_price);
            }
            // Ensure quantity is correctly displayed
            const quantityEl = itemElement.querySelector('.quantity__value');
            if (quantityEl && quantityEl.textContent !== String(updatedItem.quantity)) {
              quantityEl.textContent = updatedItem.quantity;
            }
          }
        }
        
        this.updateCartCount(cart);
        this.updateShippingNotice(cart);
        this.updateCheckoutButton(cart);
        
        // Dispatch cart updated event
        document.dispatchEvent(new CustomEvent('cart:updated', { detail: cart }));
      } catch (error) {
        console.error('Error updating quantity:', error);
        // Reload cart on error
        this.updateCart();
      }
    }
    
    updateSingleItem(itemId, item) {
      const itemElement = this.drawer.querySelector(`[data-item-id="${itemId}"]`);
      if (!itemElement) return;
      
      // Update quantity display
      const quantityEl = itemElement.querySelector('.quantity__value');
      if (quantityEl) {
        quantityEl.textContent = item.quantity;
      }
      
      // Update price
      const priceEl = itemElement.querySelector('.cart-item__price');
      if (priceEl) {
        priceEl.textContent = this.formatMoney(item.final_line_price);
      }
      
      // Update minus button disabled state
      const minusBtn = itemElement.querySelector('.quantity__btn--minus');
      if (minusBtn) {
        minusBtn.disabled = item.quantity <= 1;
      }
    }
    
    updateCartCount(cart) {
      const countEl = this.drawer.querySelector('.cart-drawer__count');
      if (countEl) {
        countEl.innerHTML = `<span class="cart-count__number">${cart.item_count}</span> ${cart.item_count === 1 ? 'produkt' : 'produkty'} w koszyku`;
      }
      
      // Update header cart count
      document.querySelectorAll('[data-cart-count]').forEach(el => {
        el.textContent = cart.item_count;
      });
    }
    
    updateShippingNotice(cart) {
      const noticeEl = this.drawer.querySelector('.cart-drawer__shipping-notice');
      if (!noticeEl) return;
      
      const freeShippingThreshold = 10000; // 100 PLN in cents
      const remaining = Math.max(0, freeShippingThreshold - cart.total_price);
      
      if (remaining > 0) {
        noticeEl.classList.remove('free-shipping-achieved');
        noticeEl.innerHTML = `
          <span class="shipping-notice__text">
            Wydaj jeszcze <span class="shipping-notice__amount">${this.formatMoney(remaining)}</span>, 
            aby uzyskać bezpłatną dostawę do paczkomatu
          </span>
        `;
      } else {
        noticeEl.classList.add('free-shipping-achieved');
        noticeEl.innerHTML = `
          <span class="shipping-notice__text">
            <img src="{{ 'icon-opakowanie.png' | asset_url }}" alt="" class="shipping-notice__icon">
            Bezpłatna dostawa do paczkomatu
          </span>
        `;
      }
    }
    
    updateCheckoutButton(cart) {
      const totalEl = this.drawer.querySelector('.checkout-btn__total');
      if (totalEl) {
        totalEl.textContent = this.formatMoney(cart.total_price);
      }
    }
    
    formatMoney(cents) {
      const amount = cents / 100;
      return new Intl.NumberFormat('pl-PL', {
        style: 'currency',
        currency: 'PLN',
        minimumFractionDigits: 2
      }).format(amount).replace('PLN', 'zł').toUpperCase();
    }
    
    interceptAddToCartForms() {
      document.addEventListener('submit', async (e) => {
        const form = e.target;
        
        // Check if this is an add to cart form
        if (!form.action || !form.action.includes('/cart/add')) return;
        // Avoid double-handling for recommendation/product-cart tiles handled by EnhancedCart.js
        if (form.classList && form.classList.contains('product-cart-tile__form')) return;
        
        e.preventDefault();
        
        const formData = new FormData(form);
        const submitButton = form.querySelector('[type="submit"]');
        const labelSpan = submitButton?.querySelector('.add-to-cart-text');
        const originalText = labelSpan ? labelSpan.textContent : submitButton?.textContent;
        
        if (submitButton) {
          // Disable only; keep original label to preserve pill size
          submitButton.disabled = true;
        }
        
        try {
          const response = await fetch('/cart/add.js', {
            method: 'POST',
            body: formData
          });
          
          if (response.ok) {
            const item = await response.json();
            
            // Dispatch cart added event
            document.dispatchEvent(new CustomEvent('cart:added', { detail: item }));
            
            // Open drawer and update
            this.open();
            this.updateCart();

            // Success UI: switch to Figma-styled "Dodano" pill for a moment
            if (submitButton) {
              submitButton.classList.add('add-to-cart--success');
              const successIconUrl = submitButton.getAttribute('data-success-icon');
              const iconContainer = submitButton.querySelector('svg');
              if (iconContainer) {
                // Replace icon with saved SVG asset path (white icon on green bg)
                const iconSrc = successIconUrl || `{{ '3589a8fde2a91501906cf41d590682ce1538c36e.svg' | asset_url }}`;
                iconContainer.outerHTML = `<img src="${iconSrc}" alt="" class="add-to-cart-success-icon" width="28" height="28">`;
              }
              if (labelSpan) labelSpan.textContent = 'Dodano';
              // Auto-revert after 1500ms
              setTimeout(() => {
                submitButton.classList.remove('add-to-cart--success');
                // Restore icon to original SVG outline cart
                const successIcon = submitButton.querySelector('.add-to-cart-success-icon');
                if (successIcon) {
                  successIcon.outerHTML = `
                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M20.7269 24.0784H7.28688C6.04928 24.0784 5.04688 23.076 5.04688 21.8384V10.0784C5.04688 9.15108 5.79952 8.39844 6.72688 8.39844H21.2869C22.2142 8.39844 22.9669 9.15108 22.9669 10.0784V21.8384C22.9669 23.076 21.9645 24.0784 20.7269 24.0784Z" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M18.4808 12.0847C18.3262 12.0847 18.2008 12.2101 18.2019 12.3647C18.2019 12.5192 18.3273 12.6447 18.4819 12.6447C18.6364 12.6447 18.7619 12.5192 18.7619 12.3647C18.7619 12.2101 18.6364 12.0847 18.4808 12.0847" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M9.52763 12.0847C9.37307 12.0847 9.24763 12.2101 9.24875 12.3647C9.24875 12.5192 9.37419 12.6447 9.52875 12.6447C9.68331 12.6447 9.80875 12.5192 9.80875 12.3647C9.80875 12.2101 9.68331 12.0847 9.52763 12.0847" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M9.5312 8.39406V8.11406V8.11406C9.5312 5.79454 11.4117 3.91406 13.7312 3.91406H14.2912C16.6108 3.91406 18.4912 5.79454 18.4912 8.11406V8.11406V8.39406" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>`;
                }
                if (labelSpan) labelSpan.textContent = originalText;
              }, 1500);
            }
          }
        } catch (error) {
          console.error('Error adding to cart:', error);
        } finally {
          if (submitButton) {
            submitButton.disabled = false;
            // If success state is active, defer text restore to timeout above
            if (!submitButton.classList.contains('add-to-cart--success')) {
              if (labelSpan) {
                labelSpan.textContent = originalText;
              } else {
                submitButton.textContent = originalText;
              }
            }
          }
        }
      });
    }
  }
  
  // Initialize cart drawer when DOM is ready (but not on cart/checkout pages)
  document.addEventListener('DOMContentLoaded', () => {
    // Don't initialize cart drawer on cart or checkout pages
    const isCartPage = window.location.pathname.includes('/cart');
    const isCheckoutPage = window.location.pathname.includes('/checkout');
    
    if (!isCartPage && !isCheckoutPage) {
      window.cartDrawer = new CartDrawer();
    }
  });
</script>