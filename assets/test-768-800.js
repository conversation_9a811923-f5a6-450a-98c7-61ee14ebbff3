/**
 * Test 768-800px Range
 * Specifically tests the problematic range where wrong images show
 */

(function() {
  console.log('=== TESTING 768-800px RANGE ===');
  
  const viewport = window.innerWidth;
  
  if (viewport >= 768 && viewport <= 800) {
    console.warn(`🔴 CURRENT VIEWPORT: ${viewport}px - IN CRITICAL RANGE`);
    console.log('Expected: TABLET images should be showing');
    
    const cards = document.querySelectorAll('.product-card');
    
    cards.forEach((card, index) => {
      const cardName = card.classList.contains('delight-card') ? 'DELIGHT' :
                      card.classList.contains('help-card') ? 'HELP' :
                      card.classList.contains('rescue-card') ? 'RESCUE' : 'Unknown';
      
      const desktopBg = card.getAttribute('data-desktop-bg');
      const tabletBg = card.getAttribute('data-tablet-bg');
      const currentBg = window.getComputedStyle(card).backgroundImage;
      const currentMode = card.getAttribute('data-current-mode') || card.getAttribute('data-active-mode');
      
      console.group(`Card ${index + 1}: ${cardName}`);
      console.log('Current mode attribute:', currentMode);
      console.log('Has tablet image?', tabletBg ? 'YES' : 'NO');
      
      if (tabletBg) {
        // Check if tablet image is actually showing
        if (currentBg.includes('600x')) {
          console.log('✅ CORRECT: Tablet image IS showing');
        } else if (currentBg.includes('800x')) {
          console.error('❌ WRONG: Desktop image is showing instead of tablet!');
          console.log('Should be:', tabletBg);
          console.log('But showing:', currentBg);
        } else {
          console.warn('⚠️ UNKNOWN: Cannot determine which image is showing');
        }
      } else {
        console.log('No tablet image set, using desktop as fallback');
      }
      console.groupEnd();
    });
    
    // Force correct images
    console.log('\n🔧 FORCING CORRECT IMAGES...');
    cards.forEach((card, index) => {
      const tabletBg = card.getAttribute('data-tablet-bg');
      const desktopBg = card.getAttribute('data-desktop-bg');
      const bg = tabletBg || desktopBg;
      
      if (bg) {
        card.setAttribute('style', 
          `background-image: url('${bg}') !important; ` +
          `background-size: cover !important; ` +
          `background-position: center !important; ` +
          `background-repeat: no-repeat !important;`
        );
        card.setAttribute('data-forced-mode', 'tablet');
        console.log(`Card ${index + 1}: Forced tablet image`);
      }
    });
    
  } else {
    console.log(`Current viewport: ${viewport}px - Not in critical range (768-800)`);
    console.log('Resize to 768-800px to test the problematic range');
  }
  
  // Add visual indicator
  const indicator = document.createElement('div');
  indicator.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: ${viewport >= 768 && viewport <= 800 ? '#FF5722' : '#4CAF50'};
    color: white;
    padding: 20px;
    border-radius: 8px;
    font-size: 18px;
    font-weight: bold;
    z-index: 99999;
    box-shadow: 0 4px 6px rgba(0,0,0,0.3);
  `;
  indicator.textContent = viewport >= 768 && viewport <= 800 
    ? `⚠️ IN CRITICAL RANGE: ${viewport}px` 
    : `✅ OUTSIDE CRITICAL RANGE: ${viewport}px`;
  document.body.appendChild(indicator);
  
  setTimeout(() => indicator.remove(), 5000);
})();