/**
 * Check Product Lines Images
 * Verifies which images are uploaded for each card
 */

(function() {
  console.log('=== CHECKING PRODUCT LINES IMAGES ===');
  
  const cards = document.querySelectorAll('.product-card');
  
  cards.forEach((card, index) => {
    const cardName = card.classList.contains('delight-card') ? 'DELIGHT' :
                    card.classList.contains('help-card') ? 'HELP' :
                    card.classList.contains('rescue-card') ? 'RESCUE' : 'Unknown';
    
    const desktopBg = card.getAttribute('data-desktop-bg');
    const tabletBg = card.getAttribute('data-tablet-bg');
    const mobileBg = card.getAttribute('data-mobile-bg');
    
    console.group(`Card ${index + 1}: ${cardName}`);
    console.log('Desktop Image:', desktopBg || '❌ NOT SET');
    console.log('Tablet Image:', tabletBg || '❌ NOT SET - Will use desktop as fallback');
    console.log('Mobile Image:', mobileBg || '❌ NOT SET - Will use tablet/desktop as fallback');
    
    // Check current style
    const currentStyle = window.getComputedStyle(card).backgroundImage;
    console.log('Current Background:', currentStyle);
    
    // Check viewport
    const viewport = window.innerWidth;
    const expectedMode = viewport <= 480 ? 'mobile' : viewport <= 800 ? 'tablet' : 'desktop';
    console.log(`Viewport: ${viewport}px - Should show: ${expectedMode} image`);
    
    // Verify if correct image is showing
    if (expectedMode === 'tablet' && tabletBg) {
      if (currentStyle.includes('600x')) {
        console.log('✅ Tablet image is showing correctly');
      } else {
        console.log('❌ Tablet image should be showing but is not');
      }
    }
    
    console.groupEnd();
  });
  
  console.log('\n=== INSTRUCTIONS ===');
  console.log('1. If tablet images show "NOT SET", you need to upload them in Shopify admin');
  console.log('2. Go to Theme Customizer > Ayla Product Lines section');
  console.log('3. For each card, upload a "Tablet Background Image"');
  console.log('4. The images should be the product packages shown in the Figma design');
  console.log('5. Recommended size: 600x width for tablet images');
})();