/**
 * Hero Section Breakpoint Debugger
 * Helps diagnose responsive issues with the hero section
 */

(function() {
  'use strict';
  
  // Create debug panel
  function createDebugPanel() {
    const panel = document.createElement('div');
    panel.id = 'hero-debug-panel';
    panel.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 15px;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      z-index: 99999;
      min-width: 300px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    `;
    
    panel.innerHTML = `
      <div style="margin-bottom: 10px; font-weight: bold; border-bottom: 1px solid #666; padding-bottom: 5px;">
        🔍 Hero Debug Panel
      </div>
      <div id="debug-viewport">Viewport: --</div>
      <div id="debug-mode">Mode: --</div>
      <div id="debug-breakpoint">Breakpoint: --</div>
      <div id="debug-css">CSS Match: --</div>
      <div id="debug-js">JS State: --</div>
      <div id="debug-elements" style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
        <div id="debug-bg">Background: --</div>
        <div id="debug-heading">Heading: --</div>
        <div id="debug-button">Button: --</div>
      </div>
      <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
        <button onclick="testHeroBreakpoint(679)" style="margin: 2px; padding: 4px 8px;">679px</button>
        <button onclick="testHeroBreakpoint(680)" style="margin: 2px; padding: 4px 8px;">680px</button>
        <button onclick="testHeroBreakpoint(799)" style="margin: 2px; padding: 4px 8px;">799px</button>
        <button onclick="testHeroBreakpoint(800)" style="margin: 2px; padding: 4px 8px;">800px</button>
        <button onclick="testHeroBreakpoint(801)" style="margin: 2px; padding: 4px 8px;">801px</button>
      </div>
      <div style="margin-top: 10px;">
        <button onclick="document.getElementById('hero-debug-panel').remove()" style="background: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">Close Debug</button>
      </div>
    `;
    
    document.body.appendChild(panel);
  }
  
  // Update debug info
  function updateDebugInfo() {
    const viewport = window.innerWidth;
    const htmlElement = document.documentElement;
    const heroView = htmlElement.getAttribute('data-hero-view');
    const viewportData = htmlElement.getAttribute('data-viewport-width');
    
    // Determine expected mode based on viewport
    const expectedMode = viewport <= 800 ? 'tablet' : 'desktop';
    const actualMode = heroView || 'unknown';
    const modeMatch = expectedMode === actualMode;
    
    // Check CSS media query matches
    const cssDesktop = window.matchMedia('(min-width: 801px)').matches;
    const cssTablet = window.matchMedia('(max-width: 800px)').matches;
    
    // Get active slide
    const activeSlide = document.querySelector('.hero-slide.active');
    let bgImage = '--';
    let headingText = '--';
    let buttonText = '--';
    let buttonTransform = '--';
    
    if (activeSlide) {
      const wrapper = activeSlide.querySelector('.hero-content-wrapper');
      const heading = activeSlide.querySelector('.hero-heading');
      const button = activeSlide.querySelector('.hero-cta-button span');
      
      if (wrapper) {
        const currentView = wrapper.getAttribute('data-current-view');
        const bgStyle = wrapper.style.backgroundImage;
        bgImage = currentView || 'not set';
        if (bgStyle) {
          bgImage += bgStyle.includes('800') ? ' (tablet img)' : ' (desktop img)';
        }
      }
      
      if (heading) {
        const text = heading.textContent.trim();
        headingText = text.includes('kotów') ? 'TABLET TEXT' : 'DESKTOP TEXT';
      }
      
      if (button) {
        buttonText = button.textContent.trim();
        buttonTransform = window.getComputedStyle(button).textTransform;
        buttonText = buttonTransform === 'uppercase' ? 'UPPERCASE' : 'lowercase';
      }
    }
    
    // Update panel
    const panel = document.getElementById('hero-debug-panel');
    if (panel) {
      document.getElementById('debug-viewport').innerHTML = 
        `Viewport: <strong style="color: #4CAF50">${viewport}px</strong>`;
      
      document.getElementById('debug-mode').innerHTML = 
        `Mode: <strong style="color: ${modeMatch ? '#4CAF50' : '#f44336'}">${actualMode}</strong> (expected: ${expectedMode})`;
      
      document.getElementById('debug-breakpoint').innerHTML = 
        `Breakpoint: <strong>${viewport <= 800 ? '≤800px (tablet)' : '>800px (desktop)'}</strong>`;
      
      document.getElementById('debug-css').innerHTML = 
        `CSS Match: Desktop=${cssDesktop}, Tablet=${cssTablet}`;
      
      document.getElementById('debug-js').innerHTML = 
        `JS State: data-hero-view="${heroView}", data-viewport="${viewportData}"`;
      
      document.getElementById('debug-bg').innerHTML = 
        `Background: <strong style="color: ${bgImage.includes('tablet') ? '#FF9800' : '#4CAF50'}">${bgImage}</strong>`;
      
      document.getElementById('debug-heading').innerHTML = 
        `Heading: <strong style="color: ${headingText === 'TABLET TEXT' ? '#FF9800' : '#4CAF50'}">${headingText}</strong>`;
      
      document.getElementById('debug-button').innerHTML = 
        `Button: <strong style="color: ${buttonText === 'lowercase' ? '#FF9800' : '#4CAF50'}">${buttonText}</strong> (${buttonTransform})`;
      
      // Add warning if mismatch
      if (!modeMatch) {
        panel.style.borderColor = '#f44336';
        panel.style.borderWidth = '2px';
        panel.style.borderStyle = 'solid';
      } else {
        panel.style.border = 'none';
      }
    }
  }
  
  // Test specific breakpoint
  window.testHeroBreakpoint = function(width) {
    const testWindow = window.open(window.location.href, '_blank', 
      `width=${width},height=900,left=100,top=100`);
    
    if (testWindow) {
      setTimeout(() => {
        testWindow.resizeTo(width, 900);
      }, 100);
    }
  };
  
  // Initialize
  function init() {
    createDebugPanel();
    updateDebugInfo();
    
    // Update on resize
    window.addEventListener('resize', updateDebugInfo);
    
    // Update periodically to catch delayed changes
    setInterval(updateDebugInfo, 1000);
    
    console.log('[Hero Debug] Panel initialized. Monitoring breakpoints...');
  }
  
  // Start when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();