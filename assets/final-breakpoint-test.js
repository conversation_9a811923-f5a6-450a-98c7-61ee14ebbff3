/**
 * Final Comprehensive Breakpoint Test
 * Tests that images switch at EXACTLY the right breakpoints
 */

(function() {
  'use strict';
  
  const viewport = window.innerWidth;
  const cards = document.querySelectorAll('.product-card');
  
  // Define expected behavior
  const getExpectedMode = (width) => {
    if (width > 800) return 'desktop';
    if (width >= 481) return 'tablet';
    return 'mobile';
  };
  
  // Create status display
  const createStatusDisplay = () => {
    const display = document.createElement('div');
    display.id = 'breakpoint-status';
    display.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border: 3px solid #333;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      z-index: 99999;
      font-family: monospace;
      min-width: 400px;
      max-width: 600px;
    `;
    return display;
  };
  
  // Remove existing display if any
  const existing = document.getElementById('breakpoint-status');
  if (existing) existing.remove();
  
  const display = createStatusDisplay();
  
  // Build status HTML
  let html = `
    <h2 style="margin: 0 0 20px 0; color: #333;">🔍 Breakpoint Test Results</h2>
    <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
      <div style="font-size: 18px; margin-bottom: 10px;">
        <strong>Current Viewport:</strong> ${viewport}px
      </div>
      <div style="font-size: 16px; color: #666;">
        <strong>Expected Mode:</strong> <span style="color: #4CAF50; font-weight: bold;">
          ${getExpectedMode(viewport).toUpperCase()}
        </span>
      </div>
    </div>
  `;
  
  // Critical range warning
  if (viewport >= 769 && viewport <= 800) {
    html += `
      <div style="background: #FFF3E0; border: 2px solid #FF9800; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
        <div style="color: #E65100; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
          ⚠️ CRITICAL RANGE DETECTED (769-800px)
        </div>
        <div style="color: #F57C00;">
          This is the problematic range. ALL cards MUST show TABLET images.
        </div>
      </div>
    `;
  }
  
  // Test each card
  html += '<div style="margin-bottom: 20px;"><strong>Card Status:</strong></div>';
  
  let allCorrect = true;
  const expectedMode = getExpectedMode(viewport);
  
  cards.forEach((card, index) => {
    const cardName = card.classList.contains('delight-card') ? 'DELIGHT' :
                    card.classList.contains('help-card') ? 'HELP' :
                    card.classList.contains('rescue-card') ? 'RESCUE' : 'Unknown';
    
    const currentMode = card.getAttribute('data-current-mode') || 
                       card.getAttribute('data-active-mode') || 
                       'not set';
    
    const tabletBg = card.getAttribute('data-tablet-bg');
    const computedBg = window.getComputedStyle(card).backgroundImage;
    
    const isCorrect = currentMode === expectedMode;
    
    // Special check for tablet mode
    let tabletImageCorrect = true;
    if (expectedMode === 'tablet' && tabletBg) {
      const tabletImageName = tabletBg.split('/').pop().split('?')[0];
      tabletImageCorrect = computedBg.includes(tabletImageName) || computedBg.includes('600x');
    }
    
    const cardCorrect = isCorrect && tabletImageCorrect;
    if (!cardCorrect) allCorrect = false;
    
    const statusIcon = cardCorrect ? '✅' : '❌';
    const statusColor = cardCorrect ? '#4CAF50' : '#F44336';
    
    html += `
      <div style="padding: 10px; background: #fafafa; border-left: 4px solid ${statusColor}; margin-bottom: 10px; border-radius: 4px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span><strong>${statusIcon} Card ${index + 1}:</strong> ${cardName}</span>
          <span style="color: ${statusColor}; font-weight: bold;">
            ${currentMode.toUpperCase()}
          </span>
        </div>
        ${!cardCorrect ? `
          <div style="color: #F44336; font-size: 12px; margin-top: 5px;">
            Should be: ${expectedMode} | ${!tabletImageCorrect ? 'Wrong image displayed!' : 'Wrong mode set!'}
          </div>
        ` : ''}
        ${expectedMode === 'tablet' && !tabletBg ? `
          <div style="color: #FF9800; font-size: 12px; margin-top: 5px;">
            ⚠️ No tablet image uploaded (using fallback)
          </div>
        ` : ''}
      </div>
    `;
  });
  
  // Overall result
  const resultColor = allCorrect ? '#4CAF50' : '#F44336';
  const resultIcon = allCorrect ? '✅' : '❌';
  const resultText = allCorrect ? 'ALL TESTS PASSED!' : 'SOME TESTS FAILED';
  
  html += `
    <div style="margin-top: 20px; padding: 15px; background: ${resultColor}; color: white; border-radius: 8px; text-align: center;">
      <div style="font-size: 20px; font-weight: bold;">
        ${resultIcon} ${resultText}
      </div>
      ${!allCorrect && viewport >= 769 && viewport <= 800 ? `
        <div style="margin-top: 10px; font-size: 14px;">
          The 769-800px range is still showing incorrect images.
          Please check the console for detailed debugging info.
        </div>
      ` : ''}
    </div>
  `;
  
  // Breakpoint reference
  html += `
    <div style="margin-top: 20px; padding: 15px; background: #E3F2FD; border-radius: 8px;">
      <div style="font-weight: bold; margin-bottom: 10px; color: #1976D2;">
        📏 Breakpoint Reference:
      </div>
      <div style="font-size: 14px; line-height: 1.6;">
        <div ${viewport > 800 ? 'style="font-weight: bold; color: #1976D2;"' : ''}>
          • <strong>Desktop:</strong> 801px and above (>800px)
        </div>
        <div ${viewport >= 481 && viewport <= 800 ? 'style="font-weight: bold; color: #1976D2;"' : ''}>
          • <strong>Tablet:</strong> 481px to 800px (inclusive)
        </div>
        <div ${viewport <= 480 ? 'style="font-weight: bold; color: #1976D2;"' : ''}>
          • <strong>Mobile:</strong> 480px and below (≤480px)
        </div>
      </div>
    </div>
  `;
  
  // Add close button
  html += `
    <button onclick="document.getElementById('breakpoint-status').remove()" 
            style="margin-top: 20px; padding: 10px 20px; background: #333; color: white; 
                   border: none; border-radius: 6px; cursor: pointer; font-size: 14px; width: 100%;">
      Close Test Results
    </button>
  `;
  
  display.innerHTML = html;
  document.body.appendChild(display);
  
  // Log detailed info to console
  console.log('');
  console.log('='.repeat(60));
  console.log('📊 DETAILED BREAKPOINT TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`Viewport: ${viewport}px | Expected: ${expectedMode}`);
  console.log('');
  
  if (!allCorrect) {
    console.error('❌ TEST FAILED - Some cards showing wrong images/modes');
    console.log('');
    console.log('Debugging Information:');
    cards.forEach((card, index) => {
      const currentMode = card.getAttribute('data-current-mode') || 
                         card.getAttribute('data-active-mode') || 
                         'not set';
      if (currentMode !== expectedMode) {
        console.log(`Card ${index + 1}: Mode is "${currentMode}" but should be "${expectedMode}"`);
      }
    });
  } else {
    console.log('✅ SUCCESS - All cards showing correct images and modes!');
  }
  
  console.log('='.repeat(60));
  
  // Auto-close after 30 seconds
  setTimeout(() => {
    const statusDisplay = document.getElementById('breakpoint-status');
    if (statusDisplay) statusDisplay.remove();
  }, 30000);
  
})();