/**
 * Strict Test for 769-800px Range
 * This range MUST show TABLET images, not desktop
 */

(function() {
  'use strict';
  
  const viewport = window.innerWidth;
  
  console.log('');
  console.log('='.repeat(60));
  console.log('🔍 STRICT 769-800px RANGE TEST');
  console.log('='.repeat(60));
  console.log(`Current viewport: ${viewport}px`);
  console.log('');
  
  // Test if we're in the critical range
  const inCriticalRange = viewport >= 769 && viewport <= 800;
  
  if (inCriticalRange) {
    console.log('🎯 YOU ARE IN THE CRITICAL RANGE (769-800px)');
    console.log('✅ EXPECTED: All cards should show TABLET images');
    console.log('❌ BUG: If you see DESKTOP images, the fix is not working');
    console.log('');
  } else {
    console.log(`📍 Current viewport (${viewport}px) is outside critical range`);
    console.log('ℹ️  Resize browser to 769-800px width to test');
    console.log('');
  }
  
  // Check all product cards
  const cards = document.querySelectorAll('.product-card');
  
  if (cards.length === 0) {
    console.error('❌ No product cards found! Make sure you\'re on the right page.');
    return;
  }
  
  console.log(`Found ${cards.length} product cards:`);
  console.log('-'.repeat(60));
  
  let allCorrect = true;
  
  cards.forEach((card, index) => {
    const cardName = card.classList.contains('delight-card') ? 'DELIGHT' :
                    card.classList.contains('help-card') ? 'HELP' :
                    card.classList.contains('rescue-card') ? 'RESCUE' : 'Unknown';
    
    const desktopBg = card.getAttribute('data-desktop-bg');
    const tabletBg = card.getAttribute('data-tablet-bg');
    const mobileBg = card.getAttribute('data-mobile-bg');
    const currentMode = card.getAttribute('data-current-mode') || card.getAttribute('data-active-mode');
    
    // Get the actual background being displayed
    const computedStyle = window.getComputedStyle(card);
    const currentBg = computedStyle.backgroundImage;
    
    console.group(`📦 Card ${index + 1}: ${cardName}`);
    
    // Show what images are available
    console.log('Available images:');
    console.log(`  Desktop: ${desktopBg ? '✅ Set' : '❌ Not set'}`);
    console.log(`  Tablet:  ${tabletBg ? '✅ Set' : '⚠️  Not set (will fallback to desktop)'}`);
    console.log(`  Mobile:  ${mobileBg ? '✅ Set' : '❌ Not set'}`);
    
    // Check current state
    console.log('');
    console.log('Current state:');
    console.log(`  Mode attribute: ${currentMode || 'NOT SET'}`);
    console.log(`  Background: ${currentBg.substring(0, 50)}...`);
    
    // Determine what SHOULD be showing
    let expectedImage = '';
    let expectedMode = '';
    
    if (viewport >= 481 && viewport <= 800) {
      expectedMode = 'tablet';
      expectedImage = tabletBg || desktopBg;
    } else if (viewport > 800) {
      expectedMode = 'desktop';
      expectedImage = desktopBg;
    } else {
      expectedMode = 'mobile';
      expectedImage = mobileBg || tabletBg || desktopBg;
    }
    
    // Check if correct image is showing
    const isCorrect = currentMode === expectedMode;
    
    // Special check for 769-800px range
    if (inCriticalRange) {
      console.log('');
      console.log('🎯 CRITICAL RANGE CHECK (769-800px):');
      
      if (tabletBg) {
        // Check if the tablet image URL is in the current background
        const tabletImageName = tabletBg.split('/').pop().split('?')[0];
        const isTabletShowing = currentBg.includes(tabletImageName) || currentBg.includes('600x');
        
        if (isTabletShowing) {
          console.log('✅ CORRECT: Tablet image IS showing');
        } else {
          console.log('❌ BUG DETECTED: Desktop image showing instead of tablet!');
          console.log(`   Should show: ${tabletBg}`);
          console.log(`   But showing: ${currentBg}`);
          allCorrect = false;
        }
      } else {
        console.log('⚠️  No tablet image uploaded (using desktop as fallback)');
      }
      
      if (currentMode !== 'tablet') {
        console.log(`❌ Mode is "${currentMode}" but should be "tablet"`);
        allCorrect = false;
      }
    } else {
      // Check for other viewport widths
      if (isCorrect) {
        console.log(`✅ Correct mode: ${currentMode}`);
      } else {
        console.log(`⚠️  Mode mismatch: showing "${currentMode}", expected "${expectedMode}"`);
        allCorrect = false;
      }
    }
    
    console.groupEnd();
    console.log('');
  });
  
  console.log('='.repeat(60));
  if (inCriticalRange) {
    if (allCorrect) {
      console.log('✅ SUCCESS: All cards showing correct tablet images!');
    } else {
      console.log('❌ FAILURE: Some cards showing wrong images');
      console.log('');
      console.log('🔧 Troubleshooting:');
      console.log('1. Check if tablet images are uploaded in Shopify admin');
      console.log('2. Clear browser cache and reload');
      console.log('3. Check browser console for any errors');
      console.log('4. Verify the fix was properly saved');
    }
  } else {
    console.log(`ℹ️  Test at viewport ${viewport}px complete`);
    console.log('Resize to 769-800px width for critical range test');
  }
  console.log('='.repeat(60));
  
  // Add visual indicator
  const indicator = document.createElement('div');
  indicator.id = 'range-test-indicator';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 15px;
    background: ${inCriticalRange ? '#FF5722' : '#4CAF50'};
    color: white;
    border-radius: 8px;
    font-family: monospace;
    font-size: 14px;
    font-weight: bold;
    z-index: 99999;
    box-shadow: 0 4px 6px rgba(0,0,0,0.3);
  `;
  
  indicator.innerHTML = `
    <div>Viewport: ${viewport}px</div>
    <div style="margin-top: 5px;">
      ${inCriticalRange ? '🎯 CRITICAL RANGE (769-800)' : `Mode: ${viewport > 800 ? 'Desktop' : viewport > 480 ? 'Tablet' : 'Mobile'}`}
    </div>
    ${inCriticalRange && !allCorrect ? '<div style="margin-top: 5px; color: #FFEB3B;">⚠️ WRONG IMAGES!</div>' : ''}
  `;
  
  // Remove any existing indicator
  const existing = document.getElementById('range-test-indicator');
  if (existing) existing.remove();
  
  document.body.appendChild(indicator);
  
  // Auto-update on resize
  window.addEventListener('resize', function() {
    const newIndicator = document.getElementById('range-test-indicator');
    if (newIndicator) {
      newIndicator.textContent = `Viewport: ${window.innerWidth}px (reload to re-test)`;
      newIndicator.style.background = '#9E9E9E';
    }
  });
})();