/**
 * Enhanced Cart JavaScript
 * Handles quantity controls, cart updates, and AJAX functionality
 */

class EnhancedCart {
  constructor() {
    this.init();
  }

  init() {
    this.bindQuantityControls();
    this.bindAddToCartForms();
    this.bindRemoveButtons();
    this.updateCartSummary();
  }

  /**
   * Bind quantity control buttons
   */
  bindQuantityControls() {
    const quantityButtons = document.querySelectorAll('.quantity-button');
    
    quantityButtons.forEach(button => {
      button.addEventListener('click', this.handleQuantityChange.bind(this));
    });
  }

  /**
   * Handle quantity increase/decrease
   */
  async handleQuantityChange(event) {
    event.preventDefault();
    
    const button = event.currentTarget;
    const itemId = button.dataset.itemId;
    const action = button.dataset.action;
    const quantitySelector = button.closest('.quantity-selector');
    const quantityDisplay = quantitySelector.querySelector('.quantity-display');
    let currentQuantity = parseInt(quantityDisplay.textContent);
    
    // Calculate new quantity
    if (action === 'increase') {
      currentQuantity += 1;
    } else if (action === 'decrease' && currentQuantity > 1) {
      currentQuantity -= 1;
    } else {
      return; // Don't allow quantity to go below 1
    }

    // Disable buttons during update
    this.setQuantityButtonsState(quantitySelector, true);
    
    try {
      const response = await this.updateCartQuantity(itemId, currentQuantity);
      
      if (response.ok) {
        const updatedCart = await response.json();
        
        // Update the display
        quantityDisplay.textContent = currentQuantity;
        
        // Update line price
        const priceElement = button.closest('.cart-item').querySelector('.price-amount');
        const updatedItem = this.findCartItem(updatedCart.items, itemId);
        
        if (updatedItem && priceElement) {
          priceElement.textContent = this.formatMoney(updatedItem.final_line_price);
        }
        
        // Update cart summary
        this.updateCartTotals(updatedCart);
        
        // VetApp integration disabled
        // document.dispatchEvent(new CustomEvent('cart:updated'));
        // document.dispatchEvent(new CustomEvent('ajaxCart:updated'));
        
      } else {
        throw new Error('Failed to update cart');
      }
    } catch (error) {
      console.error('Error updating quantity:', error);
      this.showNotification('Wystąpił błąd podczas aktualizacji koszyka', 'error');
    } finally {
      // Re-enable buttons
      this.setQuantityButtonsState(quantitySelector, false);
    }
  }

  /**
   * Update cart quantity via AJAX
   */
  async updateCartQuantity(itemId, quantity) {
    return fetch('/cart/change.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      credentials: 'same-origin',
      body: JSON.stringify({
        id: itemId,
        quantity: quantity
      })
    });
  }

  /**
   * Find cart item by ID
   */
  findCartItem(items, itemId) {
    return items.find(item => item.id.toString() === itemId.toString());
  }

  /**
   * Set quantity buttons enabled/disabled state
   */
  setQuantityButtonsState(quantitySelector, disabled) {
    const buttons = quantitySelector.querySelectorAll('.quantity-button');
    buttons.forEach(button => {
      button.disabled = disabled;
      button.style.opacity = disabled ? '0.5' : '1';
    });
  }

  /**
   * Bind add to cart forms in recommendations
   */
  bindAddToCartForms() {
    const addToCartForms = document.querySelectorAll('.product-cart-tile__form');
    
    addToCartForms.forEach(form => {
      form.addEventListener('submit', this.handleAddToCart.bind(this));
    });
  }

  /**
   * Handle add to cart submissions
   */
  async handleAddToCart(event) {
    event.preventDefault();
    
    const form = event.currentTarget;
    const formData = new FormData(form);
    const button = form.querySelector('.product-cart-tile__add-btn');
    const originalText = button.querySelector('.product-cart-tile__add-text').textContent;
    
    // Disable button during request (no loading text change)
    button.disabled = true;
    
    try {
      const response = await fetch('/cart/add.js', {
        method: 'POST',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin',
        body: formData
      });
      
      if (response.ok) {
        const item = await response.json();
        
        // Show success message
        this.showNotification('Produkt dodany do koszyka!', 'success');
        
        // Update cart count if cart icon exists
        this.updateCartCount();

        // Success UI: switch to Figma-styled "Dodano" pill briefly
        button.classList.add('add-to-cart--success');
        const iconContainer = button.querySelector('svg');
        const successIconUrl = button.getAttribute('data-success-icon');
        if (iconContainer) {
          const iconSrc = successIconUrl || `/assets/3589a8fde2a91501906cf41d590682ce1538c36e.svg`;
          iconContainer.outerHTML = `<img src="${iconSrc}" alt="" class="add-to-cart-success-icon" width="28" height="28">`;
        }
        button.querySelector('.product-cart-tile__add-text').textContent = 'Dodano';
        setTimeout(() => {
          button.classList.remove('add-to-cart--success');
          const successIcon = button.querySelector('.add-to-cart-success-icon');
          if (successIcon) {
            successIcon.outerHTML = `
              <svg class="product-cart-tile__cart-icon" width="29" height="28" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M21.2269 24.0784H7.78688C6.54928 24.0784 5.54688 23.076 5.54688 21.8384V10.0784C5.54688 9.15108 6.29952 8.39844 7.22688 8.39844H21.7869C22.7142 8.39844 23.4669 9.15108 23.4669 10.0784V21.8384C23.4669 23.076 22.4645 24.0784 21.2269 24.0784Z" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.9808 12.0847C18.8262 12.0847 18.7008 12.2101 18.7019 12.3647C18.7019 12.5192 18.8273 12.6447 18.9819 12.6447C19.1364 12.6447 19.2619 12.5192 19.2619 12.3647C19.2619 12.2101 19.1364 12.0847 18.9808 12.0847" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10.0276 12.0847C9.87307 12.0847 9.74763 12.2101 9.74875 12.3647C9.74875 12.5192 9.87419 12.6447 10.0288 12.6447C10.1833 12.6447 10.3088 12.5192 10.3088 12.3647C10.3088 12.2101 10.1833 12.0847 10.0276 12.0847" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10.0312 8.39406V8.11406V8.11406C10.0312 5.79454 11.9117 3.91406 14.2313 3.91406H14.7913C17.1108 3.91406 18.9913 5.79454 18.9913 8.11406V8.11406V8.39406" stroke="#2D4F40" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>`;
          }
          button.querySelector('.product-cart-tile__add-text').textContent = originalText;
        }, 1500);
        
        // VetApp integration disabled
        // document.dispatchEvent(new CustomEvent('cart:updated'));
        // document.dispatchEvent(new CustomEvent('ajaxCart:updated'));
        
        // NO RELOAD - Dynamic updates only
        // Removed window.location.reload() to prevent hot reload loop
        
      } else {
        throw new Error('Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      this.showNotification('Wystąpił błąd podczas dodawania produktu', 'error');
    } finally {
      // Re-enable button
      button.disabled = false;
      if (!button.classList.contains('add-to-cart--success')) {
        button.querySelector('.product-cart-tile__add-text').textContent = originalText;
      }
    }
  }

  /**
   * Bind remove buttons
   */
  bindRemoveButtons() {
    const removeButtons = document.querySelectorAll('.remove-button');
    
    removeButtons.forEach(button => {
      button.addEventListener('click', this.handleRemoveItem.bind(this));
    });
  }

  /**
   * Handle remove item with confirmation
   */
  handleRemoveItem(event) {
    event.preventDefault();
    
    const link = event.currentTarget;
    const productName = link.closest('.cart-item').querySelector('.product-name').textContent;
    
    if (confirm(`Czy na pewno chcesz usunąć "${productName}" z koszyka?`)) {
      window.location.href = link.href;
    }
  }

  /**
   * Update cart summary totals
   */
  updateCartTotals(cart) {
    // Update subtotal
    const subtotalElement = document.querySelector('.summary-row .summary-value');
    if (subtotalElement) {
      subtotalElement.textContent = this.formatMoney(cart.total_price);
    }
    
    // Update total with shipping
    const totalElement = document.querySelector('.total-section .summary-value');
    if (totalElement) {
      const shippingCost = this.getShippingCost();
      const total = cart.total_price + shippingCost;
      totalElement.textContent = this.formatMoney(total);
    }
    
    // Update free shipping threshold
    this.updateFreeShippingThreshold(cart.total_price);
  }

  /**
   * Get shipping cost from settings or default
   */
  getShippingCost() {
    const shippingElement = document.querySelector('[data-shipping-cost]');
    return shippingElement ? parseInt(shippingElement.dataset.shippingCost) : 1500; // 15.00 zł in cents
  }

  /**
   * Update free shipping threshold display
   */
  updateFreeShippingThreshold(cartTotal) {
    const freeShippingThreshold = 10000; // 100.00 zł in cents
    const remaining = Math.max(0, freeShippingThreshold - cartTotal);
    
    const thresholdElement = document.querySelector('.delivery-amount');
    if (thresholdElement) {
      if (remaining > 0) {
        thresholdElement.textContent = this.formatMoney(remaining);
      } else {
        const deliveryNotice = document.querySelector('.delivery-notice');
        if (deliveryNotice) {
          deliveryNotice.innerHTML = `
            <div class="delivery-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 6L9 17L4 12" stroke="#4C7D67" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="delivery-text-group">
              <span class="delivery-text">Gratulacje! Masz darmową dostawę</span>
            </div>
          `;
        }
      }
    }
  }

  /**
   * Update cart count in header
   */
  async updateCartCount() {
    try {
      const response = await fetch('/cart.js');
      const cart = await response.json();
      
      const cartCountElements = document.querySelectorAll('[data-cart-count]');
      cartCountElements.forEach(element => {
        element.textContent = cart.item_count;
      });
    } catch (error) {
      console.error('Error updating cart count:', error);
    }
  }

  /**
   * Update cart summary on page load
   */
  updateCartSummary() {
    // This would typically fetch the current cart state
    // and update all relevant elements
    const cartData = this.getCartDataFromPage();
    if (cartData) {
      this.updateFreeShippingThreshold(cartData.total_price);
    }
  }

  /**
   * Get cart data from the current page
   */
  getCartDataFromPage() {
    // Extract cart data from the rendered page
    const cartElement = document.querySelector('[data-cart-total]');
    if (cartElement) {
      return {
        total_price: parseInt(cartElement.dataset.cartTotal)
      };
    }
    return null;
  }

  /**
   * Format money amount
   */
  formatMoney(cents) {
    const amount = cents / 100;
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN'
    }).format(amount).replace('PLN', 'zł');
  }

  /**
   * Show notification to user
   */
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `enhanced-cart-notification enhanced-cart-notification--${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 24px',
      borderRadius: '8px',
      color: '#fff',
      backgroundColor: type === 'error' ? '#dc3545' : '#28a745',
      zIndex: '9999',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateX(100%)',
      transition: 'transform 0.3s ease'
    });
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  new EnhancedCart();
});

// Expose for external use
window.EnhancedCart = EnhancedCart;
