/**
 * Enhanced <PERSON>t Checkout Styles
 * Matches the design system provided in component_design
 */

:root {
  --enhanced-cart-primary-700: #2D4F40;
  --enhanced-cart-beige-100: #F0F0E4;
  --enhanced-cart-beige-50: #F9F9F4;
  --enhanced-cart-primary-500: #4C7D67;
  --enhanced-cart-primary-0: #FFF;
  --enhanced-cart-font-family: 'Jost', -apple-system, 'Roboto', 'Helvetica', sans-serif;
}

/* Enhanced Cart Container */
.enhanced-cart {
  font-family: var(--enhanced-cart-font-family);
}

.enhanced-cart-container {
  display: flex;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  align-items: flex-start;
  gap: 64px;
  padding: 20px;
}

.cart-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 40px;
  flex: 1 0 0;
}

.cart-header {
  color: var(--enhanced-cart-primary-700);
  font-size: 32px;
  font-weight: 500;
  line-height: 110%;
  letter-spacing: -0.96px;
  margin: 0 0 32px 0;
}

/* Cart Items */
.cart-items-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 12px;
  border: 1px solid var(--enhanced-cart-beige-100);
  overflow: hidden;
}

.cart-item {
  display: flex;
  padding: 20px;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
  border-bottom: 1px solid var(--enhanced-cart-beige-100);
}

.cart-item:last-child {
  border-bottom: none;
}

/* Product Images */
.product-image-wrapper {
  display: flex;
  width: 140px;
  height: 140px;
  justify-content: center;
  align-items: center;
  border-radius: 7px;
  background: var(--enhanced-cart-beige-50);
  overflow: hidden;
  flex-shrink: 0;
}

.product-image {
  width: 140px;
  height: 140px;
  object-fit: cover;
  border-radius: 7px;
}

/* Product Details */
.product-details {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  flex: 1 0 0;
  align-self: stretch;
  min-height: 140px;
}

.product-info-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
}

.product-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 11px;
  flex: 1 0 0;
}

.product-name {
  color: var(--enhanced-cart-primary-700);
  font-size: 20px;
  font-weight: 500;
  line-height: 110%;
  letter-spacing: -0.6px;
  margin: 0;
}

.product-description {
  color: var(--enhanced-cart-primary-700);
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  letter-spacing: -0.24px;
  margin: 0;
}

/* Remove Button */
.remove-button {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  text-decoration: none;
  cursor: pointer;
  flex-shrink: 0;
}

.remove-text {
  color: var(--enhanced-cart-primary-500);
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  letter-spacing: -0.24px;
}

.remove-icon {
  width: 20px;
  height: 20px;
}

/* Price and Quantity */
.price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  margin-top: auto;
}

.quantity-selector {
  display: flex;
  height: 36px;
  justify-content: center;
  align-items: center;
  border-radius: 44px;
  border: 1px solid var(--enhanced-cart-beige-100);
  background: var(--enhanced-cart-primary-0);
}

.quantity-button {
  display: flex;
  width: 40px;
  height: 36px;
  justify-content: center;
  align-items: center;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.quantity-button:hover {
  background-color: var(--enhanced-cart-beige-50);
}

.quantity-divider {
  width: 1px;
  align-self: stretch;
  background: var(--enhanced-cart-beige-100);
}

.quantity-display {
  display: flex;
  width: 40px;
  padding: 10px 0;
  justify-content: center;
  align-items: center;
  color: var(--enhanced-cart-primary-700);
  font-size: 14px;
  font-weight: 500;
  line-height: 140%;
  letter-spacing: 1.4px;
  text-transform: uppercase;
}

.price-amount {
  color: var(--enhanced-cart-primary-700);
  font-size: 18px;
  font-weight: 500;
  line-height: 110%;
  letter-spacing: -0.54px;
}

/* Cart Summary */
.cart-summary {
  display: flex;
  width: 378px;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
}

.summary-row {
  display: flex;
  padding: 8px 0;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.summary-label {
  color: var(--enhanced-cart-primary-700);
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  opacity: 0.8;
}

.summary-value {
  color: var(--enhanced-cart-primary-700);
  font-size: 16px;
  font-weight: 500;
  line-height: 140%;
}

.summary-divider {
  height: 1px;
  align-self: stretch;
  background: var(--enhanced-cart-beige-100);
  margin: 8px 0;
}

.total-section {
  display: flex;
  padding: 12px 0;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.total-label-group {
  display: flex;
  width: 210px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.total-label {
  color: var(--enhanced-cart-primary-700);
  font-size: 16px;
  font-weight: 700;
  line-height: 140%;
}

.vat-label {
  color: var(--enhanced-cart-primary-700);
  font-size: 16px;
  font-weight: 400;
  line-height: 140%;
  opacity: 0.8;
}

/* Checkout Button */
.checkout-section {
  display: flex;
  padding: 12px 0;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
  border-top: 1px solid var(--enhanced-cart-beige-100);
}

.checkout-button {
  display: flex;
  height: 44px;
  padding: 16px 24px;
  justify-content: center;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  border-radius: 44px;
  background: var(--enhanced-cart-primary-700);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.checkout-button:hover {
  background: #1e3529;
}

.checkout-button-text {
  color: var(--enhanced-cart-primary-0);
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.64px;
  text-transform: uppercase;
}

/* Delivery and Payment Info */
.delivery-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
}

.delivery-notice {
  display: flex;
  padding: 12px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 12px;
  background: var(--enhanced-cart-beige-50);
}

.delivery-icon {
  display: flex;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.delivery-text-group {
  display: flex;
  padding-right: 4px;
  align-items: center;
  gap: 12px;
  border-radius: 24px;
  flex: 1;
}

.delivery-text {
  color: var(--enhanced-cart-primary-700);
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  opacity: 0.8;
}

.delivery-amount {
  color: var(--enhanced-cart-primary-700);
  font-size: 14px;
  font-weight: 500;
  line-height: 140%;
}

.payment-info {
  display: flex;
  padding: 12px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 12px;
  border: 1px solid var(--enhanced-cart-beige-100);
}

.payment-label {
  color: var(--enhanced-cart-primary-700);
  font-size: 16px;
  font-weight: 500;
  line-height: 140%;
  letter-spacing: -0.32px;
}

.payment-methods {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.payment-method {
  display: flex;
  height: 32px;
  padding: 0 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  background: var(--enhanced-cart-beige-50);
}

.payment-icon {
  max-width: 40px;
  max-height: 22px;
  object-fit: contain;
}

/* Discount Notice */
.discount-notice {
  display: flex;
  padding: 16px 0;
  align-items: center;
  gap: 4px;
  align-self: stretch;
}

.discount-text-container {
  display: flex;
  padding: 16px 0;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  border-top: 1px solid var(--enhanced-cart-beige-100);
}

.discount-text {
  color: var(--enhanced-cart-primary-700);
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
}

/* Recommendations Section */
.recommendations-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
}

.recommendations-title {
  color: var(--enhanced-cart-primary-700);
  font-size: 24px;
  font-weight: 500;
  line-height: 110%;
  letter-spacing: -0.72px;
  margin: 0;
}

.recommendations-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  gap: 20px;
}

.cart-divider {
  height: 1px;
  align-self: stretch;
  background: var(--enhanced-cart-beige-100);
  margin: 32px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-cart-container {
    flex-direction: column;
    gap: 32px;
    padding: 16px;
  }

  .cart-summary {
    width: 100%;
  }

  .cart-header {
    font-size: 28px;
  }

  .cart-item {
    flex-direction: column;
    gap: 16px;
  }

  .product-info-header {
    flex-direction: column;
    gap: 12px;
  }

  .price-info {
    justify-content: center;
    gap: 20px;
  }

  .recommendations-grid {
    flex-direction: column;
    gap: 16px;
  }

  .product-image-wrapper {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .enhanced-cart-container {
    padding: 12px;
  }

  .cart-header {
    font-size: 24px;
  }

  .product-name {
    font-size: 18px;
  }

  .cart-item {
    padding: 16px;
  }
}
