// Mobile Images Handler for AYLA Care Benefits Section
document.addEventListener('DOMContentLoaded', function() {
console.log('🚀 Mobile Images Handler loaded');
console.log('🔍 Initial viewport check:', {
    innerWidth: getViewportWidth(),
    innerHeight: window.innerHeight,
    clientWidth: document.documentElement.clientWidth,
    clientHeight: document.documentElement.clientHeight
  });
  
  function getViewportWidth() {
    // Use the actual viewport width - prefer documentElement.clientWidth as it's more accurate
    const width = document.documentElement.clientWidth || window.innerWidth || 0;
    console.log('🔍 getViewportWidth() called:', {
      documentElementClientWidth: document.documentElement.clientWidth,
      windowInnerWidth: window.innerWidth,
      calculatedWidth: width
    });
    return width;
  }
  
  function switchToMobileImages() {
    const cards = document.querySelectorAll('.benefit-card');
    const viewportWidth = getViewportWidth();
console.log('📱 Switching to mobile images, found cards:', cards.length);
console.log('📱 Current viewport width:', viewportWidth);
console.log('📱 getViewportWidth():', getViewportWidth());
console.log('📱 document.documentElement.clientWidth:', document.documentElement.clientWidth);
    
    cards.forEach((card, index) => {
      const backgroundElement = card.querySelector('.card-background-image') || card;
      const mobileBg = backgroundElement.getAttribute('data-mobile-bg');
      const tabletBg = backgroundElement.getAttribute('data-tablet-bg');
      const cardType = card.classList.contains('orange-card') ? 'Orange' : 
                      card.classList.contains('green-dark-card') ? 'Green Dark' :
                      card.classList.contains('beige-card') ? 'Beige' :
                      card.classList.contains('green-medium-card') ? 'Green Medium' : 'Other';
      
console.log(`📱 Card ${index + 1} (${cardType}):`, {
        hasMobileBg: !!mobileBg,
        hasTabletBg: !!tabletBg,
        mobileBg: mobileBg,
        tabletBg: tabletBg,
        currentBg: backgroundElement.style.backgroundImage,
        isGreenDark: card.classList.contains('green-dark-card'),
        backgroundElement: backgroundElement,
        isBackgroundElementCard: backgroundElement === card
      });
      
      if (getViewportWidth() <= 500) {
        // Mobile view - try mobile image first, fallback to tablet, then desktop
        console.log(`📱 Mobile view detected for ${cardType} card - Mobile BG: ${mobileBg}, Tablet BG: ${tabletBg}`);
        if (mobileBg) {
          // Use mobile background
          if (backgroundElement === card) {
            // For green-dark-card that has background directly on the card
            const bgUrl = mobileBg.startsWith('url(') ? mobileBg : `url('${mobileBg}')`;
            card.style.backgroundImage = bgUrl;
            card.style.backgroundSize = 'contain';
            card.style.backgroundPosition = 'center';
            card.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied mobile bg to ${cardType} card (green-dark): ${mobileBg}`);
          } else {
            // For cards with .card-background-image
            const bgUrl = mobileBg.startsWith('url(') ? mobileBg : `url('${mobileBg}')`;
            backgroundElement.style.backgroundImage = bgUrl;
            backgroundElement.style.backgroundSize = 'contain';
            backgroundElement.style.backgroundPosition = 'center';
            backgroundElement.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied mobile bg to ${cardType} card (background-image): ${mobileBg}`);
          }
        } else if (tabletBg) {
          // Fallback to tablet background
          if (backgroundElement === card) {
            const bgUrl = tabletBg.startsWith('url(') ? tabletBg : `url('${tabletBg}')`;
            card.style.backgroundImage = bgUrl;
            card.style.backgroundSize = 'contain';
            card.style.backgroundPosition = 'center';
            card.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied tablet bg to ${cardType} card (mobile fallback): ${tabletBg}`);
          } else {
            const bgUrl = tabletBg.startsWith('url(') ? tabletBg : `url('${tabletBg}')`;
            backgroundElement.style.backgroundImage = bgUrl;
            backgroundElement.style.backgroundSize = 'contain';
            backgroundElement.style.backgroundPosition = 'center';
            backgroundElement.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied tablet bg to ${cardType} card (mobile fallback): ${tabletBg}`);
          }
        } else {
console.log(`⚠️ No mobile/tablet image for ${cardType} card, keeping desktop image`);
          // If no mobile or tablet image, keep desktop image but ensure it's visible
          if (backgroundElement === card) {
            // For green-dark-card, ensure it has a background
            if (!card.style.backgroundImage || card.style.backgroundImage === 'none') {
              card.style.backgroundImage = 'var(--primary-p700)';
            }
          }
        }
      } else if (tabletBg && getViewportWidth() <= 1024 && getViewportWidth() > 500) {
        // Use tablet background
        if (backgroundElement === card) {
          // For green-dark-card that has background directly on the card
          card.style.backgroundImage = `url('${tabletBg}')`;
          card.style.backgroundSize = 'contain';
          card.style.backgroundPosition = 'center';
          card.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied tablet bg to ${cardType} card (green-dark): ${tabletBg}`);
        } else {
          // For cards with .card-background-image
          backgroundElement.style.backgroundImage = `url('${tabletBg}')`;
          backgroundElement.style.backgroundSize = 'contain';
          backgroundElement.style.backgroundPosition = 'center';
          backgroundElement.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied tablet bg to ${cardType} card (background-image): ${tabletBg}`);
        }
      } else if (getViewportWidth() <= 1024) {
console.log(`⚠️ No tablet image for ${cardType} card, keeping desktop image`);
        // If no tablet image, keep desktop image but ensure it's visible
        if (backgroundElement === card) {
          // For green-dark-card, ensure it has a background
          if (!card.style.backgroundImage || card.style.backgroundImage === 'none') {
            card.style.backgroundImage = 'var(--primary-p700)';
          }
        }
      }
    });
  }
  
  function switchToTabletImages() {
    const cards = document.querySelectorAll('.benefit-card');
    const viewportWidth = getViewportWidth();
console.log('📱 Switching to tablet images, found cards:', cards.length);
console.log('📱 Current viewport width:', viewportWidth);
    
    cards.forEach((card, index) => {
      const backgroundElement = card.querySelector('.card-background-image') || card;
      const tabletBg = backgroundElement.getAttribute('data-tablet-bg');
      const cardType = card.classList.contains('orange-card') ? 'Orange' : 
                      card.classList.contains('green-dark-card') ? 'Green Dark' :
                      card.classList.contains('beige-card') ? 'Beige' :
                      card.classList.contains('green-medium-card') ? 'Green Medium' : 'Other';
      
      if (tabletBg && getViewportWidth() <= 1024 && getViewportWidth() > 500) {
        // Use tablet background
        if (backgroundElement === card) {
          // For green-dark-card that has background directly on the card
          card.style.backgroundImage = `url('${tabletBg}')`;
          card.style.backgroundSize = 'contain';
          card.style.backgroundPosition = 'center';
          card.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied tablet bg to ${cardType} card (green-dark): ${tabletBg}`);
        } else {
          // For cards with .card-background-image
          backgroundElement.style.backgroundImage = `url('${tabletBg}')`;
          backgroundElement.style.backgroundSize = 'contain';
          backgroundElement.style.backgroundPosition = 'center';
          backgroundElement.style.backgroundRepeat = 'no-repeat';
console.log(`✅ Applied tablet bg to ${cardType} card (background-image): ${tabletBg}`);
        }
      } else if (getViewportWidth() <= 1024) {
console.log(`⚠️ No tablet image for ${cardType} card, keeping desktop image`);
        // If no tablet image, keep desktop image but ensure it's visible
        if (backgroundElement === card) {
          // For green-dark-card, ensure it has a background
          if (!card.style.backgroundImage || card.style.backgroundImage === 'none') {
            card.style.backgroundImage = 'var(--primary-p700)';
          }
        }
      }
    });
  }
  
  function switchToDesktopImages() {
    const cards = document.querySelectorAll('.benefit-card');
    const viewportWidth = getViewportWidth();
console.log('💻 Switching to desktop images, found cards:', cards.length);
console.log('💻 Current viewport width:', viewportWidth);
    
    cards.forEach((card, index) => {
      const backgroundElement = card.querySelector('.card-background-image') || card;
      const cardType = card.classList.contains('orange-card') ? 'Orange' : 
                      card.classList.contains('green-dark-card') ? 'Green Dark' :
                      card.classList.contains('beige-card') ? 'Beige' :
                      card.classList.contains('green-medium-card') ? 'Green Medium' : 'Other';
      
      if (getViewportWidth() > 1024) {
        // Use desktop background from data-desktop-bg attribute
        if (backgroundElement === card) {
          // For green-dark-card - use the desktop image
          const desktopBg = card.getAttribute('data-desktop-bg');
          if (desktopBg) {
            // Check if it's already a URL or needs to be wrapped
            const bgUrl = desktopBg.startsWith('url(') ? desktopBg : `url('${desktopBg}')`;
            card.style.backgroundImage = bgUrl;
            card.style.backgroundSize = 'contain';
            card.style.backgroundPosition = 'center';
            card.style.backgroundRepeat = 'no-repeat';
console.log(`💻 Applied desktop bg to ${cardType} card (green-dark): ${desktopBg}`);
          }
        } else {
          // For cards with .card-background-image
          const desktopBg = backgroundElement.getAttribute('data-desktop-bg');
          if (desktopBg) {
            // Check if it's already a URL or needs to be wrapped
            const bgUrl = desktopBg.startsWith('url(') ? desktopBg : `url('${desktopBg}')`;
            backgroundElement.style.backgroundImage = bgUrl;
            backgroundElement.style.backgroundSize = 'contain';
            backgroundElement.style.backgroundPosition = 'center';
            backgroundElement.style.backgroundRepeat = 'no-repeat';
console.log(`💻 Applied desktop bg to ${cardType} card (background-image): ${desktopBg}`);
          } else {
console.log(`⚠️ No desktop bg found for ${cardType} card`);
          }
        }
      }
    });
  }
  
  // Store original desktop backgrounds - use data-desktop-bg if available, otherwise use current background
  document.querySelectorAll('.benefit-card').forEach(card => {
    const backgroundElement = card.querySelector('.card-background-image') || card;
    if (backgroundElement === card) {
      // For green-dark-card - use data-desktop-bg if available
      const desktopBg = card.getAttribute('data-desktop-bg');
      if (!desktopBg) {
        card.setAttribute('data-desktop-bg', card.style.backgroundImage || 'var(--primary-p700)');
      }
    } else {
      // For cards with .card-background-image - use data-desktop-bg if available
      const desktopBg = backgroundElement.getAttribute('data-desktop-bg');
      if (!desktopBg) {
        const currentBg = backgroundElement.style.backgroundImage;
        if (currentBg && currentBg !== 'none') {
          backgroundElement.setAttribute('data-desktop-bg', currentBg);
        }
      }
    }
  });
  
  // Initial check with delay to ensure DOM is ready
  setTimeout(function() {
    const viewportWidth = getViewportWidth();
console.log('🚀 Initial image check - Width:', viewportWidth);
console.log('🚀 Window dimensions:', {
      windowInnerWidth: window.innerWidth,
      documentElementClientWidth: document.documentElement.clientWidth,
      screenWidth: screen.width
    });
    
    // Force desktop mode for testing if viewport is wide enough
    if (viewportWidth > 1200) {
console.log('💻 Force desktop mode - Width > 1200px');
      switchToDesktopImages();
    } else if (viewportWidth <= 500) {
console.log('📱 Initial: Switching to mobile images');
      switchToMobileImages();
    } else if (viewportWidth <= 1024) {
console.log('📱 Initial: Switching to tablet images');
      switchToTabletImages();
    } else {
console.log('💻 Initial: Switching to desktop images');
      switchToDesktopImages();
    }
  }, 100);
  
  // Handle window resize
  let resizeTimeout;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
      const viewportWidth = getViewportWidth();
console.log('🔄 Resize detected - Viewport Width:', viewportWidth);
console.log('🔄 window.innerWidth:', window.innerWidth);
console.log('🔄 document.documentElement.clientWidth:', document.documentElement.clientWidth);
      
      if (viewportWidth <= 500) {
console.log('📱 Resize: Switching to mobile images');
        switchToMobileImages();
      } else if (viewportWidth <= 1024) {
console.log('📱 Resize: Switching to tablet images');
        switchToTabletImages();
      } else {
console.log('💻 Resize: Switching to desktop images');
        switchToDesktopImages();
      }
    }, 100);
  });
  
  // Make functions available globally for debugging
  window.debugMobileImages = switchToMobileImages;
  window.debugTabletImages = switchToTabletImages;
  window.debugDesktopImages = switchToDesktopImages;
  
  // Force desktop images for testing
  window.forceDesktopImages = function() {
    console.log('🔧 Force desktop images called');
    switchToDesktopImages();
  };
  
  // Force mobile images for testing
  window.forceMobileImages = function() {
    console.log('🔧 Force mobile images called');
    switchToMobileImages();
  };
  
console.log('🔧 Debug functions available: debugMobileImages(), debugTabletImages(), debugDesktopImages(), forceDesktopImages(), forceMobileImages()');
});
