/**
 * Product Lines Background Debug Utility
 * Helps verify responsive background switching
 */

(function() {
  'use strict';
  
  // Create debug panel
  function createDebugPanel() {
    const panel = document.createElement('div');
    panel.id = 'product-lines-debug';
    panel.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 15px;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      z-index: 99999;
      min-width: 350px;
      max-width: 400px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    `;
    
    panel.innerHTML = `
      <div style="margin-bottom: 10px; font-weight: bold; border-bottom: 1px solid #666; padding-bottom: 5px;">
        🔍 Product Lines Debug
      </div>
      <div id="pl-debug-viewport">Viewport: --</div>
      <div id="pl-debug-mode">Expected Mode: --</div>
      <div id="pl-debug-cards" style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
        <div style="font-weight: bold; margin-bottom: 5px;">Card Backgrounds:</div>
      </div>
      <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
        <button onclick="testProductLinesBreakpoint(801)" style="margin: 2px; padding: 4px 8px;">801px</button>
        <button onclick="testProductLinesBreakpoint(800)" style="margin: 2px; padding: 4px 8px;">800px</button>
        <button onclick="testProductLinesBreakpoint(768)" style="margin: 2px; padding: 4px 8px;">768px</button>
        <button onclick="testProductLinesBreakpoint(480)" style="margin: 2px; padding: 4px 8px;">480px</button>
        <button onclick="testProductLinesBreakpoint(375)" style="margin: 2px; padding: 4px 8px;">375px</button>
      </div>
      <div style="margin-top: 10px;">
        <button onclick="forceProductLinesUpdate()" style="background: #4CAF50; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-right: 5px;">Force Update</button>
        <button onclick="document.getElementById('product-lines-debug').remove()" style="background: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">Close</button>
      </div>
    `;
    
    document.body.appendChild(panel);
  }
  
  // Update debug info
  function updateDebugInfo() {
    const viewport = window.innerWidth;
    const expectedMode = viewport <= 480 ? 'mobile' : viewport <= 800 ? 'tablet' : 'desktop';
    
    // Update viewport info
    document.getElementById('pl-debug-viewport').innerHTML = 
      `Viewport: <strong style="color: #4CAF50">${viewport}px</strong>`;
    
    document.getElementById('pl-debug-mode').innerHTML = 
      `Expected Mode: <strong style="color: #FF9800">${expectedMode}</strong>`;
    
    // Check each card
    const cards = document.querySelectorAll('.product-card');
    const cardsDiv = document.getElementById('pl-debug-cards');
    let cardsHTML = '<div style="font-weight: bold; margin-bottom: 5px;">Card Backgrounds:</div>';
    
    cards.forEach((card, index) => {
      const cardName = card.classList.contains('delight-card') ? 'DELIGHT' :
                      card.classList.contains('help-card') ? 'HELP' :
                      card.classList.contains('rescue-card') ? 'RESCUE' : 'Unknown';
      
      const currentMode = card.getAttribute('data-current-mode') || card.getAttribute('data-active-mode') || 'not set';
      const bgImage = window.getComputedStyle(card).backgroundImage;
      
      // Check if background contains expected keywords
      let bgStatus = 'unknown';
      if (bgImage && bgImage !== 'none') {
        if (expectedMode === 'tablet' && card.getAttribute('data-tablet-bg')) {
          bgStatus = bgImage.includes('600x') ? 'correct' : 'wrong';
        } else if (expectedMode === 'mobile' && card.getAttribute('data-mobile-bg')) {
          bgStatus = bgImage.includes('400x') ? 'correct' : 'wrong';
        } else {
          bgStatus = bgImage.includes('800x') ? 'correct' : 'possibly wrong';
        }
      } else {
        bgStatus = 'no background';
      }
      
      const statusColor = bgStatus === 'correct' ? '#4CAF50' : 
                         bgStatus === 'wrong' ? '#f44336' : '#FF9800';
      
      cardsHTML += `
        <div style="margin-left: 10px; margin-bottom: 5px;">
          ${index + 1}. ${cardName}: 
          <span style="color: ${currentMode === expectedMode ? '#4CAF50' : '#f44336'}">
            ${currentMode}
          </span>
          - <span style="color: ${statusColor}">${bgStatus}</span>
        </div>
      `;
      
      // Also log detailed info
      console.log(`[Debug] Card ${cardName}:`, {
        expectedMode,
        currentMode,
        desktopBg: card.getAttribute('data-desktop-bg'),
        tabletBg: card.getAttribute('data-tablet-bg'),
        mobileBg: card.getAttribute('data-mobile-bg'),
        actualBg: bgImage
      });
    });
    
    cardsDiv.innerHTML = cardsHTML;
  }
  
  // Force update backgrounds
  window.forceProductLinesUpdate = function() {
    console.log('[Debug] Forcing background update...');
    
    // Try Web Component method
    const webComponent = document.querySelector('ayla-product-lines');
    if (webComponent && webComponent.updateBackgrounds) {
      console.log('[Debug] Using Web Component update');
      webComponent.updateBackgrounds();
    }
    
    // Also try global function if it exists
    if (window.updateProductLineBackgrounds) {
      console.log('[Debug] Using global function update');
      window.updateProductLineBackgrounds();
    }
    
    // Update debug info
    setTimeout(updateDebugInfo, 100);
  };
  
  // Test specific breakpoint
  window.testProductLinesBreakpoint = function(width) {
    const testWindow = window.open(window.location.href, '_blank', 
      `width=${width},height=900,left=100,top=100`);
    
    if (testWindow) {
      setTimeout(() => {
        testWindow.resizeTo(width, 900);
      }, 100);
    }
  };
  
  // Initialize
  function init() {
    createDebugPanel();
    updateDebugInfo();
    
    // Update on resize
    window.addEventListener('resize', updateDebugInfo);
    
    // Update periodically
    setInterval(updateDebugInfo, 2000);
    
    console.log('[Product Lines Debug] Initialized. Monitoring backgrounds...');
    console.log('[Product Lines Debug] Breakpoints: Mobile ≤480px, Tablet ≤800px, Desktop >800px');
  }
  
  // Start when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();