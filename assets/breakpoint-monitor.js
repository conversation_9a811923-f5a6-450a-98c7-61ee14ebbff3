/**
 * Breakpoint Monitor for Product Lines
 * Shows exactly when background images switch
 */

(function() {
  'use strict';
  
  let lastMode = '';
  
  function checkBreakpoint() {
    const viewport = window.innerWidth;
    const cards = document.querySelectorAll('.product-card');
    
    let currentMode = '';
    if (viewport <= 480) {
      currentMode = 'mobile';
    } else if (viewport <= 800) {
      currentMode = 'tablet';
    } else {
      currentMode = 'desktop';
    }
    
    // Check if mode changed
    if (currentMode !== lastMode) {
      console.log(`🔄 BREAKPOINT CROSSED at ${viewport}px: ${lastMode || 'initial'} → ${currentMode}`);
      
      // Check what backgrounds are actually showing
      cards.forEach((card, index) => {
        const bgImage = window.getComputedStyle(card).backgroundImage;
        const tabletBg = card.getAttribute('data-tablet-bg');
        
        if (currentMode === 'tablet') {
          if (tabletBg && bgImage.includes('600x')) {
            console.log(`✅ Card ${index + 1}: Tablet image applied`);
          } else if (tabletBg) {
            console.log(`❌ Card ${index + 1}: Tablet image NOT applied (but exists)`);
          } else {
            console.log(`⚠️ Card ${index + 1}: No tablet image set`);
          }
        }
      });
      
      lastMode = currentMode;
    }
    
    // Update display
    if (!document.getElementById('breakpoint-monitor')) {
      const monitor = document.createElement('div');
      monitor.id = 'breakpoint-monitor';
      monitor.style.cssText = `
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-family: monospace;
        font-size: 14px;
        z-index: 99999;
      `;
      document.body.appendChild(monitor);
    }
    
    const monitor = document.getElementById('breakpoint-monitor');
    const color = currentMode === 'desktop' ? '#4CAF50' : 
                  currentMode === 'tablet' ? '#FF9800' : '#2196F3';
    
    monitor.innerHTML = `
      Width: <strong>${viewport}px</strong> | 
      Mode: <strong style="color: ${color}">${currentMode.toUpperCase()}</strong> | 
      Breakpoint: ${viewport <= 800 ? '≤800px (tablet/mobile)' : '>800px (desktop)'}
    `;
  }
  
  // Initial check
  checkBreakpoint();
  
  // Monitor on resize
  window.addEventListener('resize', checkBreakpoint);
  
  console.log('📊 Breakpoint Monitor Active - Resize to see when images switch');
  console.log('Expected: Tablet images at 800px and below, Desktop above 800px');
})();