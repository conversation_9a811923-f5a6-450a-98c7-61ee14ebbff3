/**
 * Payment Icons CSS
 * Creates placeholder payment method icons using CSS
 */

.payment-method {
  position: relative;
}

.payment-method::after {
  content: '';
  display: block;
  width: 35px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* Visa */
.payment-method:nth-child(1)::after {
  content: 'VISA';
  background: #1a1f71;
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  line-height: 20px;
  border-radius: 3px;
}

/* Mastercard */
.payment-method:nth-child(2)::after {
  content: 'MC';
  background: #eb001b;
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  line-height: 20px;
  border-radius: 3px;
}

/* PayPal */
.payment-method:nth-child(3)::after {
  content: 'PayPal';
  background: #0070ba;
  color: white;
  font-size: 8px;
  font-weight: bold;
  text-align: center;
  line-height: 20px;
  border-radius: 3px;
}

/* BLIK */
.payment-method:nth-child(4)::after {
  content: 'BLIK';
  background: #ffa500;
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  line-height: 20px;
  border-radius: 3px;
}

/* Alternative if actual images are available */
.payment-method img {
  max-width: 35px;
  max-height: 20px;
  object-fit: contain;
}

/* Hide pseudo element if image exists */
.payment-method:has(img)::after {
  display: none;
}
